# Meena Android App - Mock-Only Backend System

## 🎯 Overview

This branch of the Meena Android app uses **exclusively mock backend implementation** for simplified development and testing. All real backend components have been removed to streamline the development workflow and eliminate the complexity of managing dual backend modes.

## ✨ Key Features

### 🔄 Complete Backend Simulation
- **Full API Coverage**: All backend endpoints are mocked with realistic responses
- **Contract Compliance**: Responses match the documented API specifications
- **Error Simulation**: Realistic error scenarios and network conditions
- **Authentication**: Complete auth flow including registration, login, and recovery

### 🤖 AI-Powered Chat Experience
- **6 Unique Personas**: Different AI personalities for varied conversation experiences
- **Context-Aware Responses**: AI considers conversation history and message context
- **Realistic Timing**: Simulated typing indicators and natural response delays
- **Multi-Chat Support**: Works across one-to-one, group, and channel conversations

### 💾 Persistent Data Management
- **Survives App Restarts**: Mock data persists across app sessions
- **Realistic Scale**: Pre-generated users, contacts, chats, and message history
- **Easy Reset**: Developer tools for clearing and regenerating data
- **Configurable**: Adjustable data scale and behavior

### ⚙️ Simplified Configuration
- **Mock-Only Mode**: No configuration needed - always uses mock backend
- **Feature Flags**: Control which features are enabled
- **Developer Menu**: In-app tools for managing mock behavior
- **Streamlined Build**: Single build configuration without backend switching complexity

## 🚀 Quick Start

### Simple Build and Run

```bash
# Build and install the app (always uses mock backend)
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### IDE Run Configuration

1. Open Android Studio
2. Run the app directly - no configuration needed
3. The app automatically uses mock backend

## 🏗️ Architecture

### Mock System Components

```
mock/
├── api/                    # Mock API implementations
│   ├── MockAuthApi.kt     # Authentication endpoints
│   ├── MockChatApi.kt     # Chat and messaging
│   ├── MockContactApi.kt  # Contact management
│   └── MockUserApi.kt     # User profile operations
├── ai/                    # AI chat simulation
│   ├── AIPersona.kt       # Personality definitions
│   └── AIResponseGenerator.kt # Response generation
├── data/                  # Data generation
│   └── MockDataGenerator.kt # Realistic test data
└── storage/               # Data persistence
    └── MockDataStorage.kt # Persistent storage
```

### Simplified Configuration System

```
config/
└── AppConfig.kt           # Simplified configuration (mock-only)
    ├── MockConfig         # Mock-specific settings
    └── DevConfig          # Development tools
```

## 🤖 AI Chat Personas

### Available Personalities

1. **Friendly Assistant** 🤝
   - Helpful and warm personality
   - Always ready to assist
   - High friendliness and helpfulness

2. **Tech Enthusiast** 💻
   - Passionate about technology
   - Discusses innovation and coding
   - High enthusiasm for tech topics

3. **Casual Friend** 😎
   - Laid-back and easy-going
   - Informal communication style
   - Low formality, high humor

4. **Professional Colleague** 👔
   - Business-focused interactions
   - Formal communication style
   - High formality and helpfulness

5. **Humorous Buddy** 😄
   - Always ready with jokes
   - Funny and entertaining responses
   - Maximum humor settings

6. **Supportive Mentor** 🎓
   - Wise and encouraging
   - Offers guidance and support
   - High helpfulness and wisdom

### AI Response Features

- **Context Awareness**: Considers conversation history
- **Trigger Recognition**: Responds to greetings, questions, emotions
- **Personality Traits**: Adjustable friendliness, formality, humor
- **Natural Timing**: Realistic response delays and typing indicators

## 📊 Pre-configured Test Data

### Generated Content

- **20 Realistic Users** with varied profiles and activity levels
- **15 Contacts** with different relationship types and interactions
- **10 Active Conversations**:
  - 8 one-to-one chats with AI responses
  - 2 group chats with multiple participants
  - 1 channel for broadcast messaging
- **50+ Messages** per conversation with varied content types
- **Media Attachments** including images, videos, and files
- **Message Reactions** and reply threads

### User Characteristics

- Verified and unverified accounts
- Gold members and regular users
- Different activity patterns and online status
- Varied profile information and avatars
- Realistic interaction history

## 🛠️ Developer Tools

### In-App Developer Menu

Access via Settings → Developer Menu (when enabled):

- **Data Management**: Reset, clear, or regenerate mock data
- **AI Configuration**: Control AI responses and behavior
- **Feature Flags**: Enable/disable specific features
- **Statistics**: View current data counts and status
- **Debug Actions**: Generate test data, simulate events

### Configuration Options

```kotlin
// Enable developer menu
object DevConfig {
    const val ENABLE_DEV_MENU = true
    const val ENABLE_MOCK_LOGGING = true
    const val RESET_MOCK_DATA_ON_START = false
}

// Adjust mock behavior
object MockConfig {
    const val NETWORK_DELAY_MIN = 200L
    const val NETWORK_DELAY_MAX = 1000L
    const val AI_RESPONSE_DELAY_MIN = 1000L
    const val AI_RESPONSE_DELAY_MAX = 3000L
    const val DEFAULT_MOCK_USERS_COUNT = 20
}
```

## 🧪 Testing Scenarios

### Comprehensive Test Coverage

1. **Authentication Flow**
   - Registration with various inputs
   - Login with different credentials
   - Account recovery process
   - 2FA simulation

2. **Contact Management**
   - Adding and removing contacts
   - Blocking and unblocking users
   - Contact search and discovery
   - Favorite contacts management

3. **Chat Features**
   - One-to-one conversations with AI
   - Group chat creation and management
   - Channel subscription and messaging
   - Message editing, deletion, and reactions

4. **Media Handling**
   - Image and video sharing
   - File attachments
   - Media download and playback
   - Thumbnail generation

5. **Real-time Features**
   - Typing indicators
   - Read receipts
   - Online status
   - Message delivery status

## 📚 Documentation

### For Developers
- [Mock Backend Guide](docs/MOCK_BACKEND_GUIDE.md) - Comprehensive development guide
- [API Documentation](docs/apis_definitions.md) - Complete API specifications
- [Architecture Guide](docs/architecture/README.md) - System architecture overview

### For Testers
- [Tester Setup Guide](docs/TESTER_SETUP_GUIDE.md) - Quick setup and testing scenarios
- [Test Scenarios](docs/TESTER_SETUP_GUIDE.md#test-scenarios) - Comprehensive testing workflows

## 🔧 Advanced Configuration

### Custom AI Personas

```kotlin
val customPersona = AIPersona(
    id = "custom_persona",
    name = "Custom Assistant",
    description = "Your custom AI personality",
    responsePatterns = listOf(
        ResponsePattern(
            trigger = MessageTrigger.Greeting,
            responses = listOf("Hello!", "Hi there!")
        )
    ),
    personality = PersonalityTraits(
        friendliness = 0.8f,
        formality = 0.3f,
        humor = 0.6f
    )
)
```

### Network Simulation

```kotlin
object MockConfig {
    const val SIMULATE_NETWORK_ERRORS = true
    const val ERROR_RATE_PERCENTAGE = 5 // 5% error rate
    const val SLOW_NETWORK_DELAY = 5000L // Simulate slow network
}
```

## �️ Removed Components

This mock-only branch has removed the following real backend components to simplify development:

### Removed Files
- `AuthInterceptor.kt` - Real authentication headers
- `TokenAuthenticator.kt` - Real token refresh logic
- `ApiClient.kt` - Real API client configuration
- `WebSocketClient.kt` - Real WebSocket implementation
- `WebSocketService.kt` - Real WebSocket service
- `WebSocketMessage.kt` - WebSocket message models
- `WebSocketMessageSerializer.kt` - WebSocket serialization
- `WebSocketModule.kt` - WebSocket dependency injection

### Simplified Components
- `NetworkModule.kt` - Now only provides mock implementations
- `AppConfig.kt` - Simplified to always use mock backend
- `build.gradle.kts` - Removed build variants and backend switching
- ViewModels and Repositories - Removed WebSocket dependencies

### Features Not Available in Mock Mode
- Real-time WebSocket communication
- Typing indicators
- Live message status updates
- Real server authentication
- Network-based file uploads/downloads

## �🚨 Troubleshooting

### Common Issues

1. **Mock data not loading**
   - Check MockDataStorage initialization
   - Clear app data and restart
   - Verify app permissions

2. **AI responses not working**
   - Ensure `ENABLE_AI_CHAT_RESPONSES` is true
   - Verify chat type is one-to-one
   - Check response delay settings

3. **Build issues**
   - Clean and rebuild project
   - Sync project with Gradle files
   - Check dependencies

### Debug Logging

```kotlin
object DevConfig {
    const val ENABLE_MOCK_LOGGING = true
    const val ENABLE_API_LOGGING = true
}
```

## 🤝 Contributing

When adding new mock features:

1. Follow existing patterns and structure
2. Add comprehensive documentation
3. Include realistic test data
4. Test with various configurations
5. Update relevant documentation

## 📄 License

This project is part of the Meena social media application. See the main project license for details.

---

**Ready to start testing?** Check out the [Tester Setup Guide](docs/TESTER_SETUP_GUIDE.md) for quick setup instructions!
