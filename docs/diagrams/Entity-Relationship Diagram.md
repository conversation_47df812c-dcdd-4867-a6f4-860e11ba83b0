Okay, generating a complete, visually accurate Entity-Relationship Diagram (ERD) directly in Mermaid syntax for a schema this complex can become quite unwieldy and hard to read due to layout limitations. Mermaid ERDs are best for simpler schemas.

However, I can provide the **Mermaid ERD syntax** representing the core tables and their most important relationships. For a truly *complete and readable* diagram, I strongly recommend using a dedicated ERD tool (like dbdiagram.io, Lucidchart, Draw.io, MySQL Workbench (can connect to Postgres), DBeaver, DataGrip) where you can manually arrange tables and relationships for optimal clarity.

This Mermaid code focuses on showing the tables and their primary/foreign key relationships. Cardinality symbols (`|o`, `||`, `}o`, `}|`, `o{`, `|{`, `zero..one`, `one..one`, `zero..many`, `one..many`) are used to denote relationships.

**Mermaid ERD Syntax (Conceptual):**

```mermaid
erDiagram
    USERS ||--o{ CONTACTS : "has/is contact"
    USERS ||--o{ FOLLOWERS : "follows"
    USERS ||--o{ FOLLOWERS : "is followed by"
    USERS ||--o{ PAYMENTS : "makes"
    USERS ||--o| SUBSCRIPTIONS : "has"
    USERS ||--o{ GROUPS : "creates"
    USERS ||--o{ CHANNELS : "creates"
    USERS ||--o{ MESSAGES : "sends"
    USERS ||--o{ MEDIA : "uploads"
    USERS ||--o{ STORIES : "creates"
    USERS ||--o{ STORY_VIEWS : "views"
    USERS ||--o{ POSTS : "creates" (If used)
    USERS ||--o{ POST_ENGAGEMENTS : "engages with" (If used)
    USERS ||--o{ REPORTS : "submits"
    USERS ||--o{ REPORTS : "reviews (admin)"
    USERS ||--o{ BANNED_ENTITIES : "bans (admin)"
    USERS ||--o{ CALL_LOGS : "initiates (caller)"
    USERS ||--o{ CALL_LOGS : "receives (callee)"
    USERS ||--o| MONTHLY_CALL_USAGE : "has usage for"
    USERS ||--o{ SUPPORT_TICKETS : "creates"
    USERS ||--o{ SUPPORT_MESSAGES : "sends"
    USERS ||--o| USER_TOTP_SECRETS : "has TOTP"
    USERS ||--o{ USER_CONVERSATIONS : "has list for"
    USERS ||--o{ CHAT_PARTICIPANTS : "participates in"
    USERS ||--o{ GROUP_MEMBERS : "is member of"
    USERS ||--o{ CHANNEL_SUBSCRIBERS : "subscribes to"

    CONTACTS }|--|| USERS : "is contact of"
    FOLLOWERS }|--|| USERS : "follower"
    FOLLOWERS }|--|| USERS : "followed"

    CHATS ||--o{ CHAT_PARTICIPANTS : "has"
    CHATS ||--o{ MESSAGES : "contains"

    GROUPS ||--o{ GROUP_MEMBERS : "has"
    GROUPS ||--o{ MESSAGES : "contains"
    GROUPS ||--o| CHANNELS : "is linked by"

    CHANNELS ||--o{ CHANNEL_SUBSCRIBERS : "has"
    CHANNELS ||--o{ MESSAGES : "contains"

    MESSAGES }o--|| CHATS : "belongs to"
    MESSAGES }o--|| GROUPS : "belongs to"
    MESSAGES }o--|| CHANNELS : "belongs to"
    MESSAGES }o--o| MESSAGES : "replies to"
    MESSAGES }o--o| MESSAGES : "is forwarded from"
    MESSAGES ||--o{ MESSAGE_MEDIA : "includes"
    MESSAGES ||--o{ MESSAGE_STATUS : "has status for"
    MESSAGES ||--o{ REPORTS : "is reported"

    MEDIA ||--o{ MESSAGE_MEDIA : "is included in"
    MEDIA ||--o{ POST_MEDIA : "is included in" (If used)
    MEDIA ||--o{ STORIES : "is base for"
    MEDIA ||--o{ STORY_ELEMENTS : "is overlay/reaction in"
    MEDIA ||--o{ REPORTS : "is reported"

    MESSAGE_MEDIA }|--|| MESSAGES : "link"
    MESSAGE_MEDIA }|--|| MEDIA : "link"

    MESSAGE_STATUS }|--|| MESSAGES : "status of"
    MESSAGE_STATUS }|--|| USERS : "status for"

    POSTS ||--o{ POST_MEDIA : "includes" (If used)
    POSTS ||--o{ POST_HASHTAGS : "uses" (If used)
    POSTS ||--o{ POST_ENGAGEMENTS : "has" (If used)
    POSTS }o--o| POSTS : "replies to" (If used)
    POSTS ||--o{ REPORTS : "is reported" (If used)

    POST_MEDIA }|--|| POSTS : "link" (If used)
    POST_MEDIA }|--|| MEDIA : "link" (If used)

    POST_HASHTAGS }|--|| POSTS : "link" (If used)
    POST_HASHTAGS }|--|| HASHTAGS : "link" (If used)

    POST_ENGAGEMENTS }|--|| POSTS : "engagement on" (If used)
    POST_ENGAGEMENTS }|--|| USERS : "engagement by" (If used)

    HASHTAGS ||--o{ POST_HASHTAGS : "is used in" (If used)

    STORIES ||--o{ STORY_ELEMENTS : "contains"
    STORIES ||--o{ STORY_VIEWS : "is viewed by"
    STORIES ||--o{ REPORTS : "is reported"

    STORY_ELEMENTS }|--|| STORIES : "element of"
    STORY_ELEMENTS }o--o| MEDIA : "uses media"

    STORY_VIEWS }|--|| STORIES : "view of"
    STORY_VIEWS }|--|| USERS : "viewer"

    PAYMENTS ||--o| SUBSCRIPTIONS : "relates to"

    REPORTS }|--|| USERS : "reported by"

    SUPPORT_TICKETS ||--o{ SUPPORT_MESSAGES : "contains"

    SUPPORT_MESSAGES }|--|| SUPPORT_TICKETS : "message in"
    SUPPORT_MESSAGES }o--o| USERS : "sent by"

    USER_CONVERSATIONS }|--|| USERS : "list for"


    %% --- Table Definitions (Simplified - Showing Key Columns/FKs) ---

    USERS {
        UUID user_id PK
        VARCHAR user_handle UK
        VARCHAR display_name
        VARCHAR profile_picture_url
        TEXT bio
        VARCHAR phone_number UK
        VARCHAR email UK
        VARCHAR password_hash
        VARCHAR recovery_phrase_hash
        VARCHAR recovery_pin_hash
        VARCHAR remote_wipe_pin_hash
        subscription_tier_enum subscription_tier
        verification_status_enum verification_status
        BOOLEAN is_active
        TIMESTAMP created_at
        TIMESTAMP last_seen_at
        TIMESTAMP scheduled_deletion_at
        JSONB settings
    }
    USER_TOTP_SECRETS {
        UUID user_id PK, FK
        VARCHAR secret_key
        BOOLEAN is_active
    }
    CONTACTS {
        UUID user_id PK, FK
        UUID contact_user_id PK, FK
        VARCHAR display_name
        BOOLEAN is_blocked
    }
    FOLLOWERS {
        UUID follower_id PK, FK
        UUID followed_id PK, FK
    }
    PAYMENTS {
        UUID payment_id PK
        UUID user_id FK
        DECIMAL amount
        VARCHAR currency
        payment_status_enum status
        payment_purpose_enum purpose
        UUID related_entity_id NULL
        VARCHAR processor_transaction_id UK
    }
    SUBSCRIPTIONS {
        UUID subscription_id PK
        UUID user_id FK
        subscription_tier_enum tier
        VARCHAR status
        TIMESTAMP start_date
        TIMESTAMP end_date NULL
        UUID last_payment_id FK NULL
        VARCHAR processor_subscription_id UK NULL
    }
    CHATS {
        UUID chat_id PK
        BOOLEAN is_encrypted
        JSONB encryption_metadata NULL
    }
    CHAT_PARTICIPANTS {
        UUID chat_id PK, FK
        UUID user_id PK, FK
    }
    GROUPS {
        UUID group_id PK
        UUID creator_id FK NULL
        VARCHAR name
        privacy_type_enum privacy_type
        VARCHAR invite_link_token UK NULL
        moderation_status_enum moderation_status
        UUID linked_channel_id FK NULL UK
    }
    GROUP_MEMBERS {
        UUID group_id PK, FK
        UUID user_id PK, FK
        VARCHAR role
        UUID invited_by FK NULL
    }
    CHANNELS {
        UUID channel_id PK
        UUID creator_id FK NULL
        VARCHAR name
        privacy_type_enum privacy_type
        VARCHAR invite_link_token UK NULL
        INT subscriber_count
        moderation_status_enum moderation_status
        UUID linked_group_id FK NULL UK
    }
    CHANNEL_SUBSCRIBERS {
        UUID channel_id PK, FK
        UUID user_id PK, FK
        VARCHAR role
    }
    MEDIA {
        UUID media_id PK
        UUID uploader_user_id FK NULL
        VARCHAR media_type
        VARCHAR file_url
        moderation_status_enum moderation_status
        JSONB scan_results NULL
        JSONB metadata NULL
    }
    MESSAGES {
        UUID message_id PK
        UUID chat_id FK NULL
        UUID group_id FK NULL
        UUID channel_id FK NULL
        UUID sender_id FK NULL
        TEXT text_content NULL
        TIMESTAMP sent_at
        TIMESTAMP edited_at NULL
        BOOLEAN is_deleted
        UUID reply_to_message_id FK NULL
        UUID forwarded_from_message_id FK NULL
        moderation_status_enum moderation_status
        JSONB scan_results NULL
        JSONB metadata NULL
    }
    MESSAGE_MEDIA {
        UUID message_id PK, FK
        UUID media_id PK, FK
    }
    MESSAGE_STATUS {
        UUID message_id PK, FK
        UUID user_id PK, FK
        VARCHAR status PK
    }
    STORIES {
        UUID story_id PK
        UUID user_id FK
        UUID base_media_id FK
        TEXT caption NULL
        TIMESTAMP expires_at
    }
    STORY_ELEMENTS {
        UUID element_id PK
        UUID story_id FK
        story_element_type_enum element_type
        JSONB element_data
    }
    STORY_VIEWS {
        UUID story_id PK, FK
        UUID viewer_id PK, FK
    }
    REPORTS {
        UUID report_id PK
        UUID reporter_user_id FK
        reportable_entity_type_enum reported_entity_type
        UUID reported_entity_id
        report_reason_enum reason
        report_status_enum status
        UUID reviewed_by_admin_id FK NULL
    }
    BANNED_ENTITIES {
        reportable_entity_type_enum entity_type PK
        UUID entity_id PK
        UUID banned_by_admin_id FK NULL
        TIMESTAMP expires_at NULL
    }
    CALL_LOGS {
        UUID call_log_id PK
        UUID caller_id FK
        UUID callee_id FK
        TIMESTAMP start_time
        TIMESTAMP end_time NULL
        INT duration_seconds NULL
        VARCHAR call_type
        VARCHAR status
    }
    MONTHLY_CALL_USAGE {
        UUID user_id PK, FK
        DATE usage_month PK
        INT total_duration_seconds
    }
    USER_CONVERSATIONS {
        UUID user_id PK, FK
        UUID conversation_id PK
        VARCHAR conversation_type
        privacy_type_enum privacy_type NULL
        TIMESTAMP last_activity_timestamp NULL
        INT unread_count
    }
    %% -- Add Posts, Hashtags, Support Tables if primary focus --
```

**Key Points & Recommendations for Actual Diagramming:**

1.  **Tool Choice:** Use a dedicated tool (dbdiagram.io is free and text-based like Mermaid but produces better layouts; Lucidchart/Draw.io are graphical).
2.  **Layout is Key:** Arrange tables logically (e.g., central `USERS` table, related tables nearby). Minimize crossing relationship lines.
3.  **Focus on Relationships:** Clearly show the lines connecting Foreign Keys (FK) to Primary Keys (PK). Ensure cardinality (one-to-many, many-to-many via junction tables) is correct.
4.  **Include Data Types:** The full ERD should ideally include data types for columns.
5.  **Highlight Key Tables:** Maybe use color or bolding for central tables like `USERS`, `MESSAGES`, `GROUPS`, `CHANNELS`.
6.  **Junction Tables:** Clearly show many-to-many relationships implemented via junction tables (e.g., `GROUP_MEMBERS`, `MESSAGE_MEDIA`).
7.  **Modularity (Optional):** Some tools allow grouping tables by functional area (Auth, Messaging, Moderation), which can improve readability for very large schemas.

This Mermaid code gives you the raw relationships and key fields. Transferring this information into a proper ERD tool will yield the complete, readable diagram needed for development.