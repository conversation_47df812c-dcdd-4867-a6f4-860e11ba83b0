Okay, here is the Backend Component Diagram (C4 Level 3) focusing specifically on the internals of the main `Backend API / WebSocket Service (Go Application)` container identified in the previous diagram.

**Description:**

This diagram decomposes the main Go backend application into its logical, high-level components (modules or packages in your Go codebase). It shows the primary responsibilities of each component and, crucially, the dependencies and interactions *between them*. It helps understand the internal structure and coupling within the backend code.

*   **Core Components (Representing Go Packages/Modules):**
    *   **API Handlers / Router (Gin/Echo):** Receives HTTP requests from the API Gateway, parses them, and delegates processing to the appropriate Service/Use Case Handler.
    *   **WebSocket Handler:** Handles WebSocket connection lifecycle (upgrade, authentication, close), receives messages from clients, and delegates processing. Uses the `WebSocket Manager Component`.
    *   **Auth & User Service:** Handles user registration, login, token generation/validation, password/secret management, recovery flows, basic user data access.
    *   **Profile & Settings Service:** Manages detailed user profile updates and retrieval, handles complex settings logic. Depends on `AuthService`.
    *   **Contact & Relationship Service:** Manages contacts, blocking, potentially follower logic. Depends on `AuthService`.
    *   **Messaging Service:** Core logic for sending/receiving/fetching messages, managing read status. Depends on `AuthService`, `MediaService`, interacts heavily with `WebSocket Manager Component`, `Notification Service`, and queues jobs via `Job Queue Client`.
    *   **Group Service:** Logic for group creation, metadata, membership, permissions, invite links. Depends on `AuthService`, `PaymentService`, `ModerationService`, interacts with `WebSocket Manager Component`, `Notification Service`, and queues jobs.
    *   **Channel Service:** Similar to GroupService but for channels, including linking to groups. Dependencies similar to GroupService.
    *   **Story Service:** Logic for story creation (incl. elements), feed generation, viewing. Depends on `AuthService`, `MediaService`, `ContactService` (for feed visibility).
    *   **Call Service:** Handles call setup signaling coordination, logging, limit checks. Depends on `AuthService`, `SubscriptionService`, interacts with `WebSocket Manager Component`.
    *   **Payment & Subscription Service:** Handles interaction logic with the external Payment Gateway, manages subscription lifecycle, checks payment status. Depends on `AuthService`.
    *   **Moderation Service:** Handles report processing, interacts with external AI services, updates moderation status on content/entities, manages banned entities list. Depends on `AuthService`.
    *   **Media Service:** Handles generating upload URLs, processing upload completions (optional), validating media access. Depends on `AuthService`, interacts with `ModerationService`.
    *   **Notification Service:** Formats and triggers push notifications and potentially real-time notifications via the WebSocket Manager.
    *   **WebSocket Manager Component:** Internal component (likely a package) responsible for managing active connections, mapping connections to users, handling room/topic subscriptions, and broadcasting messages (using Redis Pub/Sub). Used by `WebSocket Handler`, `MessagingService`, `GroupService`, `ChannelService`, `CallService`, `NotificationService`.
    *   **Job Queue Client (Asynq Client):** Interface used by various services to enqueue background jobs onto Redis.
    *   **Database Client (pgx Pool/Wrapper):** Internal component abstracting direct database interactions using `pgx`. Used by nearly all services needing persistent data.
    *   **Redis Client (go-redis Client/Wrapper):** Internal component abstracting direct Redis interactions. Used by services needing cache, Pub/Sub, or other Redis features.
    *   **External API Client:** Internal component(s) abstracting interactions with external services (Payments, AI, Push, Storage).

*   **Key Interactions:** Arrows indicate dependencies or calls *within* the Go application. For example, `GroupService --> PaymentService` means the Group Service code calls functions/methods provided by the Payment Service component (likely to check limits or initiate a paid creation flow).

**Mermaid Syntax:**

```mermaid
graph TD
    %% Define the boundary of the component diagram
    subgraph "Backend API / WebSocket Service (Go Application)"
        direction LR

        %% Core Request Handlers
        ApiHandlers[API Handlers / Router (Gin/Echo)]
        WsHandler[WebSocket Handler]

        %% Service Layer Components (Logical Modules/Packages)
        AuthSvc[Auth & User Service]
        ProfileSvc[Profile & Settings Service]
        ContactSvc[Contact & Relationship Service]
        MsgSvc[Messaging Service]
        GroupSvc[Group Service]
        ChannelSvc[Channel Service]
        StorySvc[Story Service]
        CallSvc[Call Service]
        PaymentSvc[Payment & Subscription Service]
        ModSvc[Moderation Service]
        MediaSvc[Media Service]
        NotifSvc[Notification Service]

        %% Infrastructure / Shared Components within the Service
        WsMgr[WebSocket Manager Component]
        JobClient[Job Queue Client (Asynq)]
        DbClient[Database Client (pgx)]
        RedisClient[Redis Client (go-redis)]
        ExtApiClient[External API Client Wrapper]

        %% Handler to Service Dependencies
        ApiHandlers --> AuthSvc
        ApiHandlers --> ProfileSvc
        ApiHandlers --> ContactSvc
        ApiHandlers --> MsgSvc
        ApiHandlers --> GroupSvc
        ApiHandlers --> ChannelSvc
        ApiHandlers --> StorySvc
        ApiHandlers --> CallSvc
        ApiHandlers --> PaymentSvc
        ApiHandlers --> ModSvc
        ApiHandlers --> MediaSvc
        ApiHandlers --> NotifSvc %% Maybe for admin actions?

        WsHandler --> WsMgr
        WsHandler --> AuthSvc %% Auth WebSocket connection

        %% Service to Service Dependencies
        ProfileSvc --> AuthSvc
        ContactSvc --> AuthSvc
        MsgSvc --> AuthSvc
        MsgSvc --> MediaSvc
        MsgSvc --> WsMgr
        MsgSvc --> NotifSvc
        MsgSvc --> JobClient -->|Enqueue: Update UserConv| JobQueue[(Redis Job Queue)]
        GroupSvc --> AuthSvc
        GroupSvc --> PaymentSvc
        GroupSvc --> ModSvc
        GroupSvc --> WsMgr
        GroupSvc --> NotifSvc
        GroupSvc --> JobClient -->|Enqueue: Update UserConv| JobQueue
        ChannelSvc --> AuthSvc
        ChannelSvc --> PaymentSvc
        ChannelSvc --> ModSvc
        ChannelSvc --> GroupSvc %% For linking info
        ChannelSvc --> WsMgr
        ChannelSvc --> NotifSvc
        ChannelSvc --> JobClient -->|Enqueue: Update UserConv| JobQueue
        StorySvc --> AuthSvc
        StorySvc --> MediaSvc
        StorySvc --> ContactSvc %% For feed generation/visibility
        CallSvc --> AuthSvc
        CallSvc --> PaymentSvc %% For limit checks via Subscription logic
        CallSvc --> WsMgr %% For signaling relay
        PaymentSvc --> AuthSvc
        ModSvc --> AuthSvc
        MediaSvc --> AuthSvc
        MediaSvc --> ModSvc %% Trigger moderation scan

        %% Service to Infrastructure/Shared Component Dependencies
        AuthSvc --> DbClient
        AuthSvc --> RedisClient %% For sessions?
        ProfileSvc --> DbClient
        ContactSvc --> DbClient
        MsgSvc --> DbClient
        MsgSvc --> RedisClient %% Potentially for rate limiting/PubSub via WsMgr
        GroupSvc --> DbClient
        ChannelSvc --> DbClient
        StorySvc --> DbClient
        CallSvc --> DbClient %% Logging calls
        PaymentSvc --> DbClient
        PaymentSvc --> ExtApiClient -->|Payment Gateway| ExternalServices[(External Services)]
        ModSvc --> DbClient
        ModSvc --> ExtApiClient -->|AI Moderation| ExternalServices
        MediaSvc --> DbClient
        MediaSvc --> ExtApiClient -->|Object Storage| ExternalServices
        NotifSvc --> ExtApiClient -->|Push Services| ExternalServices
        WsMgr --> RedisClient %% For PubSub / Connection State

        %% Infrastructure to External (Conceptual)
        DbClient --> Database[(PostgreSQL)]
        RedisClient --> RedisCache[(Redis)]
        JobClient --> JobQueue

    end

    %% Styling
    style WsMgr fill:#f9d7ff
    style DbClient fill:#e0e0e0
    style RedisClient fill:#ffe0e0
    style JobClient fill:#fff0e0
    style ExtApiClient fill:#e0f0ff
```

**How to Interpret:**

*   This focuses *only* on the Go backend service. Boxes represent logical code modules/packages.
*   Arrows show which modules call functions or depend on interfaces provided by other modules *within the same application*.
*   Infrastructure interaction components (`DbClient`, `RedisClient`, `JobClient`, `ExtApiClient`) abstract the low-level details of talking to the Database, Redis, Queue, and External APIs. Services use these clients.
*   The `WebSocket Manager Component` is highlighted as a crucial internal piece used by many services involved in real-time updates.
*   External systems like the Database, Redis, Job Queue, and External Service APIs are shown conceptually outside the main service boundary, representing what the internal clients interact with.

This component diagram provides a much clearer view for backend developers on how the different parts of their Go application should be structured and how they relate to each other, promoting modularity and maintainability.