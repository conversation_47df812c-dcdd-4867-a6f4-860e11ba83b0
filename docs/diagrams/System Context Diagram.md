**Description:**

This diagram shows the entire `MEENA` system as a central black box. It identifies the primary types of users (Actors) who interact with the system and the key external systems that `MEENA` depends on or interacts with.

*   **Actors:**
    *   **User (Mobile/Web):** The primary end-user interacting with the application via their device (phone or web browser) for messaging, calls, stories, groups, etc.
    *   **Admin User:** A privileged user interacting with specific backend administrative interfaces (likely separate or secured parts of the API) for tasks like content moderation review, user management, system configuration, etc.

*   **External Systems:**
    *   **Payment Gateway:** An external service (e.g., Stripe, PayPal) used to process payments for Gold Membership and paid features. `MEENA` sends payment requests and receives confirmation/status updates.
    *   **Push Notification Service (APNS/FCM):** External services provided by Apple (APNS) and Google (FCM) used to deliver push notifications to offline mobile devices. `MEENA` sends notification payloads to these services.
    *   **AI Moderation Service:** External AI services (e.g., Google Cloud Vision/Language, AWS Rekognition/Comprehend, third-party provider) used to scan text and media content for policy violations. `MEENA` sends content for analysis and receives results/scores.
    *   **Object Storage (R2/B2/S3):** External cloud storage used to store large binary files like images, videos, and documents uploaded by users. `MEENA` manages access (e.g., generating signed URLs) and stores metadata/references.
    *   **STUN/TURN Service:** External servers (can be self-hosted `coturn` or a managed service) required for WebRTC to establish peer-to-peer connections for audio/video calls, especially across different networks (NAT traversal). `MEENA` provides server addresses to clients during call setup.

**Mermaid Syntax:**

```mermaid
graph TD
    %% Define Actors (Users)
    actor User [User (Mobile/Web)]
    actor AdminUser [Admin User]

    %% Define the System Under Design
    subgraph MyAppSystem [MEENA System]
        direction LR
        SystemBox{MEENA Backend & Infrastructure}
    end

    %% Define External Systems
    Ext_Payment[(Payment Gateway e.g., Stripe)]
    Ext_Push[(Push Notification Service APNS/FCM)]
    Ext_AIMod[(AI Moderation Service e.g., Google Cloud AI)]
    Ext_ObjectStore[(Object Storage e.g., R2/B2)]
    Ext_STUN_TURN[(STUN/TURN Service e.g., Coturn)]

    %% Define Interactions
    User -- "Uses (Messaging, Calls, Stories, Groups, Payments)" --> SystemBox
    AdminUser -- "Administers (Moderation Review, Config)" --> SystemBox

    SystemBox -- "Processes Payments" --> Ext_Payment
    SystemBox -- "Sends Push Notifications" --> Ext_Push
    SystemBox -- "Requests Content Analysis" --> Ext_AIMod
    SystemBox -- "Stores/Retrieves Media Files" --> Ext_ObjectStore
    SystemBox -- "Provides Server Info for Calls" --> Ext_STUN_TURN

    %% Optional: Show interaction back from external systems if needed (e.g., webhooks)
    Ext_Payment -- "Payment Status Webhooks" --> SystemBox
    Ext_AIMod -- "Analysis Results" --> SystemBox

    %% Style Node for Clarity
    style MyAppSystem fill:#lightblue,stroke:#333,stroke-width:2px
```

**How to Interpret:**

*   The `User (Mobile/Web)` is the primary driver of most features.
*   The `Admin User` interacts for specific backend management tasks.
*   The `MEENA System` box represents *everything* you build and manage (Go backend, database, Redis, workers, etc.).
*   The arrows show the direction of primary interaction or data flow *between* your system and the external dependencies.
*   This diagram deliberately hides the internal complexity of `MEENA` to focus on its place within the larger ecosystem.