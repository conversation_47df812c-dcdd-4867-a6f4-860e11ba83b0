**Description:**

This diagram zooms *inside* the "MEENA System Boundary" defined in the Context Diagram. It shows the major deployable/runnable units (containers) that make up your application and how they interact with each other and with the key external systems identified previously.

*   **Containers within the `MEENA` System:**
    *   **Client App (Mobile: iOS/Android):** The native mobile application running on the user's device. Makes API calls (HTTPS) and establishes WebSocket connections (WSS) to the backend. Interacts with Push Notification services directly to receive notifications.
    *   **Client App (Web):** The web application running in the user's browser. Makes API calls (HTTPS) and establishes WebSocket connections (WSS) to the backend.
    *   **API Gateway / Reverse Proxy (Nginx/Caddy):** The public entry point. Handles incoming HTTPS/WSS traffic, performs SSL/TLS termination, routes API requests to the Backend Service, and proxies WebSocket connections. May also serve static web assets.
    *   **Backend API / WebSocket Service (Go Application):** The core backend logic written in Go. Exposes the REST API, manages WebSocket connections and real-time communication logic, interacts with the Database and Redis, queues background jobs, and communicates with external services.
    *   **Background Job Worker (Go / Asynq Worker):** A separate process (likely running the same Go codebase but in worker mode). Pulls jobs from the Redis Queue, processes them asynchronously (e.g., updating denormalized tables, sending batches of notifications, processing moderation results), and interacts with the Database, Redis, and External APIs.
    *   **PostgreSQL Database:** The primary relational database storing persistent application data (users, messages, groups, etc.).
    *   **Redis:** The in-memory data store used for caching sessions/data, managing WebSocket presence/state via Pub/Sub, and acting as the message broker for the Background Job Queue.

*   **Key Interactions Shown:**
    *   Clients connect to the API Gateway.
    *   API Gateway routes traffic to the Backend Service (API & WebSockets).
    *   Backend Service uses PostgreSQL for persistent storage.
    *   Backend Service uses Redis for caching, Pub/Sub (WebSocket coordination), and queueing jobs.
    *   Background Worker consumes jobs from the Redis Queue and interacts with the Database, Redis, and External APIs.
    *   Backend Service (and sometimes the Worker) interacts with external systems (Payments, AI, Push Notifications, Object Storage, STUN/TURN) via their respective APIs/protocols.

**Mermaid Syntax:**

```mermaid
graph TD
    %% Define Actors
    actor User [User]
    actor AdminUser [Admin User]

    %% Define System Boundary
    subgraph MyAppSystemBoundary [MEENA System Boundary]
        direction LR

        %% Client Containers
        subgraph ClientApps [Client Applications]
            ClientMobile[Mobile App (iOS/Android)]
            ClientWeb[Web Application (Browser)]
        end

        %% Backend Infrastructure Containers
        APIGateway[API Gateway / Reverse Proxy (Nginx/Caddy)]

        subgraph BackendServices [Backend Services (Go)]
            BackendApp[Backend API / WebSocket Service (Go)]
            BackgroundWorker[Background Job Worker (Go/Asynq)]
        end

        subgraph DataStores [Data Stores]
            Database[(PostgreSQL Database)]
            CacheQueue[(Redis Cache / Queue / PubSub)]
        end

        %% Interactions WITHIN the System Boundary
        ClientMobile -- "HTTPS API Calls / WSS WebSocket Connection" --> APIGateway
        ClientWeb -- "HTTPS API Calls / WSS WebSocket Connection" --> APIGateway

        APIGateway -- "Routes API (HTTPS) / Proxies WebSocket (WSS)" --> BackendApp

        BackendApp -- "Reads/Writes Data [SQL]" --> Database
        BackendApp -- "Reads/Writes Cache, Pub/Sub, Enqueues Jobs [Redis Protocol]" --> CacheQueue
        BackendApp -- "Invokes External Services [HTTPS/API]" --> ExternalAPIs[External Service Integrations]

        BackgroundWorker -- "Dequeues Jobs [Redis Protocol]" --> CacheQueue
        BackgroundWorker -- "Reads/Writes Data [SQL]" --> Database
        BackgroundWorker -- "Reads/Writes Cache [Redis Protocol]" --> CacheQueue
        BackgroundWorker -- "Invokes External Services [HTTPS/API]" --> ExternalAPIs

        AdminUser -- "Uses Admin Interface/API [HTTPS]" --> APIGateway %% Assuming admin access goes via Gateway

    end

    %% Define External Systems (Outside the main boundary for clarity)
    subgraph ExternalSystems [External Dependencies]
        Ext_Payment[(Payment Gateway)]
        Ext_Push[(Push Notification Service APNS/FCM)]
        Ext_AIMod[(AI Moderation Service)]
        Ext_ObjectStore[(Object Storage)]
        Ext_STUN_TURN[(STUN/TURN Service)]
    end

    %% Interactions with External Systems (Conceptual Link)
    ExternalAPIs -- "Processes Payments" --> Ext_Payment
    ExternalAPIs -- "Sends Push Notifications" --> Ext_Push
    ExternalAPIs -- "Requests Content Analysis" --> Ext_AIMod
    ExternalAPIs -- "Stores/Retrieves Media Files" --> Ext_ObjectStore
    ExternalAPIs -- "Provides Server Info" --> Ext_STUN_TURN %% Call Service provides info to clients

    %% Direct Client -> External System Interactions
    ClientMobile -- "Receives Notifications" --> Ext_Push %% Mobile OS interaction
    ClientMobile -- "Direct Media Upload/Download? (Signed URL)" --> Ext_ObjectStore %% Often via Signed URLs
    ClientWeb -- "Direct Media Upload/Download? (Signed URL)" --> Ext_ObjectStore %% Often via Signed URLs
    ClientMobile -- "WebRTC Media Stream" --> Ext_STUN_TURN %% For NAT Traversal
    ClientWeb -- "WebRTC Media Stream" --> Ext_STUN_TURN %% For NAT Traversal


    %% Styling
    style MyAppSystemBoundary fill:#ececff,stroke:#999,stroke-width:2px,stroke-dasharray: 5 5
    style BackendServices fill:#ccffcc
    style DataStores fill:#ffffcc
    style ClientApps fill:#cceeff
```

**How to Interpret:**

*   This diagram shows the distinct deployable pieces of your system.
*   The `Backend API / WebSocket Service` is the central brain written in Go.
*   The `Background Job Worker` handles offline processing, reducing load on the main API service.
*   `PostgreSQL` and `Redis` are the essential stateful components.
*   The arrows indicate the *primary* communication protocols or data flow types (e.g., `[SQL]`, `[HTTPS]`, `[Redis Protocol]`).
*   It clarifies how clients interact (via the API Gateway) and how backend components communicate with each other (often via Redis for decoupling or direct DB access) and with external services.
*   It also shows potential direct interactions between clients and some external services (like receiving push notifications or direct object storage access via signed URLs).