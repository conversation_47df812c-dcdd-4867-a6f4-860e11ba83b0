# Testing Guide

This guide provides detailed instructions for testing components in our new architecture approach.

## Overview

Testing is a critical aspect of any application. In our architecture, we've implemented a testable approach that ensures components can be tested in isolation.

The key components of our testing approach are:

1. **Unit Tests**: Tests for individual components (e.g., ViewModels, use cases, repositories).
2. **Integration Tests**: Tests for the interaction between components.
3. **UI Tests**: Tests for the UI components.

## Unit Testing

### Testing ViewModels

ViewModels are tested using JUnit and the Kotlin Coroutines Test library. The key aspects of testing ViewModels are:

1. **Mocking Dependencies**: Use Mockito or MockK to mock dependencies (e.g., use cases, ErrorHandler).
2. **Testing State Updates**: Verify that the ViewModel updates the UI state correctly.
3. **Testing Error Handling**: Verify that the ViewModel handles errors correctly.

Example of a ViewModel test:

```kotlin
@ExperimentalCoroutinesApi
class AuthViewModelTest {

    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    val coroutineRule = MainCoroutineRule()

    private lateinit var viewModel: AuthViewModel
    private lateinit var loginUseCase: LoginUseCase
    private lateinit var registerUseCase: RegisterUseCase
    private lateinit var twoFactorAuthUseCase: TwoFactorAuthUseCase
    private lateinit var recoverAccountUseCase: RecoverAccountUseCase
    private lateinit var errorHandler: ErrorHandler

    @Before
    fun setup() {
        loginUseCase = mock(LoginUseCase::class.java)
        registerUseCase = mock(RegisterUseCase::class.java)
        twoFactorAuthUseCase = mock(TwoFactorAuthUseCase::class.java)
        recoverAccountUseCase = mock(RecoverAccountUseCase::class.java)
        errorHandler = mock(ErrorHandler::class.java)

        viewModel = AuthViewModel(
            loginUseCase,
            registerUseCase,
            twoFactorAuthUseCase,
            recoverAccountUseCase,
            errorHandler
        )
    }

    @Test
    fun `login success updates state correctly`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val response = LoginResponse(
            userId = "123",
            userHandle = "test",
            requires2fa = false
        )
    }
}
```

### Testing Use Cases

Use cases are tested using JUnit. The key aspects of testing use cases are:

1. **Mocking Dependencies**: Use Mockito or MockK to mock dependencies (e.g., repositories).
2. **Testing Success Cases**: Verify that the use case returns the expected result on success.
3. **Testing Failure Cases**: Verify that the use case returns the expected result on failure.

Example of a use case test:

```kotlin
class LoginUseCaseTest {

    private lateinit var useCase: LoginUseCase
    private lateinit var authRepository: AuthRepository

    @Before
    fun setup() {
        authRepository = mock(AuthRepository::class.java)
        useCase = LoginUseCase(authRepository)
    }

    @Test
    fun `invoke returns success result when repository returns success`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val response = LoginResponse(
            userId = "123",
            userHandle = "test",
            requires2fa = false
        )
        whenever(authRepository.login(identifier, password)).thenReturn(Result.success(response))

        // When
        val result = useCase(identifier, password)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(response, result.getOrNull())
    }

    @Test
    fun `invoke returns failure result when repository returns failure`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val error = IOException("Network error")
        whenever(authRepository.login(identifier, password)).thenReturn(Result.failure(error))

        // When
        val result = useCase(identifier, password)

        // Then
        assertTrue(result.isFailure)
        assertEquals(error, result.exceptionOrNull())
    }
}
```

### Testing Repositories

Repositories are tested using JUnit. The key aspects of testing repositories are:

1. **Mocking Dependencies**: Use Mockito or MockK to mock dependencies (e.g., API, DAO).
2. **Testing Success Cases**: Verify that the repository returns the expected result on success.
3. **Testing Failure Cases**: Verify that the repository returns the expected result on failure.

Example of a repository test:

```kotlin
class AuthRepositoryImplTest {

    private lateinit var repository: AuthRepositoryImpl
    private lateinit var authApi: AuthApi
    private lateinit var authDao: AuthDao
    private lateinit var tokenManager: TokenManager

    @Before
    fun setup() {
        authApi = mock(AuthApi::class.java)
        authDao = mock(AuthDao::class.java)
        tokenManager = mock(TokenManager::class.java)
        repository = AuthRepositoryImpl(authApi, authDao, tokenManager)
    }

    @Test
    fun `login returns success result when API returns success`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val response = LoginResponse(
            userId = "123",
            userHandle = "test",
            accessToken = "access_token",
            refreshToken = "refresh_token",
            requires2fa = false
        )
        whenever(authApi.login(LoginRequest(identifier, password))).thenReturn(response)

        // When
        val result = repository.login(identifier, password)

        // Then
        assertTrue(result.isSuccess)
        assertEquals(response, result.getOrNull())
        verify(tokenManager).saveAccessToken(response.accessToken)
        verify(tokenManager).saveRefreshToken(response.refreshToken)
    }

    @Test
    fun `login returns failure result when API throws exception`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val error = IOException("Network error")
        whenever(authApi.login(LoginRequest(identifier, password))).thenThrow(error)

        // When
        val result = repository.login(identifier, password)

        // Then
        assertTrue(result.isFailure)
        assertEquals(error, result.exceptionOrNull())
    }
}
```

## Integration Testing

Integration tests verify that components work together correctly. The key aspects of integration testing are:

1. **Testing Component Interactions**: Verify that components interact correctly.
2. **Testing End-to-End Flows**: Verify that end-to-end flows work correctly.

Example of an integration test:

```kotlin
@ExperimentalCoroutinesApi
class AuthIntegrationTest {

    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    val coroutineRule = MainCoroutineRule()

    private lateinit var viewModel: AuthViewModel
    private lateinit var loginUseCase: LoginUseCase
    private lateinit var authRepository: AuthRepository
    private lateinit var authApi: AuthApi
    private lateinit var tokenManager: TokenManager
    private lateinit var errorHandler: ErrorHandler

    @Before
    fun setup() {
        authApi = mock(AuthApi::class.java)
        tokenManager = mock(TokenManager::class.java)
        errorHandler = mock(ErrorHandler::class.java)

        authRepository = AuthRepositoryImpl(authApi, mock(AuthDao::class.java), tokenManager)
        loginUseCase = LoginUseCase(authRepository)
        viewModel = AuthViewModel(
            loginUseCase,
            mock(RegisterUseCase::class.java),
            mock(TwoFactorAuthUseCase::class.java),
            mock(RecoverAccountUseCase::class.java),
            errorHandler
        )
    }

    @Test
    fun `login flow works correctly`() = runBlockingTest {
        // Given
        val identifier = "test"
        val password = "password"
        val apiResponse = LoginApiResponse(
            userId = "123",
            userHandle = "test",
            accessToken = "access_token",
            refreshToken = "refresh_token",
            requires2fa = false
        )
        whenever(authApi.login(LoginRequest(identifier, password))).thenReturn(apiResponse)

        // When
        viewModel.login(identifier, password)

        // Then
        val authState = viewModel.authState.value
        assertTrue(authState.loginOperation.isSuccessful)
        assertEquals("123", authState.userId)
        assertEquals("test", authState.userHandle)
        assertFalse(authState.requires2fa)
        verify(tokenManager).saveAccessToken(apiResponse.accessToken)
        verify(tokenManager).saveRefreshToken(apiResponse.refreshToken)
    }
}
```

## UI Testing

UI tests verify that UI components work correctly. The key aspects of UI testing are:

1. **Testing UI Rendering**: Verify that UI components render correctly.
2. **Testing User Interactions**: Verify that user interactions work correctly.
3. **Testing UI State Updates**: Verify that UI components update correctly when the state changes.

Example of a UI test:

```kotlin
@ExperimentalCoroutinesApi
class LoginScreenTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private lateinit var viewModel: AuthViewModel
    private lateinit var loginUseCase: LoginUseCase
    private lateinit var errorHandler: ErrorHandler

    @Before
    fun setup() {
        loginUseCase = mock(LoginUseCase::class.java)
        errorHandler = mock(ErrorHandler::class.java)

        viewModel = AuthViewModel(
            loginUseCase,
            mock(RegisterUseCase::class.java),
            mock(TwoFactorAuthUseCase::class.java),
            mock(RecoverAccountUseCase::class.java),
            errorHandler
        )
    }

    @Test
    fun `login screen renders correctly`() {
        // Given
        composeTestRule.setContent {
            LoginScreen(
                onNavigateToRegister = {},
                onNavigateToRecovery = {},
                onLoginSuccess = {},
                onNavigateTo2FA = {},
                viewModel = viewModel
            )
        }

        // Then
        composeTestRule.onNodeWithText("Sign In").assertIsDisplayed()
        composeTestRule.onNodeWithText("Username or Email").assertIsDisplayed()
        composeTestRule.onNodeWithText("Password").assertIsDisplayed()
        composeTestRule.onNodeWithText("Don't have an account? Sign Up").assertIsDisplayed()
        composeTestRule.onNodeWithText("Forgot your password? Recover Account").assertIsDisplayed()
    }

    @Test
    fun `login button click calls login method`() {
        // Given
        val identifier = "test"
        val password = "password"
        composeTestRule.setContent {
            LoginScreen(
                onNavigateToRegister = {},
                onNavigateToRecovery = {},
                onLoginSuccess = {},
                onNavigateTo2FA = {},
                viewModel = viewModel
            )
        }

        // Then
        composeTestRule.onNodeWithText("Username or Email").performTextInput(identifier)
        composeTestRule.onNodeWithText("Password").performTextInput(password)
        composeTestRule.onNodeWithText("Sign In").performClick()

        // Verify that the login method was called with the correct parameters
        verify(viewModel).login(identifier, password)
    }
}
```

## Testing OperationState

The `OperationState` class is tested using JUnit. The key aspects of testing `OperationState` are:

1. **Testing Default Constructor**: Verify that the default constructor creates an idle state.
2. **Testing Extension Functions**: Verify that the extension functions update the state correctly.

Example of an `OperationState` test:

```kotlin
class OperationStateTest {

    @Test
    fun `default constructor creates idle state`() {
        val state = OperationState()
        
        assertFalse(state.isInProgress)
        assertFalse(state.isSuccessful)
        assertNull(state.error)
    }
    
    @Test
    fun `start() sets isInProgress to true and resets other fields`() {
        val initialState = OperationState(
            isInProgress = false,
            isSuccessful = true,
            error = "Error"
        )
        
        val startedState = initialState.start()
        
        assertTrue(startedState.isInProgress)
        assertFalse(startedState.isSuccessful)
        assertNull(startedState.error)
    }
    
    @Test
    fun `success() sets isSuccessful to true and isInProgress to false`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = false,
            error = "Error"
        )
        
        val successState = initialState.success()
        
        assertFalse(successState.isInProgress)
        assertTrue(successState.isSuccessful)
        assertNull(successState.error)
    }
    
    @Test
    fun `failure() sets error and isInProgress to false`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = true,
            error = null
        )
        
        val errorMessage = "Error message"
        val failureState = initialState.failure(errorMessage)
        
        assertFalse(failureState.isInProgress)
        assertFalse(failureState.isSuccessful)
        assertEquals(errorMessage, failureState.error)
    }
    
    @Test
    fun `reset() resets all fields to default values`() {
        val initialState = OperationState(
            isInProgress = true,
            isSuccessful = true,
            error = "Error"
        )
        
        val resetState = initialState.reset()
        
        assertFalse(resetState.isInProgress)
        assertFalse(resetState.isSuccessful)
        assertNull(resetState.error)
    }
}
```

## Best Practices

### 1. Use Dependency Injection for Testability

Use dependency injection to make components testable. This allows you to mock dependencies in tests.

```kotlin
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    private val registerUseCase: RegisterUseCase,
    private val twoFactorAuthUseCase: TwoFactorAuthUseCase,
    private val recoverAccountUseCase: RecoverAccountUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {
    // ...
}
```

### 2. Use Interfaces for Dependencies

Use interfaces for dependencies to make them easier to mock in tests.

```kotlin
interface AuthRepository {
    suspend fun login(identifier: String, password: String): Result<LoginResponse>
    suspend fun register(userHandle: String?, password: String, email: String?, phoneNumber: String?): Result<RegisterResponse>
    suspend fun twoFactorAuth(code: String): Result<TwoFactorAuthResponse>
    suspend fun recoverAccount(userHandle: String, recoveryPhrase: String, recoveryPin: String, newPassword: String): Result<RecoverAccountResponse>
    suspend fun isLoggedIn(): Result<Boolean>
}
```

### 3. Use TestCoroutineDispatcher for Testing Coroutines

Use `TestCoroutineDispatcher` to test coroutines. This allows you to control the execution of coroutines in tests.

```kotlin
@ExperimentalCoroutinesApi
class MainCoroutineRule(
    private val dispatcher: TestCoroutineDispatcher = TestCoroutineDispatcher()
) : TestWatcher(), TestCoroutineScope by TestCoroutineScope(dispatcher) {

    override fun starting(description: Description?) {
        super.starting(description)
        Dispatchers.setMain(dispatcher)
    }

    override fun finished(description: Description?) {
        super.finished(description)
        cleanupTestCoroutines()
        Dispatchers.resetMain()
    }
}
```

### 4. Use InstantTaskExecutorRule for Testing LiveData

Use `InstantTaskExecutorRule` to test LiveData. This ensures that LiveData updates are executed synchronously in tests.

```kotlin
@get:Rule
val instantExecutorRule = InstantTaskExecutorRule()
```

### 5. Use Mockito or MockK for Mocking Dependencies

Use Mockito or MockK to mock dependencies in tests.

```kotlin
@Before
fun setup() {
    loginUseCase = mock(LoginUseCase::class.java)
    registerUseCase = mock(RegisterUseCase::class.java)
    twoFactorAuthUseCase = mock(TwoFactorAuthUseCase::class.java)
    recoverAccountUseCase = mock(RecoverAccountUseCase::class.java)
    errorHandler = mock(ErrorHandler::class.java)

    viewModel = AuthViewModel(
        loginUseCase,
        registerUseCase,
        twoFactorAuthUseCase,
        recoverAccountUseCase,
        errorHandler
    )
}
```

### 6. Use Compose Testing for UI Tests

Use Compose Testing to test UI components.

```kotlin
@get:Rule
val composeTestRule = createComposeRule()
...

    // Then
    composeTestRule.onNodeWithText("Sign In").assertIsDisplayed()
    composeTestRule.onNodeWithText("Username or Email").assertIsDisplayed()
    composeTestRule.onNodeWithText("Password").assertIsDisplayed()
    composeTestRule.onNodeWithText("Don't have an account? Sign Up").assertIsDisplayed()
    composeTestRule.onNodeWithText("Forgot your password? Recover Account").assertIsDisplayed()
}
```

## Conclusion

By following these guidelines, you can implement testing that is consistent, maintainable, and comprehensive. The new architecture approach provides a solid foundation for testing at all levels of the app.
