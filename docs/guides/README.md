# Meena Developer Guides

This directory contains guides for developers working on the Meena application.

## Table of Contents

1. [Testing Guide](#testing-guide)
2. [Contribution Guide](#contribution-guide)
3. [How to Use These Guides](#how-to-use-these-guides)

## Testing Guide

The [Testing Guide](./testing_guide.md) provides detailed instructions for testing components in our architecture. It covers:

- Unit testing
- Integration testing
- UI testing
- Test doubles (mocks, stubs, fakes)
- Testing ViewModels, use cases, and repositories
- Testing with coroutines
- Testing with Hilt

This guide is essential for ensuring that your code is properly tested and maintainable.

## Contribution Guide

The Contribution Guide (planned) will provide guidelines for contributing to the Meena application. It will cover:

- Code style and formatting
- Pull request process
- Code review process
- Documentation requirements
- Testing requirements

This guide will ensure that contributions to the codebase are consistent and maintainable.

## How to Use These Guides

### For New Developers

1. Start with the [Testing Guide](./testing_guide.md) to understand how to test your code.
2. Review the Contribution Guide (when available) to understand how to contribute to the codebase.

### For Experienced Developers

1. Use the [Testing Guide](./testing_guide.md) as a reference for testing specific components.
2. Consult the Contribution Guide (when available) to ensure your contributions follow the established guidelines.

### For Code Reviewers

1. Use the [Testing Guide](./testing_guide.md) to ensure that code submissions are properly tested.
2. Use the Contribution Guide (when available) to ensure that code submissions follow the established guidelines.

## Related Documentation

- [Architecture Documentation](../architecture/README.md) - Application architecture
- [Implementation Guidelines](../implementation/README.md) - Performance benchmarks and implementation priorities
