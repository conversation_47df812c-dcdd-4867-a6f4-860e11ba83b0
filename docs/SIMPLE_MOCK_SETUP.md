# Simple Mock Backend Setup Guide

## 🎯 Overview

This guide provides a simplified approach to using the mock backend that works seamlessly with Android Studio's standard build process.

## 🚀 Quick Setup

### For Testers (Mock App with Live Backend)

1. **Open `app/build.gradle.kts`**
2. **Find line ~49** (in the release build type):
   ```kotlin
   buildConfigField("boolean", "USE_MOCK_BACKEND", "false")
   ```
3. **Change it to**:
   ```kotlin
   buildConfigField("boolean", "USE_MOCK_BACKEND", "true")
   ```
4. **Sync the project**
5. **Generate AAB**: Build → Generate Signed Bundle/APK → Select "release"

### For Developers (Local Mock Development)

1. **Open `app/build.gradle.kts`**
2. **Find line ~40** (in the debug build type):
   ```kotlin
   buildConfigField("String", "MOCK_BASE_URL", "\"https://socialmediabackend-production-b175.up.railway.app/\"")
   ```
3. **Change it to**:
   ```kotlin
   buildConfigField("String", "MOCK_BASE_URL", "\"http://localhost:8080/\"")
   ```
4. **Sync the project**
5. **Run the app** in debug mode

## 📋 Available Configurations

### Current Setup (Simplified)

| Build Type | Mock Backend | Backend URL | Use Case |
|------------|--------------|-------------|----------|
| **Debug** | ✅ Enabled | Live backend | Development with real data |
| **Release** | ❌ Disabled | Live backend | Production app |

### Easy Modifications

#### Option 1: Mock App for Play Store
```kotlin
// In release build type, change:
buildConfigField("boolean", "USE_MOCK_BACKEND", "true")
```

#### Option 2: Local Development
```kotlin
// In debug build type, change:
buildConfigField("String", "MOCK_BASE_URL", "\"http://localhost:8080/\"")
```

#### Option 3: Different Backend URLs
```kotlin
// Change to staging server:
buildConfigField("String", "MOCK_BASE_URL", "\"https://staging-backend.com/\"")

// Change to local server:
buildConfigField("String", "MOCK_BASE_URL", "\"http://********:8080/\"") // For emulator
```

## 🏗️ Android Studio Build Process

### Generate AAB (App Bundle)

1. **Build → Generate Signed Bundle/APK**
2. **Select "Android App Bundle"**
3. **Choose build type**:
   - **Debug**: For testing (includes debug symbols)
   - **Release**: For Play Store (optimized)
4. **Let Android Studio handle signing**
5. **Click "Create"**

### Output Locations

```
app/build/outputs/bundle/
├── debug/
│   └── app-debug.aab          # Debug version
└── release/
    └── app-release.aab        # Release version
```

## 🎛️ Runtime Configuration

### Developer Menu

The app includes a developer menu for runtime configuration:

1. **Open the app**
2. **Go to Settings → Developer Menu**
3. **Toggle "Use Mock Backend"**
4. **Reset mock data if needed**

### Feature Flags

Control which features are enabled:
- Stories
- Calls  
- Group Chats
- Channels
- Media Messages
- AI Chat Responses

## 🔄 Common Scenarios

### Scenario 1: Testers Need Mock App with Live Data

**Goal**: Testers get realistic data but with mock app features

**Steps**:
1. Set `USE_MOCK_BACKEND = "true"` in release build type
2. Keep `MOCK_BASE_URL` pointing to live backend
3. Generate release AAB
4. Upload to Play Store or distribute directly

**Result**: App uses live backend data but maintains all mock infrastructure

### Scenario 2: Developers Need Offline Development

**Goal**: Work without internet connection

**Steps**:
1. Set `MOCK_BASE_URL = "http://localhost:8080/"` in debug build type
2. Keep `USE_MOCK_BACKEND = "true"` in debug build type
3. Run in debug mode

**Result**: App uses local mock data and AI responses

### Scenario 3: Production Release

**Goal**: Real app for end users

**Steps**:
1. Set `USE_MOCK_BACKEND = "false"` in release build type
2. Set `REAL_BASE_URL` to production backend
3. Generate release AAB

**Result**: Standard production app

## 🛠️ Quick Configuration Changes

### For Mock App Release (Most Common)

```kotlin
// In app/build.gradle.kts, release build type:
buildConfigField("boolean", "USE_MOCK_BACKEND", "true")
buildConfigField("String", "MOCK_BASE_URL", "\"https://your-backend.com/\"")
```

### For Local Development

```kotlin
// In app/build.gradle.kts, debug build type:
buildConfigField("boolean", "USE_MOCK_BACKEND", "true")
buildConfigField("String", "MOCK_BASE_URL", "\"http://localhost:8080/\"")
```

## 🎯 Benefits of This Approach

✅ **Simple**: Only two build types (debug/release)
✅ **Android Studio Compatible**: Works with standard build process
✅ **No Complex Signing**: Uses Android Studio's automatic signing
✅ **Play Store Ready**: Google handles app signing automatically
✅ **Easy Configuration**: Just change a few lines in build.gradle.kts
✅ **Runtime Toggle**: Can switch modes without rebuilding

## 🚨 Important Notes

1. **Sync After Changes**: Always sync the project after modifying build.gradle.kts
2. **Clean Build**: If switching configurations, do a clean build
3. **Check Build Type**: Make sure you're generating the right build type (debug vs release)
4. **Backend URL**: Ensure the backend URL is accessible from the device/emulator

## 📞 Support

If you need help:
1. Check the developer menu in the app
2. Look for build errors in Android Studio
3. Verify backend URL accessibility
4. Try a clean build if issues persist

This simplified approach gives you all the benefits of the mock backend system while working seamlessly with Android Studio's standard build process!
