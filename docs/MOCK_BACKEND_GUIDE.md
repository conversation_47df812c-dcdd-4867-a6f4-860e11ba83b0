# Mock Backend Development Guide

## Overview

The Meena Android app includes a comprehensive mock backend system that allows developers and testers to work independently without requiring a deployed backend server. This system provides realistic data, AI-powered chat simulation, and full API contract compliance.

## Features

### 🎯 Core Features
- **Complete API Simulation**: All backend APIs are mocked with realistic responses
- **AI-Powered Chat**: Intelligent chat responses using multiple AI personas
- **Data Persistence**: Mock data survives app restarts
- **Configuration Toggle**: Easy switching between mock and real backend
- **Realistic User Scenarios**: Pre-generated users, chats, contacts, and messages

### 🤖 AI Chat Simulation
- **Multiple Personas**: 6 different AI personalities (friendly, tech enthusiast, casual, professional, humorous, supportive)
- **Context-Aware Responses**: AI considers conversation history and message context
- **Realistic Timing**: Simulated typing indicators and response delays
- **Conversation Types**: Supports one-to-one, group, and channel chats

### 📊 Data Management
- **Persistent Storage**: Uses SharedPreferences for data persistence
- **Realistic Data**: Generated users, contacts, chats, and messages
- **Configurable Scale**: Adjustable number of mock users and conversations
- **Easy Reset**: Developer tools for clearing and regenerating data

## Quick Start

### 1. Enable Mock Mode

#### Option A: Build Variants (Recommended)
```bash
# Build with mock backend
./gradlew assembleMockDebug

# Build with real backend  
./gradlew assembleRealDebug
```

#### Option B: Runtime Configuration
```kotlin
// In your Application class or early in app lifecycle
AppConfig.setMockMode(true)
```

### 2. Install and Run
```bash
# Install mock variant
adb install app/build/outputs/apk/mock/debug/app-mock-debug.apk

# Or run directly
./gradlew installMockDebug
```

### 3. Access Developer Menu
- Open the app
- Navigate to Settings → Developer Menu (if enabled)
- Control mock behavior and view statistics

## Configuration

### Build Configuration

The app supports multiple build variants:

```kotlin
// app/build.gradle.kts
productFlavors {
    create("mock") {
        dimension = "backend"
        applicationIdSuffix = ".mock"
        buildConfigField("boolean", "USE_MOCK_BACKEND", "true")
    }
    create("real") {
        dimension = "backend"
        buildConfigField("boolean", "USE_MOCK_BACKEND", "false")
    }
}
```

### Runtime Configuration

```kotlin
// AppConfig.kt - Main configuration
object AppConfig {
    val useMockBackend: Boolean
    val baseUrl: String
    val webSocketUrl: String
    
    // Mock-specific settings
    object MockConfig {
        const val NETWORK_DELAY_MIN = 200L
        const val NETWORK_DELAY_MAX = 1000L
        const val AI_RESPONSE_DELAY_MIN = 1000L
        const val AI_RESPONSE_DELAY_MAX = 3000L
        const val DEFAULT_MOCK_USERS_COUNT = 20
        const val DEFAULT_MOCK_CHATS_COUNT = 10
    }
}
```

## AI Personas

### Available Personas

1. **Friendly Assistant** - Helpful and warm, always ready to assist
2. **Tech Enthusiast** - Passionate about technology and innovation
3. **Casual Friend** - Laid-back and easy-going personality
4. **Professional Colleague** - Business-focused and professional
5. **Humorous Buddy** - Always ready with jokes and funny comments
6. **Supportive Mentor** - Wise and encouraging, offers guidance

### Persona Configuration

```kotlin
// Creating custom personas
val customPersona = AIPersona(
    id = "custom_persona",
    name = "Custom Persona",
    description = "Your custom personality",
    responsePatterns = listOf(
        ResponsePattern(
            trigger = MessageTrigger.Greeting,
            responses = listOf("Hello!", "Hi there!", "Hey!")
        )
    ),
    personality = PersonalityTraits(
        friendliness = 0.8f,
        formality = 0.3f,
        humor = 0.6f
    )
)
```

## API Implementation

### Mock API Structure

```
mock/
├── api/
│   ├── MockAuthApi.kt      # Authentication endpoints
│   ├── MockChatApi.kt      # Chat and messaging
│   ├── MockContactApi.kt   # Contact management
│   └── MockUserApi.kt      # User profile operations
├── ai/
│   ├── AIPersona.kt        # AI personality definitions
│   └── AIResponseGenerator.kt # Response generation logic
├── data/
│   └── MockDataGenerator.kt # Realistic data generation
└── storage/
    └── MockDataStorage.kt  # Persistent data management
```

### Adding New Mock APIs

1. **Create Mock Implementation**
```kotlin
@Singleton
class MockNewApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : NewApi {
    
    override suspend fun newEndpoint(request: NewRequest): Response<NewResponse> {
        simulateNetworkDelay()
        
        // Validate authentication
        val userId = extractUserIdFromToken(authToken)
            ?: return createErrorResponse(401, "Unauthorized")
        
        // Implement mock logic
        return Response.success(mockResponse)
    }
}
```

2. **Add to Dependency Injection**
```kotlin
// In NetworkModule.kt
@Provides
@Singleton
fun provideNewApi(
    retrofit: Retrofit,
    mockNewApi: MockNewApi
): NewApi {
    return if (AppConfig.useMockBackend) {
        mockNewApi
    } else {
        retrofit.create(NewApi::class.java)
    }
}
```

## Testing Scenarios

### Pre-configured Test Data

The mock system generates realistic test scenarios:

- **20 Mock Users** with varied profiles and activity
- **15 Contacts** with different relationship types
- **10 Conversations** including one-to-one, groups, and channels
- **50+ Messages** per conversation with varied content types
- **AI Responses** that adapt to conversation context

### Custom Test Scenarios

```kotlin
// Generate specific test data
val testUser = MockDataGenerator.generateMockUser()
val testChat = MockDataGenerator.generateMockChat(
    participants = listOf(currentUserId, testUser.id),
    type = ConversationType.ONE_TO_ONE
)
mockDataStorage.addUser(testUser)
mockDataStorage.addChat(testChat)
```

## Developer Tools

### Developer Menu Features

- **Mock Backend Toggle**: Switch between mock and real backend
- **Data Management**: Reset, clear, or regenerate mock data
- **AI Configuration**: Enable/disable AI responses and typing indicators
- **Feature Flags**: Control which features are enabled
- **Statistics**: View current mock data counts and status
- **Debug Actions**: Generate test data, simulate events

### Accessing Developer Menu

1. Enable in configuration:
```kotlin
object DevConfig {
    const val ENABLE_DEV_MENU = true
}
```

2. Add to navigation:
```kotlin
// In your navigation setup
if (AppConfig.DevConfig.ENABLE_DEV_MENU) {
    composable("developer_menu") {
        DeveloperMenuScreen(onNavigateBack = { navController.popBackStack() })
    }
}
```

## Troubleshooting

### Common Issues

1. **Mock data not loading**
   - Check if `AppConfig.useMockBackend` is true
   - Verify MockDataStorage initialization
   - Clear app data and restart

2. **AI responses not working**
   - Ensure `ENABLE_AI_CHAT_RESPONSES` is true
   - Check network delay settings
   - Verify chat type is one-to-one

3. **Build variant issues**
   - Clean and rebuild project
   - Check flavor configuration in build.gradle
   - Verify BuildConfig fields are generated

### Debug Logging

Enable detailed logging:
```kotlin
object DevConfig {
    const val ENABLE_MOCK_LOGGING = true
    const val ENABLE_API_LOGGING = true
}
```

## Best Practices

### For Developers

1. **Use Build Variants**: Prefer build variants over runtime configuration
2. **Test Both Modes**: Regularly test with both mock and real backend
3. **Realistic Data**: Keep mock data realistic and up-to-date with API contracts
4. **Error Simulation**: Include error scenarios in mock responses

### For Testers

1. **Reset Data**: Use developer menu to reset data between test sessions
2. **Explore Personas**: Test different AI personalities in chats
3. **Feature Testing**: Use feature flags to test different app configurations
4. **Edge Cases**: Test with various data states (empty, full, error conditions)

## Advanced Configuration

### Custom AI Responses

```kotlin
// Add custom response patterns
val customPattern = ResponsePattern(
    trigger = MessageTrigger.Keyword(listOf("help", "support")),
    responses = listOf(
        "I'm here to help! What do you need?",
        "How can I assist you today?",
        "Let me know what you need help with!"
    ),
    probability = 0.9f
)
```

### Network Simulation

```kotlin
// Simulate different network conditions
object MockConfig {
    const val SIMULATE_NETWORK_ERRORS = true
    const val ERROR_RATE_PERCENTAGE = 5 // 5% error rate
    const val SLOW_NETWORK_DELAY = 5000L // 5 second delay
}
```

## Contributing

When adding new features to the mock backend:

1. Follow existing patterns and structure
2. Add comprehensive documentation
3. Include realistic test data
4. Test with various configurations
5. Update this guide with new features

## Support

For questions or issues with the mock backend system:

1. Check this documentation
2. Review existing mock implementations
3. Use developer menu for debugging
4. Check console logs when debug logging is enabled
