# Tester Setup Guide - Meena Mock Backend

## Overview

This guide helps testers set up and use the Meena Android app with the mock backend for comprehensive testing without requiring a live server connection.

## Quick Setup

### 1. Install the Mock Version

Download and install the mock version of the app:

```bash
# If you have the APK file
adb install app-mock-debug.apk

# Or if building from source
./gradlew installMockDebug
```

### 2. First Launch

When you first open the app:

1. **Automatic Setup**: The app will automatically generate realistic test data
2. **Mock User**: You'll be logged in as a pre-configured test user
3. **Sample Data**: The app includes contacts, chats, and message history

### 3. Access Developer Tools

1. Open the app
2. Go to **Settings** → **Developer Menu**
3. Here you can control mock behavior and reset data

## Test Scenarios

### Pre-configured Test Data

The mock backend includes:

- **20 Realistic Users** with varied profiles
- **15 Contacts** in your contact list
- **10 Active Conversations**:
  - 8 one-to-one chats
  - 2 group chats
  - 1 channel
- **AI-Powered Responses** in conversations
- **Message History** with different content types

### User Profiles

Test users include varied characteristics:
- Verified and unverified users
- Gold members and regular users
- Different activity levels and online status
- Varied profile information and avatars

## Testing Features

### 1. Authentication Flow

**Registration Testing:**
- Try registering with different user handles
- Test with various display names and bio information
- Verify recovery phrase generation

**Login Testing:**
- Use any existing user handle from the mock data
- Password can be anything (mock accepts all passwords)
- Test 2FA flow (randomly triggered)

**Recovery Testing:**
- Use any user handle and recovery phrase
- PIN can be any 6-digit number
- Test password reset flow

### 2. Contact Management

**Adding Contacts:**
- Search for users by handle: `user1`, `user2`, etc.
- Add contacts with custom display names
- Test blocking/unblocking functionality

**Contact Organization:**
- Mark contacts as favorites
- Test contact search functionality
- Verify contact synchronization

### 3. Chat Features

**One-to-One Chats:**
- Send messages to any contact
- **AI Responses**: Contacts will respond with AI-generated messages
- Test different message types (text, media, reactions)
- Verify read receipts and delivery status

**Group Chats:**
- Create new groups with multiple participants
- Test admin functions (add/remove members, change settings)
- Send messages and observe group dynamics

**Channels:**
- Join existing channels
- Test broadcast-style messaging
- Verify subscriber management

### 4. AI Chat Simulation

The mock backend includes 6 different AI personas:

1. **Friendly Assistant** - Helpful and supportive responses
2. **Tech Enthusiast** - Technology-focused conversations
3. **Casual Friend** - Relaxed, informal communication
4. **Professional Colleague** - Business-like interactions
5. **Humorous Buddy** - Jokes and funny responses
6. **Supportive Mentor** - Encouraging and wise guidance

**Testing AI Responses:**
- Send different types of messages (questions, greetings, complaints)
- Observe how AI adapts to conversation context
- Test response timing and typing indicators

### 5. Media and Attachments

**Media Messages:**
- Send images, videos, and audio messages
- Test file attachments
- Verify thumbnail generation and media playback

**Media Management:**
- Test media download and upload
- Verify media storage and organization
- Test media sharing between chats

### 6. Stories Feature

**Creating Stories:**
- Create photo and video stories
- Add text overlays and stickers
- Test story privacy settings

**Viewing Stories:**
- View stories from contacts
- Test story interactions (views, reactions)
- Verify story expiration (24 hours)

### 7. Profile Management

**Profile Editing:**
- Update display name and bio
- Change profile picture
- Test verification status display

**Settings:**
- Modify notification preferences
- Test privacy settings
- Configure security options

## Developer Menu Features

### Data Management

**Reset Mock Data:**
- Clears all current data
- Regenerates fresh test data
- Useful for starting clean test sessions

**View Statistics:**
- See current data counts (users, chats, messages)
- Monitor mock backend status
- Check current user information

### Feature Controls

**AI Chat Settings:**
- Enable/disable AI responses
- Control typing indicators
- Adjust response timing

**Feature Flags:**
- Toggle specific features on/off
- Test app behavior with different configurations
- Simulate feature rollouts

### Debug Actions

**Generate Test Data:**
- Add more users and conversations
- Create specific test scenarios
- Populate with additional content

**Simulate Events:**
- Trigger incoming messages
- Simulate call notifications
- Test real-time updates

## Testing Workflows

### 1. New User Experience

1. Reset mock data in developer menu
2. Go through registration flow
3. Explore empty state of the app
4. Add first contacts and start conversations
5. Test onboarding and tutorial flows

### 2. Active User Experience

1. Use default mock data (don't reset)
2. Navigate through existing conversations
3. Send messages and observe AI responses
4. Test all major features with existing data

### 3. Edge Cases

1. Test with no internet connection
2. Simulate app crashes and recovery
3. Test with maximum data (many contacts, long conversations)
4. Test with minimal data (few contacts, short conversations)

### 4. Performance Testing

1. Load conversations with many messages
2. Test scrolling through long chat histories
3. Send multiple messages rapidly
4. Test app responsiveness during AI response generation

## Common Test Scenarios

### Messaging Scenarios

1. **Basic Conversation:**
   - Send "Hello" to any contact
   - Wait for AI response
   - Continue conversation naturally

2. **Group Discussion:**
   - Create group with 3-5 participants
   - Send messages and observe group dynamics
   - Test admin functions

3. **Media Sharing:**
   - Send photos and videos
   - Test different file types
   - Verify media handling

### Social Features

1. **Story Interaction:**
   - Create and share stories
   - View stories from contacts
   - Test story privacy settings

2. **Contact Discovery:**
   - Search for new users
   - Send contact requests
   - Manage contact relationships

3. **Profile Customization:**
   - Update profile information
   - Change privacy settings
   - Test verification features

## Troubleshooting

### Common Issues

**App Not Responding:**
- Check if mock mode is enabled in developer menu
- Reset mock data if needed
- Restart the app

**No AI Responses:**
- Verify AI responses are enabled in developer menu
- Check if you're in a one-to-one chat
- Wait for response delay (1-3 seconds)

**Missing Data:**
- Use "Reset Mock Data" in developer menu
- Check if app has proper permissions
- Verify mock backend is enabled

**Performance Issues:**
- Clear app cache
- Reset mock data to reduce size
- Check device storage space

### Getting Help

1. Check developer menu for status information
2. Look for error messages in the app
3. Try resetting mock data
4. Restart the app if issues persist

## Best Practices

### For Effective Testing

1. **Start Fresh**: Reset data between major test sessions
2. **Test Systematically**: Follow structured test scenarios
3. **Document Issues**: Note any bugs or unexpected behavior
4. **Test Edge Cases**: Try unusual inputs and scenarios
5. **Verify AI Behavior**: Ensure AI responses are appropriate and contextual

### For Realistic Testing

1. **Use Natural Language**: Send realistic messages to test AI responses
2. **Vary Message Types**: Test text, media, and special messages
3. **Test Different Personas**: Engage with various AI personalities
4. **Simulate Real Usage**: Follow natural user behavior patterns

## Feedback and Reporting

When testing, please note:

- **Feature Completeness**: Are all expected features working?
- **AI Response Quality**: Are AI responses appropriate and engaging?
- **Performance**: Is the app responsive and smooth?
- **Data Persistence**: Does data survive app restarts?
- **User Experience**: Is the mock experience realistic and useful?

This mock backend system provides a comprehensive testing environment that closely mimics the final app experience while allowing independent testing and development.
