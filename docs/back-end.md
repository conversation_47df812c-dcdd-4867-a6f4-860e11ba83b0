This schema attempts to incorporate *all* your specified requirements into a cohesive PostgreSQL structure. It assumes supporting services (like Redis for caching/queues, object storage for media, payment gateways, AI moderation services) will be used alongside it.

**Disclaimer:** This is a complex database design. Implementing and optimizing it requires careful consideration of indexing, query patterns, transaction management, and security practices beyond just the schema definition. This schema represents a strong *blueprint*.

---

**Database Schema (PostgreSQL)**

```sql
-- Extension for UUID generation if not enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================================================
-- Core User & Authentication Tables
-- ==================================================

CREATE TYPE subscription_tier_enum AS ENUM ('free', 'gold');
CREATE TYPE verification_status_enum AS ENUM ('none', 'pending', 'verified', 'rejected');
CREATE TYPE two_fa_method_enum AS ENUM ('none', 'totp', 'face_id', 'fingerprint'); -- Can store multiple in settings

CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- Unique, user-facing, non-sequential ID (generate securely in application)
    user_handle VARCHAR(9) UNIQUE NOT NULL,
    -- User Profile Info (can be modified)
    display_name VARCHAR(100),
    profile_picture_url VARCHAR(255),
    bio TEXT,
    -- Authentication & Security (nullable for anonymous signup option)
    phone_number VARCHAR(20) UNIQUE, -- Consider encryption or hashing if stored
    email VARCHAR(100) UNIQUE,       -- Consider encryption or hashing if stored
    password_hash VARCHAR(255),      -- Required if using password auth
    -- Secret Phrase & PIN for Recovery (Store HASHES, never plain text)
    recovery_phrase_hash VARCHAR(255),
    recovery_pin_hash VARCHAR(255),
    -- PIN for Remote Wipe (Store HASH)
    remote_wipe_pin_hash VARCHAR(255),
    -- Subscription & Status
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    verification_status verification_status_enum NOT NULL DEFAULT 'none',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE,
    -- Account Deletion Scheduling
    scheduled_deletion_at TIMESTAMP WITH TIME ZONE, -- Set 60 days in future upon request
    -- Settings (Permissions, Notifications, 2FA, Theme, Language etc.)
    settings JSONB DEFAULT '{}'::jsonb -- Store keys like: 'permissions': {'can_call': 'contacts', ...}, 'notifications': {'volume': 80,...}, '2fa_methods': ['totp', 'face_id'], 'theme': 'dark', 'language': 'fr'
);

-- Indexes for common lookups
CREATE INDEX idx_users_user_handle ON users(user_handle);
CREATE INDEX idx_users_phone ON users(phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_scheduled_deletion ON users(scheduled_deletion_at) WHERE scheduled_deletion_at IS NOT NULL; -- For cleanup job

-- Stores active 2FA TOTP secrets (encrypt this data at rest!)
CREATE TABLE user_totp_secrets (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    secret_key VARCHAR(255) NOT NULL, -- Encrypted secret key
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- Subscription & Payment Tables
-- ==================================================

CREATE TYPE payment_status_enum AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE payment_purpose_enum AS ENUM ('subscription_gold', 'create_public_group', 'create_private_group', 'create_secret_group', 'create_public_channel', 'create_private_channel', 'create_secret_channel', 'account_verification');

CREATE TABLE payments (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL, -- e.g., 'EUR'
    status payment_status_enum NOT NULL DEFAULT 'pending',
    purpose payment_purpose_enum NOT NULL,
    related_entity_id UUID, -- e.g., group_id, channel_id (can be null for subscription)
    payment_processor VARCHAR(50), -- e.g., 'Stripe', 'PayPal'
    processor_transaction_id VARCHAR(255) UNIQUE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_processor_id ON payments(processor_transaction_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_purpose ON payments(purpose);

CREATE TABLE subscriptions (
    subscription_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    tier subscription_tier_enum NOT NULL DEFAULT 'gold',
    status VARCHAR(20) NOT NULL DEFAULT 'inactive', -- e.g., 'active', 'inactive', 'canceled', 'past_due'
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE, -- For fixed-term or expiration
    current_period_start TIMESTAMP WITH TIME ZONE, -- For recurring
    current_period_end TIMESTAMP WITH TIME ZONE, -- For recurring
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    last_payment_id UUID REFERENCES payments(payment_id) ON DELETE SET NULL,
    payment_processor_subscription_id VARCHAR(255) UNIQUE, -- ID from Stripe/etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_subscriptions_user_id_active ON subscriptions(user_id) WHERE status = 'active'; -- Ensure only one active subscription per user
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_end_date ON subscriptions(end_date);

-- ==================================================
-- Contact & Relationship Tables
-- ==================================================

CREATE TABLE contacts (
    -- Removed contact_id UUID PRIMARY KEY, composite key is better
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    contact_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    display_name VARCHAR(100), -- User's custom name for the contact
    is_blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, contact_user_id),
    CHECK (user_id != contact_user_id)
);

CREATE INDEX idx_contacts_contact_user_id ON contacts(contact_user_id); -- Find who added this user

-- Followers table for X-like public profiles/posts (if needed, same as before)
CREATE TABLE followers (
    follower_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    followed_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    followed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (follower_id, followed_id),
    CHECK (follower_id != followed_id)
);

CREATE INDEX idx_followers_followed_id ON followers(followed_id);

-- ==================================================
-- Conversation Core Tables (Chats, Groups, Channels)
-- ==================================================

CREATE TYPE privacy_type_enum AS ENUM ('public', 'private', 'secret');
CREATE TYPE moderation_status_enum AS ENUM ('ok', 'pending_review', 'flagged', 'banned'); -- Applied to groups/channels/messages etc.

CREATE TABLE chats ( -- For 1-to-1 conversations
    chat_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_encrypted BOOLEAN DEFAULT TRUE, -- Assuming E2EE for 1-to-1
    encryption_metadata JSONB -- Store keys/parameters if needed
);

CREATE TABLE chat_participants (
    chat_id UUID REFERENCES chats(chat_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (chat_id, user_id)
);
-- No separate index needed due to PK covering chat_id

CREATE TABLE groups (
    group_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    picture_url VARCHAR(255),
    privacy_type privacy_type_enum NOT NULL,
    invite_link_token VARCHAR(50) UNIQUE, -- Securely generated token for private/secret links
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb -- e.g., specific group rules, linked channel_id
);

CREATE INDEX idx_groups_name ON groups(name); -- For searching public groups
CREATE INDEX idx_groups_privacy_type ON groups(privacy_type);
CREATE INDEX idx_groups_creator_id ON groups(creator_id); -- For counting created groups

CREATE TABLE group_members (
    group_id UUID REFERENCES groups(group_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member', -- 'member', 'admin', 'owner'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    PRIMARY KEY (group_id, user_id)
);

CREATE INDEX idx_group_members_user_id ON group_members(user_id); -- Find groups a user is in

CREATE TABLE channels (
    channel_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    picture_url VARCHAR(255),
    privacy_type privacy_type_enum NOT NULL,
    invite_link_token VARCHAR(50) UNIQUE, -- Securely generated token for private/secret links
    subscriber_count INT DEFAULT 0, -- Denormalized for efficiency
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok',
    linked_group_id UUID REFERENCES groups(group_id) ON DELETE SET NULL UNIQUE, -- Link to discussion group
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb
);

CREATE INDEX idx_channels_name ON channels(name); -- For searching public channels
CREATE INDEX idx_channels_privacy_type ON channels(privacy_type);
CREATE INDEX idx_channels_creator_id ON channels(creator_id); -- For counting created channels

CREATE TABLE channel_subscribers (
    channel_id UUID REFERENCES channels(channel_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'subscriber', -- 'subscriber', 'admin', 'owner'
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (channel_id, user_id)
);

CREATE INDEX idx_channel_subscribers_user_id ON channel_subscribers(user_id);

-- ==================================================
-- Media & Content Tables
-- ==================================================

CREATE TABLE media (
    media_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    uploader_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    media_type VARCHAR(30) NOT NULL, -- 'image', 'video', 'audio', 'document', 'story_base', 'story_overlay' etc.
    file_url VARCHAR(255) NOT NULL, -- URL to object storage (S3, R2, B2 etc.)
    thumbnail_url VARCHAR(255),
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    duration INT, -- For audio/video in seconds
    width INT, -- For images/videos
    height INT, -- For images/videos
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok', -- Status from AI scan / reports
    scan_results JSONB, -- Store results from AI moderation service
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb -- e.g., blurhash, encryption keys, EXIF data summary
);

CREATE INDEX idx_media_uploader_user_id ON media(uploader_user_id);
CREATE INDEX idx_media_type ON media(media_type);
CREATE INDEX idx_media_moderation_status ON media(moderation_status);

-- Messages Table (Core communication)
CREATE TABLE messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- Conversation context (only one should be non-null)
    chat_id UUID REFERENCES chats(chat_id) ON DELETE CASCADE,
    group_id UUID REFERENCES groups(group_id) ON DELETE CASCADE,
    channel_id UUID REFERENCES channels(channel_id) ON DELETE CASCADE,
    -- Message details
    sender_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    text_content TEXT, -- Encrypted if E2EE chat/group
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE, -- Soft delete flag
    deleted_at TIMESTAMP WITH TIME ZONE,
    -- Relationships
    reply_to_message_id UUID REFERENCES messages(message_id) ON DELETE SET NULL,
    forwarded_from_message_id UUID REFERENCES messages(message_id) ON DELETE SET NULL,
    -- Status and Metadata
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok', -- Status from AI scan / reports
    scan_results JSONB, -- Store results from AI moderation service
    metadata JSONB DEFAULT '{}'::jsonb, -- E.g., Link previews, poll data
    -- Check constraint
    CHECK(
        (chat_id IS NOT NULL AND group_id IS NULL AND channel_id IS NULL) OR
        (chat_id IS NULL AND group_id IS NOT NULL AND channel_id IS NULL) OR
        (chat_id IS NULL AND group_id IS NULL AND channel_id IS NOT NULL)
    )
);

-- Indexes for efficient message retrieval per conversation
CREATE INDEX idx_messages_chat_id_sent_at ON messages(chat_id, sent_at DESC) WHERE chat_id IS NOT NULL;
CREATE INDEX idx_messages_group_id_sent_at ON messages(group_id, sent_at DESC) WHERE group_id IS NOT NULL;
CREATE INDEX idx_messages_channel_id_sent_at ON messages(channel_id, sent_at DESC) WHERE channel_id IS NOT NULL;
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_sent_at ON messages(sent_at DESC);
CREATE INDEX idx_messages_moderation_status ON messages(moderation_status);

-- Junction table for messages with multiple media items
CREATE TABLE message_media (
    message_id UUID REFERENCES messages(message_id) ON DELETE CASCADE,
    media_id UUID REFERENCES media(media_id) ON DELETE CASCADE,
    display_order SMALLINT DEFAULT 0, -- If order matters
    PRIMARY KEY (message_id, media_id)
);
CREATE INDEX idx_message_media_media_id ON message_media(media_id);

-- Per-recipient status (Delivered/Read)
CREATE TABLE message_status (
    message_id UUID REFERENCES messages(message_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL, -- 'delivered', 'read'
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (message_id, user_id, status) -- Composite key allows storing both delivered and read times if needed
);
CREATE INDEX idx_message_status_user_id_message_id ON message_status(user_id, message_id);

-- Stories (Replacing previous Status Updates)
CREATE TABLE stories (
    story_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    base_media_id UUID NOT NULL REFERENCES media(media_id) ON DELETE RESTRICT, -- Main photo/video for the story
    caption TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL -- Usually created_at + 24 hours
    -- Privacy scope handled by user settings or potentially a visibility table if needed
);

CREATE INDEX idx_stories_user_id_expires_at ON stories(user_id, expires_at DESC);
CREATE INDEX idx_stories_expires_at ON stories(expires_at); -- For cleanup job

CREATE TYPE story_element_type_enum AS ENUM ('text', 'overlay_media', 'music', 'location', 'reaction_video');

CREATE TABLE story_elements (
    element_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES stories(story_id) ON DELETE CASCADE,
    element_type story_element_type_enum NOT NULL,
    -- Positioning and styling (adapt based on UI needs)
    position_x REAL,
    position_y REAL,
    width REAL,
    height REAL,
    rotation REAL,
    z_index SMALLINT DEFAULT 0, -- Layer order
    -- Element specific data stored in JSONB
    element_data JSONB NOT NULL -- Examples:
    -- {'text': 'Hello!', 'color': '#FFFFFF', 'font_size': 16}
    -- {'media_id': 'uuid-of-overlay-image', 'type': 'image'}
    -- {'media_id': 'uuid-of-reaction-video', 'type': 'video'}
    -- {'track_id': 'spotify:track:xyz', 'provider': 'spotify', 'start_ms': 15000, 'duration_ms': 30000}
    -- {'latitude': 48.8584, 'longitude': 2.2945, 'name': 'Eiffel Tower', 'city': 'Paris'}
);

CREATE INDEX idx_story_elements_story_id ON story_elements(story_id);

-- Track story views
CREATE TABLE story_views (
    story_id UUID REFERENCES stories(story_id) ON DELETE CASCADE,
    viewer_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (story_id, viewer_id)
);
CREATE INDEX idx_story_views_viewer_id ON story_views(viewer_id);

-- Posts (X-like feature, if still needed, largely unchanged)
CREATE TABLE posts (
    post_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    posted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE,
    reply_to_post_id UUID REFERENCES posts(post_id) ON DELETE SET NULL,
    likes_count INT DEFAULT 0, -- Denormalized
    reposts_count INT DEFAULT 0, -- Denormalized
    comments_count INT DEFAULT 0, -- Denormalized
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok',
    scan_results JSONB,
    visibility VARCHAR(20) DEFAULT 'public' -- 'public', 'followers', 'private'
);
-- Add appropriate indexes similar to previous versions if using posts

-- ==================================================
-- Moderation & Reporting Tables
-- ==================================================

CREATE TYPE reportable_entity_type_enum AS ENUM ('message', 'post', 'media', 'story', 'user', 'group', 'channel');
CREATE TYPE report_reason_enum AS ENUM ('racism', 'sexual_content', 'threats', 'violence_harm', 'hateful_content', 'terrorism_extremism', 'harassment_bullying', 'spam', 'other');
CREATE TYPE report_status_enum AS ENUM ('submitted', 'under_review', 'action_taken_ban', 'action_taken_warn', 'dismissed');

CREATE TABLE reports (
    report_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reporter_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    reported_entity_type reportable_entity_type_enum NOT NULL,
    reported_entity_id UUID NOT NULL, -- ID of the message, post, user etc.
    reported_content_snapshot TEXT, -- Optional: snippet of text content at time of report
    reason report_reason_enum NOT NULL,
    details TEXT, -- User provided details
    status report_status_enum NOT NULL DEFAULT 'submitted',
    reviewed_by_admin_id UUID REFERENCES users(user_id), -- Link to admin user who reviewed
    review_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_reports_status ON reports(status);
CREATE INDEX idx_reports_reported_entity ON reports(reported_entity_type, reported_entity_id);
CREATE INDEX idx_reports_reporter ON reports(reporter_user_id);

-- Table to track banned entities (can query this before allowing interaction)
CREATE TABLE banned_entities (
    entity_type reportable_entity_type_enum NOT NULL,
    entity_id UUID NOT NULL, -- ID of the banned group, channel, user etc.
    reason report_reason_enum,
    ban_details TEXT,
    banned_by_admin_id UUID REFERENCES users(user_id),
    banned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE, -- For temporary bans
    PRIMARY KEY (entity_type, entity_id)
);

CREATE INDEX idx_banned_entities_expires_at ON banned_entities(expires_at);

-- ==================================================
-- Call Related Tables
-- ==================================================

CREATE TABLE call_logs (
    call_log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    caller_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    callee_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_seconds INT, -- Calculated on call end
    call_type VARCHAR(10) NOT NULL, -- 'audio', 'video'
    status VARCHAR(20) NOT NULL -- 'initiated', 'answered', 'missed', 'ended', 'failed'
);

CREATE INDEX idx_call_logs_caller_id ON call_logs(caller_id, start_time DESC);
CREATE INDEX idx_call_logs_callee_id ON call_logs(callee_id, start_time DESC);

-- Table to track monthly call usage for non-Gold users (aggregate periodically)
CREATE TABLE monthly_call_usage (
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    usage_month DATE NOT NULL, -- First day of the month
    total_duration_seconds INT NOT NULL DEFAULT 0,
    PRIMARY KEY (user_id, usage_month)
);

-- ==================================================
-- Denormalized Chat List Table (Crucial for Performance)
-- ==================================================

CREATE TABLE user_conversations (
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL, -- chat_id, group_id, or channel_id
    conversation_type VARCHAR(10) NOT NULL, -- 'chat', 'group', 'channel'
    privacy_type privacy_type_enum, -- Store group/channel privacy for potential UI hints
    -- Denormalized data for quick display
    conversation_name VARCHAR(100),
    conversation_avatar_url VARCHAR(255),
    last_message_id UUID,
    last_message_sender_id UUID,
    last_message_sender_handle VARCHAR(9), -- Use handle for display consistency
    last_message_preview TEXT,
    last_activity_timestamp TIMESTAMP WITH TIME ZONE,
    unread_count INT DEFAULT 0,
    is_muted BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (user_id, conversation_id)
);

-- Essential index for fetching a user's chat list
CREATE INDEX idx_user_conversations_user_activity ON user_conversations(user_id, is_pinned DESC, last_activity_timestamp DESC);
CREATE INDEX idx_user_conversations_conversation_id ON user_conversations(conversation_id); -- To find entries for updates

-- ==================================================
-- Support & Static Content Tables (Optional)
-- ==================================================

CREATE TABLE support_tickets (
    ticket_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL, -- Allow anonymous/deleted user tickets?
    subject VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'open', -- 'open', 'in_progress', 'closed', 'resolved'
    priority SMALLINT DEFAULT 3, -- 1 (high) to 5 (low)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);

CREATE TABLE support_messages (
    support_message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID NOT NULL REFERENCES support_tickets(ticket_id) ON DELETE CASCADE,
    sender_user_id UUID REFERENCES users(user_id), -- Null if from support staff without a user account
    sender_type VARCHAR(10) NOT NULL, -- 'user', 'admin', 'system'
    message_body TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_support_messages_ticket_id_sent_at ON support_messages(ticket_id, sent_at);

```

**Explanation of Key Additions/Changes:**

1.  **`users` Table:**
    *   `user_handle` added for the unique 9-char ID. Requires application-level generation and uniqueness check.
    *   `phone_number`/`email` potentially nullable depending on signup requirements.
    *   Added fields for recovery (`recovery_phrase_hash`, `recovery_pin_hash`), remote wipe (`remote_wipe_pin_hash`), subscription (`subscription_tier`), verification (`verification_status`), and deletion scheduling (`scheduled_deletion_at`).
    *   `settings` JSONB holds many user-configurable options (2FA, permissions, notifications, etc.).
2.  **`payments` & `subscriptions` Tables:** Added to handle "Adhésion Gold" and per-action payments (creating extra groups/channels). Requires integration with a payment processor (Stripe, PayPal, etc.).
3.  **`groups` & `channels` Tables:** `is_public` replaced with `privacy_type` enum ('public', 'private', 'secret'). `moderation_status` added. `invite_link_token` added for private/secret access. `linked_group_id` added to channels.
4.  **Group/Channel Limits:** Instead of a dedicated tracking table, the logic to enforce the limit (2 free public/private, pay for more or secret) would likely reside in the application layer. It would query the count of existing groups/channels created by the user (`SELECT COUNT(*) FROM groups WHERE creator_id = ? AND privacy_type IN ('public', 'private')`) before allowing creation or initiating a payment flow.
5.  **Moderation:** Added `moderation_status` and `scan_results` to `messages`, `media`, `posts`, `groups`, `channels`. Requires external AI services to populate `scan_results` and update `moderation_status`.
6.  **`reports` & `banned_entities` Tables:** Added for user reporting and tracking banned content/users/groups/channels.
7.  **Stories:** Replaced `status_updates`/`status_views` with more complex `stories`, `story_elements` (for overlays, text, music, location), and `story_views`. Requires significant application logic to handle element composition and rendering.
8.  **Calls:** Added `call_logs` and `monthly_call_usage` to track call history and enforce limits for non-Gold users. Requires application logic to check usage before initiating calls and update usage logs.
9.  **Security:** Added fields for recovery/wipe hashes. The actual implementation of these flows (generating phrases, verifying PINs, secure storage) is critical and happens in the application layer.
10. **`user_conversations`:** Remains crucial for chat list performance. Needs to be updated diligently by the application based on all relevant events (new messages, reads, mutes, metadata changes).

**Next Steps & Considerations:**

1.  **External Services:** This schema *relies* on external services:
    *   **Payment Processor:** Stripe, PayPal, etc.
    *   **AI Moderation:** Google Cloud Vision/Video Intelligence/Natural Language, AWS Rekognition/Comprehend, or specialized third-party APIs.
    *   **Object Storage:** S3, Google Cloud Storage, Cloudflare R2, Backblaze B2.
    *   **Push Notifications:** APNS (Apple), FCM (Google).
    *   **Real-time:** WebSockets (managed via your backend) + Redis Pub/Sub.
    *   **Call Infrastructure:** WebRTC servers (STUN/TURN) or a CPaaS provider (Twilio, Vonage) for handling audio/video call signalling and media relay.
2.  **Security Implementation:** Hashing passwords, recovery phrases, PINs correctly (use strong algorithms like Argon2 or bcrypt). Encrypting sensitive data at rest (like TOTP secrets, potentially PII). Implementing robust authentication and authorization checks at the API layer. Input validation. Rate limiting.
3.  **Application Logic:** Implementing the algorithms discussed previously (updating `user_conversations`), generating unique handles, handling payment flows, managing call duration limits, interacting with moderation services, processing reports, handling recovery/wipe flows, enforcing permissions.
4.  **Background Jobs:** Setting up background workers (Celery, BullMQ, Asynq) for tasks like: sending notifications, processing moderation results asynchronously, cleaning up expired stories/deletion requests, aggregating call usage, handling webhook callbacks from payment processors.
5.  **Indexing & Optimization:** Continuously monitor query performance (`EXPLAIN ANALYZE`) and add/tune indexes as needed based on real-world usage patterns.

This schema provides a comprehensive structure, but the complexity lies significantly in the application logic, external integrations, security practices, and infrastructure needed to bring it to life robustly.