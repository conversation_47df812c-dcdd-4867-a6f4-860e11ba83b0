# Backend Analysis & Refactoring Plan

## I. Executive Summary & Goals

The primary goals of this refactoring effort are:

1.  **Enhance Security:** Address critical vulnerabilities in PIN storage, recovery token storage, and strengthen token handling.
2.  **Fix Core Authentication Bugs:** Resolve issues causing premature token expiration and broken refresh token mechanisms, leading to unexpected logouts and login problems.
3.  **Improve Maintainability:** Refactor configuration loading, context propagation, and standardize error handling.
4.  **Strengthen Token Management:** Implement robust logout functionality and complete the password reset feature.
5.  **Improve Performance (Minor Focus initially):** Optimize configuration loading. Further performance analysis can be a next step.
6.  **Enhance Error Monitoring & Documentation:** Ensure clear logging and update database schema documentation.

## II. Identified Issues & Proposed Solutions

Here's a breakdown by category:

**A. Critical Security Vulnerabilities**

1.  **Issue:** Recovery PINs are stored and compared in plaintext.
    *   **Files Affected:** [`backend/internal/database/user_repository.go:106`](../backend/internal/database/user_repository.go:106) (storage), [`backend/internal/database/user_repository.go:490`](../backend/internal/database/user_repository.go:490) (comparison).
    *   **Risk:** High. Database compromise exposes user PINs.
    *   **Solution:**
        *   Hash recovery PINs using `bcrypt` before storing them in the `users` table (similar to `password_hash` and `recovery_phrase_hash`).
        *   Update `UserRepository.VerifyRecoveryInfo()` ([`user_repository.go:472`](../backend/internal/database/user_repository.go:472)) to compare the hashed input PIN with the stored hash.
        *   **Database Change:** The `recovery_pin` column in the `users` table will now store a hash. Existing plaintext PINs will need a migration strategy (e.g., force users to set a new PIN upon next recovery attempt or a proactive reset campaign if feasible). Given it's for recovery, a simpler approach might be to invalidate old PINs and require users to set them up again if they use the recovery feature.
        *   **Plan Note:** Update [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) and [`docs/database/master_schema.sql`](master_schema.sql) to reflect that `recovery_pin` stores a hash (e.g., by renaming to `recovery_pin_hash` or documenting its contents).

2.  **Issue:** Account recovery tokens are stored and queried in plaintext.
    *   **Files Affected:** [`backend/internal/database/user_repository.go:498-508`](../backend/internal/database/user_repository.go:498-508) (storage), [`backend/internal/database/user_repository.go:511-525`](../backend/internal/database/user_repository.go:511-525) (verification), [`backend/internal/database/user_repository.go:528-535`](../backend/internal/database/user_repository.go:528-535) (marking used).
    *   **Risk:** Medium-High. Database compromise exposes active recovery tokens.
    *   **Solution:**
        *   Hash recovery tokens (e.g., using `utils.HashToken()` or a similar SHA-256 approach) before storing them in the `recovery_tokens` table.
        *   Update `UserRepository.VerifyRecoveryToken()` and `MarkRecoveryTokenAsUsed()` to query using the hashed token.
        *   **Database Change:** The `token` column in `recovery_tokens` will store `token_hash`.
        *   **Plan Note:** Update [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) (table `recovery_tokens` [`initialize_database_fixed.sql:347`](../scripts/initialize_database_fixed.sql:347)) and [`docs/database/master_schema.sql`](master_schema.sql). Rename the column to `token_hash`.

**B. Critical Bugs Affecting Authentication Stability**

1.  **Issue:** Refresh Token `used_at` column mismatch between database schema and Go code.
    *   **Files Affected:** [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) (missing column), [`backend/internal/database/refresh_token_repository.go`](../backend/internal/database/refresh_token_repository.go) (queries for non-existent column), [`backend/internal/models/refresh_token.go`](../backend/internal/models/refresh_token.go) (model includes it).
    *   **Impact:** Token refresh is completely broken, leading to forced re-logins. This is highly likely the cause of the user-reported issue of being "logged out" and needing to log in again.
    *   **Solution:**
        *   Add the `used_at TIMESTAMP WITH TIME ZONE NULL` column to the `refresh_tokens` table definition in [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) (lines [`199-208`](../scripts/initialize_database_fixed.sql:199-208)) and [`docs/database/master_schema.sql`](master_schema.sql).
        *   Ensure `RefreshTokenRepository` queries in [`backend/internal/database/refresh_token_repository.go`](../backend/internal/database/refresh_token_repository.go) correctly reference this column. (The Go code is already trying to use it, so it should work once the column exists).
        *   **Plan Note:** This requires a database schema migration for existing deployments.

2.  **Issue:** Refresh token `expires_at` in the database is set using the access token's (shorter) lifetime.
    *   **Files Affected:** [`backend/internal/services/auth_service.go`](../backend/internal/services/auth_service.go) (lines [`64`](../backend/internal/services/auth_service.go:64), [`119`](../backend/internal/services/auth_service.go:119), [`195`](../backend/internal/services/auth_service.go:195), [`301`](../backend/internal/services/auth_service.go:301)), [`backend/internal/middleware/auth.go:170`](../backend/internal/middleware/auth.go:170) (returns access token expiry).
    *   **Impact:** Database considers refresh tokens expired prematurely, contributing to refresh failures.
    *   **Solution:**
        *   Modify `middleware.GenerateTokens()` ([`auth.go:124`](../backend/internal/middleware/auth.go:124)) to return both the access token's `expiresIn` seconds AND the actual `refreshExpirationTime` (as a `time.Time` object or its Unix timestamp).
        *   Update `AuthService` calls to `refreshTokenRepo.StoreToken()` to pass the correct `refreshExpirationTime`.

**C. Other Security & Token Management Improvements**

1.  **Issue:** Login error messages could allow user enumeration.
    *   **File Affected:** [`backend/internal/services/auth_service.go:35-37`](../backend/internal/services/auth_service.go:35-37).
    *   **Solution:** Change the error message at [`auth_service.go:37`](../backend/internal/services/auth_service.go:37) from "invalid credentials: user not found" to a generic "invalid credentials" to match the incorrect password message ([`auth_service.go:46`](../backend/internal/services/auth_service.go:46)).

2.  **Issue:** No explicit server-side logout functionality.
    *   **Solution:**
        *   Create a new `/auth/logout` endpoint and handler in [`backend/internal/api/auth_handler.go`](../backend/internal/api/auth_handler.go).
        *   The handler should:
            *   Extract the refresh token (if provided by the client, e.g., in the request body).
            *   Call a new method in `AuthService` (e.g., `Logout(refreshTokenString string)`).
            *   The `AuthService.Logout` method should:
                *   Hash the provided refresh token.
                *   Call `refreshTokenRepo.RevokeToken()` ([`refresh_token_repository.go:67`](../backend/internal/database/refresh_token_repository.go:67)) or `MarkTokenAsUsed()` to invalidate it. Using `RevokeToken` is more explicit for logout.
                *   Consider if all user's refresh tokens should be revoked on logout: `refreshTokenRepo.RevokeAllUserTokens()` ([`refresh_token_repository.go:78`](../backend/internal/database/refresh_token_repository.go:78)). This is a stricter security measure.
        *   Client should clear its stored tokens upon successful logout.

3.  **Issue:** Placeholder password reset functionality.
    *   **Files Affected:** [`backend/internal/services/auth_service.go:223-234`](../backend/internal/services/auth_service.go:223-234).
    *   **Solution:**
        *   **RequestPasswordReset (`auth_service.go:223`):**
            *   Find user by email.
            *   Generate a secure, unique, time-limited (e.g., 15-60 minutes) password reset token.
            *   Hash this token and store it in `recovery_tokens` (similar to account recovery tokens, perhaps with a different type/purpose flag if needed, or ensure current table can handle it). The current `recovery_tokens` table seems suitable.
            *   Send an email to the user with a link containing this plaintext token (e.g., `https://yourdomain.com/reset-password?token=THE_TOKEN`).
        *   **ResetPassword (`auth_service.go:230`):**
            *   The handler for the link above will call this service method.
            *   Verify the provided token (hash it, check against `recovery_tokens`, ensure not expired, not used).
            *   If valid, allow the user to set a new password (hash it and update in `users` table).
            *   Mark the reset token as used in `recovery_tokens`.
            *   Invalidate all active refresh tokens for the user for security.
        *   **Plan Note:** This involves external email sending capabilities.

4.  **Issue:** Refresh token hashing uses `SHA-256` without a salt.
    *   **File Affected:** [`backend/internal/utils/token_utils.go:9`](../backend/internal/utils/token_utils.go:9).
    *   **Solution (Optional Enhancement):**
        *   Modify `HashToken` to accept a salt.
        *   Generate a unique salt per token and store it alongside the `token_hash` in the `refresh_tokens` table.
        *   Alternatively, use a system-wide pepper from `config.go` if per-token salt is too complex for this stage.
        *   **Database Change:** Add `token_salt` column to `refresh_tokens` if per-token salt is chosen.
        *   **Plan Note:** Update [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) and [`docs/database/master_schema.sql`](master_schema.sql). This is lower priority than the critical bugs.

5.  **Issue:** Default JWT secret is weak.
    *   **File Affected:** [`backend/internal/config/config.go:70`](../backend/internal/config/config.go:70).
    *   **Solution (Operational):** Ensure a strong, random `JWT_SECRET` is set in the production environment. This is not a code change but an operational requirement. Add a prominent note in documentation.

**D. Maintainability & Performance**

1.  **Issue:** Inefficient configuration loading.
    *   **Files Affected:** [`backend/internal/config/config.go:60`](../backend/internal/config/config.go:60), [`backend/internal/middleware/auth.go`](../backend/internal/middleware/auth.go) (multiple calls).
    *   **Solution:**
        *   Load configuration once at application startup in [`backend/cmd/api/main.go`](../backend/cmd/api/main.go).
        *   Pass the loaded `Config` struct to handlers and services via dependency injection (e.g., make it part of `AuthService` struct, or part of a larger application context struct).
        *   Remove direct calls to `config.LoadConfig()` from middleware and services.

2.  **Issue:** `context.Background()` used instead of propagating request contexts.
    *   **Files Affected:** [`backend/internal/services/auth_service.go`](../backend/internal/services/auth_service.go) (e.g., lines [`29`](../backend/internal/services/auth_service.go:29), [`93`](../backend/internal/services/auth_service.go:93), etc.).
    *   **Solution:**
        *   Modify service methods (e.g., `Login`, `Register` in `AuthService`) to accept `ctx context.Context` as their first parameter.
        *   Pass the context from the Gin handlers (e.g., `c.Request.Context()`) to these service methods.
        *   The repository methods already accept `context.Context`, so this change primarily affects the service layer.

3.  **Issue:** Minor error handling inconsistencies (silent errors, different error types).
    *   **Files Affected:** [`backend/internal/services/auth_service.go`](../backend/internal/services/auth_service.go) (e.g., silent errors on `UpdateLastSeen` [`auth_service.go:50-53`](../backend/internal/services/auth_service.go:50-53)).
    *   **Solution:**
        *   Review all instances where errors are logged and ignored. Decide if these errors should be propagated or handled more explicitly. For `UpdateLastSeen`, current behavior might be acceptable if it's non-critical. For `MarkRecoveryTokenAsUsed` ([`auth_service.go:281-284`](../backend/internal/services/auth_service.go:281-284)), failure is more problematic and should likely return an error.
        *   Consider defining custom error types or using error wrapping more consistently for better upstream handling.

**E. Database Schema Discrepancies & Cleanup (related to `refresh_tokens`)**

1.  **Issue:** `refresh_tokens` table in DB has `issued_at` (timestamp) and `is_revoked` (boolean), but Go model `models.RefreshToken` ([`refresh_token.go:10`](../backend/internal/models/refresh_token.go:10)) lacks `IssuedAt` and uses `RevokedAt *time.Time` (which matches DB `revoked_at`) but doesn't have `IsRevoked`.
    *   **Solution:**
        *   Add `IssuedAt time.Time` to `models.RefreshToken`.
        *   Add `IsRevoked bool` to `models.RefreshToken`.
        *   Update `RefreshTokenRepository.GetToken()` ([`refresh_token_repository.go:31`](../backend/internal/database/refresh_token_repository.go:31)) to select `issued_at` and `is_revoked`.
        *   Update `RefreshTokenRepository.StoreToken()` ([`refresh_token_repository.go:21`](../backend/internal/database/refresh_token_repository.go:21)) to also insert `issued_at` (can be `time.Now()` or derived from JWT if preferred, though DB default is fine) and `is_revoked` (default `false`).
        *   The `IsValid()` method in `models.RefreshToken` ([`refresh_token.go:21`](../backend/internal/models/refresh_token.go:21)) should check `IsRevoked` field instead of/in addition to `RevokedAt == nil`.
        *   **Plan Note:** Update [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) and [`docs/database/master_schema.sql`](master_schema.sql) if any column definitions need to change (though `issued_at` and `is_revoked` are already there). This is mostly about aligning the Go model and repository code.

## III. Refactoring Steps & Prioritization

The following order is recommended:

1.  **P0: Fix Critical Database Schema/Code Mismatches:**
    *   Add `used_at` column to `refresh_tokens` table in SQL scripts and apply migration. (Addresses B.1)
    *   Align `models.RefreshToken` with `refresh_tokens` DB schema (`issued_at`, `is_revoked`). (Addresses E.1)
2.  **P0: Fix Critical Security Vulnerabilities:**
    *   Hash Recovery PINs (storage and verification). (Addresses A.1)
    *   Hash Recovery Tokens (storage and verification). (Addresses A.2)
3.  **P0: Fix Critical Token Logic Bugs:**
    *   Correct `expires_at` for refresh tokens in `AuthService.StoreToken()`. (Addresses B.2)
4.  **P1: Implement Core Missing Features:**
    *   Implement proper server-side Logout functionality. (Addresses C.2)
    *   Implement Password Reset functionality (token generation, email, verification). (Addresses C.3)
5.  **P1: Improve System Stability & Maintainability:**
    *   Refactor configuration loading to be done once at startup. (Addresses D.1)
    *   Propagate `context.Context` correctly through service layer. (Addresses D.2)
    *   Make login error messages generic. (Addresses C.1)
6.  **P2: Further Enhancements & Cleanup:**
    *   Review and improve error handling for silent/ignored errors. (Addresses D.3)
    *   Consider salting refresh token hashes (optional). (Addresses C.4)
    *   Ensure production `JWT_SECRET` is strong (operational task, document this). (Addresses C.5)

## IV. Documentation & Monitoring

*   **Database Documentation:**
    *   Update [`docs/database/master_schema.sql`](master_schema.sql) to reflect all schema changes (e.g., `used_at` column, `recovery_pin` storing hash, `recovery_tokens.token` storing hash).
    *   Update [`scripts/initialize_database_fixed.sql`](../scripts/initialize_database_fixed.sql) to match the master schema.
    *   Update [`scripts/README.md`](../scripts/README.md) if any script usage changes.
    *   The `generate_schema_files.sh` ([`scripts/generate_schema_files.sh`](../scripts/generate_schema_files.sh)) script should be re-run after `master_schema.sql` is updated to regenerate individual schema files in `backend/internal/database/schema/`.
*   **API Documentation:** Update API documentation (e.g., OpenAPI specs in `docs/openapi/`) for any new endpoints (like `/logout`) or changes in request/response for existing ones.
*   **Error Monitoring:**
    *   Ensure comprehensive logging, especially around authentication flows. Use structured logging (e.g., logrus, zap) if not already in place (current logging seems to be standard `log` package).
    *   The `DatadogConfig` ([`config.go:22`](../backend/internal/config/config.go:22)) suggests Datadog is intended for use. Ensure critical errors and auth events (logins, logouts, refresh attempts, failures) are sent to Datadog for monitoring and alerting.
*   **Code Comments:** Add comments to explain complex logic, especially around security-sensitive areas.

## V. Mermaid Diagrams (Illustrative)

**A. Proposed Refresh Token Flow (High-Level)**

```mermaid
sequenceDiagram
    participant Client
    participant APIServer
    participant AuthService
    participant RefreshTokenRepo
    participant DB

    Client->>APIServer: POST /auth/refresh (RefreshToken_A)
    APIServer->>AuthService: RefreshToken(RefreshToken_A)
    AuthService->>AuthService: Parse JWT (RefreshToken_A)
    alt Token Invalid/Expired (JWT)
        AuthService-->>APIServer: Error (Unauthorized)
        APIServer-->>Client: 401 Unauthorized
    else Token Valid (JWT)
        AuthService->>RefreshTokenRepo: GetToken(Hash(RefreshToken_A))
        RefreshTokenRepo->>DB: SELECT ... WHERE token_hash = Hash(RefreshToken_A)
        DB-->>RefreshTokenRepo: StoredTokenData (includes expires_at, used_at, is_revoked)
        RefreshTokenRepo-->>AuthService: StoredTokenData
        alt StoredToken Invalid (DB: expired, used, or revoked)
            AuthService-->>APIServer: Error (Invalid Refresh Token)
            APIServer-->>Client: 401 Unauthorized
        else StoredToken Valid (DB)
            AuthService->>RefreshTokenRepo: MarkTokenAsUsed(Hash(RefreshToken_A))
            RefreshTokenRepo->>DB: UPDATE refresh_tokens SET used_at = NOW() WHERE token_hash = Hash(RefreshToken_A)
            DB-->>RefreshTokenRepo: Success/Error
            AuthService->>AuthService: GenerateTokens() -> AccessToken_B, RefreshToken_B
            AuthService->>RefreshTokenRepo: StoreToken(UserID, Hash(RefreshToken_B), Correct_RefreshToken_Expiry)
            RefreshTokenRepo->>DB: INSERT new RefreshToken_B data
            DB-->>RefreshTokenRepo: Success/Error
            AuthService-->>APIServer: AuthResponse (AccessToken_B, RefreshToken_B)
            APIServer-->>Client: 200 OK (New Tokens)
        end
    end
```

**B. Proposed Logout Flow (High-Level)**

```mermaid
sequenceDiagram
    participant Client
    participant APIServer
    participant AuthService
    participant RefreshTokenRepo
    participant DB

    Client->>APIServer: POST /auth/logout (Optionally with RefreshToken_Current)
    APIServer->>AuthService: Logout(RefreshToken_Current)
    AuthService->>RefreshTokenRepo: RevokeToken(Hash(RefreshToken_Current))
    Note over AuthService,RefreshTokenRepo: Or RevokeAllUserTokens(UserID)
    RefreshTokenRepo->>DB: UPDATE refresh_tokens SET is_revoked=true, revoked_at=NOW() WHERE token_hash = ...
    DB-->>RefreshTokenRepo: Success
    RefreshTokenRepo-->>AuthService: Success
    AuthService-->>APIServer: Success (e.g., 204 No Content)
    APIServer-->>Client: 204 No Content
    Client->>Client: Clear local tokens