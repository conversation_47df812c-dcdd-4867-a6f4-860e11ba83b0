# docs/openapi/paths/channels.yaml

/channels:
  get:
    summary: List user's channels
    tags:
      - Channels
    security:
      - BearerAuth: []
    responses:
      '200':
        description: A list of channels the user is part of or subscribed to.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ChannelListResponse' # To be defined
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Create a new channel
    tags:
      - Channels
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/CreateChannelRequest' # To be defined
    responses:
      '201':
        description: Channel created successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Channel' # To be defined
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403': # Maybe only certain users can create channels
        $ref: '../components/responses.yaml#/ForbiddenError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/channels/{channel_id}:
  parameters:
    - name: channel_id
      in: path
      required: true
      description: The ID of the channel.
      schema:
        type: string
        format: uuid # Or a custom channel identifier
  get:
    summary: Get channel details
    tags:
      - Channels
    security:
      - BearerAuth: [] # Or maybe public channels don't require auth?
    responses:
      '200':
        description: Detailed information about the channel.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Channel' # To be defined
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError' # If private channel and user not member
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  put:
    summary: Update channel settings
    tags:
      - Channels
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/UpdateChannelRequest' # To be defined
    responses:
      '200':
        description: Channel updated successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Channel'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Delete a channel
    description: Only administrators or creators can delete a channel.
    tags:
      - Channels
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Channel deleted successfully.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/channels/{channel_id}/subscribe:
  parameters:
    - name: channel_id
      in: path
      required: true
      description: The ID of the channel to subscribe to.
      schema:
        type: string
        format: uuid
  post:
    summary: Subscribe to a channel
    tags:
      - Channels
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Successfully subscribed to the channel.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError' # e.g., private channel requiring invite
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '409': # Conflict - Already subscribed
        $ref: '../components/responses.yaml#/ConflictError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/channels/{channel_id}/unsubscribe:
  parameters:
    - name: channel_id
      in: path
      required: true
      description: The ID of the channel to unsubscribe from.
      schema:
        type: string
        format: uuid
  post:
    summary: Unsubscribe from a channel
    tags:
      - Channels
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Successfully unsubscribed from the channel.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '409': # Conflict - Not subscribed
        $ref: '../components/responses.yaml#/ConflictError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/channels/{channel_id}/messages:
  parameters:
    - name: channel_id
      in: path
      required: true
      description: The ID of the channel.
      schema:
        type: string
        format: uuid
  get:
    summary: Get channel messages
    tags:
      - Channels
      - Messaging
    security:
      - BearerAuth: []
    parameters:
      # Add pagination parameters (e.g., limit, before_id, after_id)
      - $ref: '../components/parameters.yaml#/LimitParam'
      - $ref: '../components/parameters.yaml#/BeforeIdParam'
      - $ref: '../components/parameters.yaml#/AfterIdParam'
    responses:
      '200':
        description: A list of messages in the channel.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/MessageListResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Send a message to a channel
    description: Only authorized members (e.g., admins) can post in some channels.
    tags:
      - Channels
      - Messaging
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/SendMessageRequest'
    responses:
      '201':
        description: Message sent successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Message'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'