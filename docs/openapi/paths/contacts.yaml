# paths/contacts.yaml
paths:
  /contacts:
    get:
      summary: Get contact list
      tags:
        - Contacts
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
          description: Maximum number of contacts to return.
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of contacts to skip for pagination.
      responses:
        '200':
          description: Contact list retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/ContactListResponse' # Needs definition
        '401':
          description: Unauthorized

  /contacts/favorites:
    get:
      summary: Get favorite contacts
      tags:
        - Contacts
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
          description: Maximum number of favorite contacts to return.
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of favorite contacts to skip for pagination.
      responses:
        '200':
          description: Favorite contacts retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/ContactListResponse'
        '401':
          description: Unauthorized
    post:
      summary: Add a contact
      tags:
        - Contacts
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/AddContactRequest' # Needs definition
      responses:
        '201':
          description: Contact added successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/Contact' # Needs definition
        '400':
          description: Bad Request (e.g., user not found, already a contact)
        '401':
          description: Unauthorized
        '404':
          description: User to add not found.

  /contacts/{contact_user_handle}:
    patch:
      summary: Update contact display name
      tags:
        - Contacts
      security:
        - bearerAuth: []
      parameters:
        - name: contact_user_handle
          in: path
          required: true
          schema:
            type: string
          description: The handle of the contact to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                display_name:
                  type: string
                  description: The new custom display name for the contact.
              required:
                - display_name
      responses:
        '200':
          description: Contact updated successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/Contact'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '404':
          description: Contact not found.
    delete:
      summary: Remove a contact
      tags:
        - Contacts
      security:
        - bearerAuth: []
      parameters:
        - name: contact_user_handle
          in: path
          required: true
          schema:
            type: string
          description: The handle of the contact to remove.
      responses:
        '204':
          description: Contact removed successfully.
        '401':
          description: Unauthorized
        '404':
          description: Contact not found.

  /blocks:
    get:
      summary: Get blocked users list
      tags:
        - Blocking
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
          description: Maximum number of blocked users to return.
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of blocked users to skip for pagination.
      responses:
        '200':
          description: Blocked users list retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/BlockedListResponse' # Needs definition
        '401':
          description: Unauthorized
    post:
      summary: Block a user
      tags:
        - Blocking
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                blocked_user_handle:
                  type: string
                  description: The handle of the user to block.
              required:
                - blocked_user_handle
      responses:
        '204':
          description: User blocked successfully.
        '400':
          description: Bad Request (e.g., cannot block self, user already blocked)
        '401':
          description: Unauthorized
        '404':
          description: User to block not found.

  /blocks/{blocked_user_handle}:
    delete:
      summary: Unblock a user
      tags:
        - Blocking
      security:
        - bearerAuth: []
      parameters:
        - name: blocked_user_handle
          in: path
          required: true
          schema:
            type: string
          description: The handle of the user to unblock.
      responses:
        '204':
          description: User unblocked successfully.
        '401':
          description: Unauthorized
        '404':
          description: Blocked user not found.