# paths/users.yaml
paths:
  /users/me:
    get:
      summary: Get current user's profile
      tags:
        - Users
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/User' # Assuming User schema exists
        '401':
          description: Unauthorized
    patch:
      summary: Update current user's profile
      tags:
        - Users
      security:
        - bearerAuth: []
      requestBody:
        description: Fields to update in the user profile.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                display_name:
                  type: string
                bio:
                  type: string
                profile_picture_url:
                  type: string
                  format: url
              minProperties: 1 # Require at least one field to update
      responses:
        '200':
          description: User profile updated successfully
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/User'
        '400':
          description: Bad Request (e.g., validation error)
        '401':
          description: Unauthorized

  /users/{user_handle}:
    get:
      summary: Get public profile of a user by handle
      tags:
        - Users
      security:
        - bearerAuth: [] # Or optional depending on privacy settings
      parameters:
        - name: user_handle
          in: path
          required: true
          schema:
            type: string
          description: The handle of the user to retrieve.
      responses:
        '200':
          description: Public user profile retrieved successfully
          content:
            application/json:
              schema:
                # Define a specific PublicUser schema if needed, or use User with limited fields
                $ref: '../schemas/schemas.yaml#/components/schemas/User' # Placeholder - adjust schema for public view
        '401':
          description: Unauthorized
        '404':
          description: User not found

  /users/me/settings:
    get:
      summary: Get current user's settings
      tags:
        - Users
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/UserSettings' # Needs definition
        '401':
          description: Unauthorized
    patch:
      summary: Update current user's settings
      tags:
        - Users
      security:
        - bearerAuth: []
      requestBody:
        description: Settings fields to update.
        required: true
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/UserSettingsUpdate' # Needs definition
      responses:
        '200':
          description: User settings updated successfully
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/UserSettings'
        '400':
          description: Bad Request (e.g., validation error)
        '401':
          description: Unauthorized

  /users/me/delete/request:
    post:
      summary: Request account deletion
      tags:
        - Users
      security:
        - bearerAuth: []
      requestBody:
        description: Optional password confirmation.
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  format: password
      responses:
        '202':
          description: Account deletion scheduled
          content:
            application/json:
              schema:
                type: object
                properties:
                  scheduled_deletion_at:
                    type: string
                    format: date-time
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (e.g., incorrect password)
    delete:
      summary: Cancel pending account deletion request
      tags:
        - Users
      security:
        - bearerAuth: []
      responses:
        '204':
          description: Deletion request cancelled successfully
        '401':
          description: Unauthorized
        '404':
          description: No pending deletion request found

  /users/me/remote-wipe/setup:
    post:
      summary: Setup or update the remote wipe PIN
      tags:
        - Users
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  format: password
                  description: Current account password for verification.
                remote_wipe_pin:
                  type: string
                  description: The new PIN for remote wipe.
              required:
                - password
                - remote_wipe_pin
      responses:
        '204':
          description: Remote wipe PIN set/updated successfully
        '400':
          description: Bad Request (e.g., weak PIN)
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (e.g., incorrect password)