# paths/messaging.yaml
paths:
  /conversations:
    get:
      summary: Get user's conversation list
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 30
          description: Maximum number of conversations to return.
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of conversations to skip for pagination.
      responses:
        '200':
          description: Conversation list retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/ConversationListResponse' # Needs definition
        '401':
          description: Unauthorized

  /conversations/{conversation_id}/messages:
    get:
      summary: Get messages for a conversation
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid # Assuming UUID
          description: The ID of the conversation.
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
          description: Maximum number of messages to return.
        - name: before_message_id
          in: query
          schema:
            type: string
            format: uuid # Assuming UUID
          description: Retrieve messages before this message ID (for pagination).
      responses:
        '200':
          description: Messages retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/MessageListResponse' # Needs definition
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (User not part of the conversation)
        '404':
          description: Conversation not found
    post:
      summary: Send a message to a conversation
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the conversation to send the message to.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/SendMessageRequest' # Needs definition
      responses:
        '201':
          description: Message sent successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/Message' # Needs definition
        '400':
          description: Bad Request (e.g., invalid input)
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (User cannot send messages to this conversation)
        '404':
          description: Conversation not found

  /messages/{message_id}:
    patch:
      summary: Edit a message
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: message_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the message to edit.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/EditMessageRequest' # Needs definition
      responses:
        '200':
          description: Message updated successfully.
          content:
            application/json:
              schema:
                $ref: '../schemas/schemas.yaml#/components/schemas/Message'
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (User cannot edit this message)
        '404':
          description: Message not found
    delete:
      summary: Delete a message
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: message_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the message to delete.
      responses:
        '204':
          description: Message deleted successfully.
        '401':
          description: Unauthorized
        '403':
          description: Forbidden (User cannot delete this message)
        '404':
          description: Message not found

  /conversations/{conversation_id}/read:
    post:
      summary: Mark conversation as read
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the conversation to mark as read.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                last_read_message_id:
                  type: string
                  format: uuid
                  description: Optional. Mark as read up to this message ID.
      responses:
        '204':
          description: Conversation marked as read.
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found

  /conversations/{conversation_id}/settings:
    patch:
      summary: Update conversation settings (mute/pin)
      tags:
        - Messaging
      security:
        - bearerAuth: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the conversation to update settings for.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ConversationSettingsUpdate' # Needs definition
      responses:
        '204':
          description: Conversation settings updated.
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '404':
          description: Conversation not found