# docs/openapi/paths/support.yaml

/support/tickets:
  get:
    summary: Get support tickets for the current user
    tags:
      - Support
    security:
      - bearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 20
        description: Maximum number of tickets to return
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
        description: Number of tickets to skip for pagination
    responses:
      '200':
        description: Support tickets retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketsResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Create a new support ticket
    tags:
      - Support
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/CreateTicketRequest'
    responses:
      '201':
        description: Support ticket created successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/support/tickets/{ticket_id}:
  parameters:
    - name: ticket_id
      in: path
      required: true
      description: The ID of the support ticket
      schema:
        type: string
        format: uuid
  get:
    summary: Get a specific support ticket
    tags:
      - Support
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Support ticket retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  put:
    summary: Update a support ticket
    tags:
      - Support
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/UpdateTicketRequest'
    responses:
      '200':
        description: Support ticket updated successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/support/tickets/{ticket_id}/messages:
  parameters:
    - name: ticket_id
      in: path
      required: true
      description: The ID of the support ticket
      schema:
        type: string
        format: uuid
  get:
    summary: Get messages for a support ticket
    tags:
      - Support
    security:
      - bearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 20
        description: Maximum number of messages to return
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
        description: Number of messages to skip for pagination
    responses:
      '200':
        description: Support ticket messages retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketMessagesResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Add a message to a support ticket
    tags:
      - Support
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/AddTicketMessageRequest'
    responses:
      '201':
        description: Message added to support ticket successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/TicketMessageResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
