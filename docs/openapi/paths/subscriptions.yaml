# docs/openapi/paths/subscriptions.yaml

/subscriptions/me:
  get:
    summary: Get the current user's subscription status
    tags:
      - Subscriptions
    security:
      - bearerAuth: []
    responses:
      '200':
        description: User's subscription information retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Subscription'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Cancel the current user's subscription
    tags:
      - Subscriptions
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Subscription cancellation initiated successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Subscription'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/subscriptions/gold/checkout:
  post:
    summary: Initiate the payment process for Gold subscription
    tags:
      - Subscriptions
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/SubscriptionCheckoutRequest'
    responses:
      '200':
        description: Subscription checkout initiated successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/SubscriptionCheckoutResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
