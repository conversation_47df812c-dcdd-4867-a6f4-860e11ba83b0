# docs/openapi/paths/groups.yaml

/groups:
  get:
    summary: List user's groups
    tags:
      - Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: A list of groups the user is part of.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/GroupListResponse' # To be defined
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Create a new group
    tags:
      - Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/CreateGroupRequest' # To be defined
    responses:
      '201':
        description: Group created successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Group' # To be defined
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/groups/{group_id}:
  parameters:
    - name: group_id
      in: path
      required: true
      description: The ID of the group.
      schema:
        type: string
        format: uuid
  get:
    summary: Get group details
    tags:
      - Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: Detailed information about the group.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Group' # To be defined
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  put:
    summary: Update group settings
    tags:
      - Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/UpdateGroupRequest' # To be defined
    responses:
      '200':
        description: Group updated successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Group'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Delete a group (or leave)
    description: Depending on permissions, this might delete the group or just make the user leave.
    tags:
      - Groups
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Group deleted or user left successfully.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/groups/{group_id}/members:
  parameters:
    - name: group_id
      in: path
      required: true
      description: The ID of the group.
      schema:
        type: string
        format: uuid
  get:
    summary: List group members
    tags:
      - Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: A list of members in the group.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/GroupMemberListResponse' # To be defined
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Add members to a group
    tags:
      - Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/AddGroupMembersRequest' # To be defined
    responses:
      '200':
        description: Members added successfully.
        # Potentially return the updated member list or just success
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/groups/{group_id}/members/{user_id}:
  parameters:
    - name: group_id
      in: path
      required: true
      description: The ID of the group.
      schema:
        type: string
        format: uuid
    - name: user_id
      in: path
      required: true
      description: The ID of the user to manage within the group.
      schema:
        type: string
        format: uuid
  put:
    summary: Update member role/permissions
    tags:
      - Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/UpdateGroupMemberRequest' # To be defined
    responses:
      '200':
        description: Member updated successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/GroupMember' # To be defined
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Remove a member from the group (or leave)
    tags:
      - Groups
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Member removed or left successfully.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'