# docs/openapi/paths/calls.yaml

/calls/logs:
  get:
    summary: Get call logs for the current user
    tags:
      - Calls
    security:
      - bearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 20
        description: Maximum number of call logs to return
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
        description: Number of call logs to skip for pagination
    responses:
      '200':
        description: Call logs retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/CallLogsResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/users/me/call-usage:
  get:
    summary: Get remaining call time for the current month (for non-Gold users)
    tags:
      - Calls
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Call usage information retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/CallUsageResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/calls/initiate:
  post:
    summary: Set up signaling for a call (WebRTC)
    tags:
      - Calls
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/InitiateCallRequest'
    responses:
      '200':
        description: Call initiation successful
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/InitiateCallResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/calls/{call_id}/end:
  parameters:
    - name: call_id
      in: path
      required: true
      description: The ID of the call
      schema:
        type: string
        format: uuid
  post:
    summary: Notify the backend that a call has ended
    tags:
      - Calls
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/EndCallRequest'
    responses:
      '204':
        description: Call end notification processed successfully
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
