# docs/openapi/paths/stories.yaml

/stories:
  get:
    summary: Get stories for the current user
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '200':
        description: List of stories created by the current user
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/UserStoriesResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Create a new story
    tags:
      - Stories
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/CreateStoryRequest'
    responses:
      '201':
        description: Story created successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Story'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/stories/feed:
  get:
    summary: Get story feed from contacts
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Story feed grouped by user
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/StoryFeedResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/users/{user_handle}/stories:
  parameters:
    - name: user_handle
      in: path
      required: true
      description: The handle of the user whose stories to retrieve
      schema:
        type: string
  get:
    summary: Get all active stories for a specific user
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '200':
        description: List of active stories for the specified user
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/UserStoriesResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/stories/{story_id}:
  parameters:
    - name: story_id
      in: path
      required: true
      description: The ID of the story
      schema:
        type: string
        format: uuid
  get:
    summary: Get a specific story
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Story details
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Story'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Delete a story
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '204':
        description: Story deleted successfully
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/stories/{story_id}/view:
  parameters:
    - name: story_id
      in: path
      required: true
      description: The ID of the story
      schema:
        type: string
        format: uuid
  post:
    summary: Mark a story as viewed by the current user
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '204':
        description: Story marked as viewed
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/stories/{story_id}/viewers:
  parameters:
    - name: story_id
      in: path
      required: true
      description: The ID of the story
      schema:
        type: string
        format: uuid
  get:
    summary: Get the list of users who viewed the story
    tags:
      - Stories
    security:
      - bearerAuth: []
    responses:
      '200':
        description: List of users who viewed the story
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/StoryViewersResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
