# paths/auth.yaml
paths:
  /auth/register:
    post:
      summary: Register a new user
      description: |
        Register a new user account. For anonymous sign-in, only password is required and the server will generate a unique Meena ID.
        A recovery phrase will be returned that should be securely stored by the client for account recovery.
      tags:
        - Authentication
      security: [] # No authentication required for registration
      x-rate-limit:
        description: Strict rate limiting applied to prevent abuse
        requests: 5
        period: 60 # 5 requests per minute
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/RegisterRequest
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/AuthResponse
        '400':
          description: Bad Request (e.g., validation error, Meena ID already taken)

  /auth/login:
    post:
      summary: Log in a user
      description: |
        Authenticate a user with their Meena ID (or email/phone) and password.
        Returns user profile information and authentication tokens.
      tags:
        - Authentication
      security: [] # No authentication required for login
      x-rate-limit:
        description: Strict rate limiting applied to prevent brute force attacks
        requests: 5
        period: 60 # 5 requests per minute
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/LoginRequest
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/AuthResponse
        '401':
          description: Unauthorized (Invalid credentials)

  /auth/refresh:
    post:
      summary: Refresh authentication tokens
      description: |
        Obtain a new access token using a valid refresh token.
        This endpoint is used when the access token has expired but the refresh token is still valid.
      tags:
        - Authentication
      security:
        - refreshTokenAuth: [] # Uses refresh token for authentication
      x-rate-limit:
        description: Rate limiting to prevent token abuse
        requests: 10
        period: 60 # 10 requests per minute
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/TokenRefreshRequest
      responses:
        '200':
          description: Tokens refreshed successfully
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/AuthResponse
        '401':
          description: Unauthorized (Invalid refresh token)

  /auth/logout:
    post:
      summary: Log out the current user
      description: Invalidates the provided refresh token and logs the user out. The request should be authenticated with a bearer token to identify the user session, and the refresh token to invalidate is passed in the request body.
      tags:
        - Authentication
      security:
        - bearerAuth: []
      requestBody:
        required: true
        description: Refresh token to be invalidated.
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/LogoutRequest
      responses:
        '204':
          description: Logout successful, refresh token invalidated.
        '400':
          description: Bad Request (e.g., refresh token missing or invalid format).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '401':
          description: Unauthorized (e.g., invalid access token).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse

  /auth/initiate-recovery:
    post:
      summary: Initiate account recovery
      description: |
        Initiates the account recovery process using user handle, recovery phrase, and PIN.
        If successful, returns a recovery session token to be used with the /auth/reset-password endpoint.
      tags:
        - Authentication
      security: [] # No authentication required for initiation
      x-rate-limit:
        description: Rate limiting to prevent abuse of recovery attempts
        requests: 5
        period: 300 # 5 requests per 5 minutes
      requestBody:
        required: true
        description: User handle, recovery phrase, and recovery PIN.
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/InitiateRecoveryRequest
      responses:
        '200':
          description: Account recovery initiated successfully.
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/InitiateRecoveryResponse
        '400':
          description: Bad Request (e.g., invalid input, missing fields).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '401':
          description: Unauthorized (e.g., invalid recovery credentials).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '404':
          description: Not Found (e.g., user handle not found).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
  /auth/reset-password: # Path changed from /auth/password-reset/confirm
    post:
      summary: Reset user password using recovery session token
      description: |
        Resets the user's password using the `recovery_session_token` obtained from the
        `/auth/initiate-recovery` endpoint and a new password.
      tags:
        - Authentication
      security: [] # No authentication required for this step, token itself is proof
      requestBody:
        required: true
        description: Recovery session token and new password.
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/ResetPasswordRequest # Updated schema
      responses:
        '200':
          description: Password reset successful.
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/GeneralMessageResponse # Updated response
        '400':
          description: Bad Request (e.g., invalid or expired token, invalid new password format).
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: ../components/responses.yaml#/components/schemas/ErrorResponse

  /auth/recovery/request:
    post:
      summary: Request account recovery
      description: |
        Initiate account recovery using the recovery phrase and PIN.
        This is used when a user has forgotten their password but has their recovery information.
      tags:
        - Authentication
      security: [] # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/AccountRecoveryRequest
      responses:
        '200':
          description: Recovery request successful
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/RecoveryResponse
        '400':
          description: Bad Request (e.g., invalid recovery information)

  /auth/recovery/confirm:
    post:
      summary: Confirm account recovery
      description: |
        Complete the account recovery process using the recovery token and set a new password.
      tags:
        - Authentication
      security: [] # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/RecoveryConfirmRequest
      responses:
        '200':
          description: Recovery successful
          content:
            application/json:
              schema:
                $ref: ../schemas/schemas.yaml#/components/schemas/AuthResponse
        '400':
          description: Bad Request (e.g., invalid token)

  /auth/verify-account:
    post:
      summary: Verify user account (e.g., email or phone)
      description: |
        Verify a user's email address or phone number using a verification token.
        This is used to confirm ownership of the contact information.
      tags:
        - Authentication
      security: [] # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                verification_token:
                  type: string
                  description: Token received via email or SMS.
              required:
                - verification_token
      responses:
        '200':
          description: Account verified successfully
        '400':
          description: Bad Request (e.g., invalid token)

  /auth/password:
    post:
      summary: Change password
      description: |
        Change the password for the authenticated user.
        Requires the current password for verification.
      tags:
        - Authentication
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: ../schemas/schemas.yaml#/components/schemas/PasswordChangeRequest
      responses:
        '200':
          description: Password changed successfully
        '400':
          description: Bad Request (e.g., incorrect current password, weak new password)
        '401':
          description: Unauthorized