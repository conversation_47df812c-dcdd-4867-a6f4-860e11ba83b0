# docs/openapi/paths/media.yaml

/media/upload:
  post:
    summary: Upload a media file
    tags:
      - Media
    security:
      - bearerAuth: []
    x-security-considerations:
      description: |
        This endpoint handles file uploads and requires:
        - File type validation
        - File size limits
        - Malware scanning
        - Content moderation
        - Rate limiting per user
    x-rate-limit:
      description: Rate limiting to prevent abuse
      requests: 20
      period: 60 # 20 requests per minute
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              file:
                type: string
                format: binary
                description: The media file to upload.
              context:
                type: string
                description: Optional context for the upload (e.g., 'profile_picture', 'chat_attachment').
            required:
              - file
    responses:
      '201':
        description: Media uploaded successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/MediaUploadResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError' # e.g., invalid file type, size limit exceeded
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/upload/encrypted:
  post:
    summary: Upload an encrypted media file
    description: |
      Request a pre-signed URL for uploading an encrypted media file.
      The file should be encrypted client-side before uploading.
    tags:
      - Media
      - Encryption
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              file_name:
                type: string
                description: The name of the encrypted file.
              content_type:
                type: string
                description: The content type of the encrypted file (usually application/octet-stream).
              file_size:
                type: integer
                description: The size of the encrypted file in bytes.
              is_encrypted:
                type: boolean
                default: true
                description: Flag indicating that the file is encrypted.
            required:
              - file_name
              - content_type
              - file_size
    responses:
      '200':
        description: Upload URL generated successfully.
        content:
          application/json:
            schema:
              type: object
              properties:
                media_id:
                  type: string
                  description: The ID of the media item.
                upload_url:
                  type: string
                  format: uri
                  description: The pre-signed URL for uploading the encrypted file.
                expires_at:
                  type: string
                  format: date-time
                  description: When the upload URL expires.
              required:
                - media_id
                - upload_url
                - expires_at
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/upload/chunked/init:
  post:
    summary: Initialize a chunked upload
    description: |
      Start a chunked upload session for a large file.
      This creates an upload session that can be used to upload the file in chunks.
    tags:
      - Media
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              file_name:
                type: string
                description: The name of the file.
              content_type:
                type: string
                description: The content type of the file.
              total_size:
                type: integer
                description: The total size of the file in bytes.
              chunk_size:
                type: integer
                description: The size of each chunk in bytes.
              is_encrypted:
                type: boolean
                default: false
                description: Flag indicating whether the file is encrypted.
            required:
              - file_name
              - content_type
              - total_size
              - chunk_size
    responses:
      '200':
        description: Chunked upload session created successfully.
        content:
          application/json:
            schema:
              type: object
              properties:
                upload_id:
                  type: string
                  description: The ID of the upload session.
                expires_at:
                  type: string
                  format: date-time
                  description: When the upload session expires.
              required:
                - upload_id
                - expires_at
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/upload/chunked/{upload_id}/{chunk_index}:
  parameters:
    - name: upload_id
      in: path
      required: true
      description: The ID of the upload session.
      schema:
        type: string
    - name: chunk_index
      in: path
      required: true
      description: The index of the chunk (0-based).
      schema:
        type: integer
  put:
    summary: Upload a chunk of a file
    description: |
      Upload a single chunk of a file as part of a chunked upload session.
      Chunks must be uploaded in order, starting from index 0.
    tags:
      - Media
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/octet-stream:
          schema:
            type: string
            format: binary
            description: The binary data of the chunk.
    responses:
      '200':
        description: Chunk uploaded successfully.
        content:
          application/json:
            schema:
              type: object
              properties:
                chunk_index:
                  type: integer
                  description: The index of the uploaded chunk.
                received_size:
                  type: integer
                  description: The size of the uploaded chunk in bytes.
                total_received:
                  type: integer
                  description: The total size of all uploaded chunks in bytes.
              required:
                - chunk_index
                - received_size
                - total_received
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '409':
        description: Conflict. The chunk index is out of order or the chunk size is incorrect.
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/upload/chunked/{upload_id}/status:
  parameters:
    - name: upload_id
      in: path
      required: true
      description: The ID of the upload session.
      schema:
        type: string
  get:
    summary: Get the status of a chunked upload
    description: |
      Get the current status of a chunked upload session, including which chunks have been uploaded.
      This can be used to resume an interrupted upload.
    tags:
      - Media
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Upload status retrieved successfully.
        content:
          application/json:
            schema:
              type: object
              properties:
                upload_id:
                  type: string
                  description: The ID of the upload session.
                file_name:
                  type: string
                  description: The name of the file.
                content_type:
                  type: string
                  description: The content type of the file.
                total_size:
                  type: integer
                  description: The total size of the file in bytes.
                uploaded_chunks:
                  type: array
                  items:
                    type: integer
                  description: The indices of the chunks that have been uploaded.
                total_received:
                  type: integer
                  description: The total size of all uploaded chunks in bytes.
                expires_at:
                  type: string
                  format: date-time
                  description: When the upload session expires.
              required:
                - upload_id
                - file_name
                - content_type
                - total_size
                - uploaded_chunks
                - total_received
                - expires_at
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/upload/chunked/{upload_id}/complete:
  parameters:
    - name: upload_id
      in: path
      required: true
      description: The ID of the upload session.
      schema:
        type: string
  post:
    summary: Complete a chunked upload
    description: |
      Complete a chunked upload session after all chunks have been uploaded.
      This finalizes the upload and makes the file available for use.
    tags:
      - Media
    security:
      - bearerAuth: []
    responses:
      '200':
        description: Upload completed successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Media'
      '400':
        description: Bad request. The upload is not ready to be completed (missing chunks).
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/media/{media_id}:
  parameters:
    - name: media_id
      in: path
      required: true
      description: The ID of the media item.
      schema:
        type: string
        # format: uuid or other identifier
  get:
    summary: Get media metadata or download
    description: Retrieves metadata or potentially the media file itself (implementation dependent).
    tags:
      - Media
    security:
      - bearerAuth: [] # Or public access for some media?
    x-security-considerations:
      description: |
        Media access security considerations:
        - Access control based on media visibility settings
        - Content delivery through secure channels
        - Signed URLs with expiration for sensitive content
    responses:
      '200':
        description: Media metadata or file content.
        content:
          application/json: # For metadata
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/Media' # To be defined
          # Potentially add other content types for direct download (e.g., image/jpeg)
          # image/jpeg:
          #   schema:
          #     type: string
          #     format: binary
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Delete a media item
    description: Allows users to delete media they uploaded, or admins to manage media.
    tags:
      - Media
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Media deleted successfully.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'