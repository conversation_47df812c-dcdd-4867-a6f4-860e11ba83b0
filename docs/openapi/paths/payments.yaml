# docs/openapi/paths/payments.yaml

/payments/checkout:
  post:
    summary: Initiate payment for a one-time action
    tags:
      - Payments
    security:
      - bearerAuth: []
    x-security-considerations:
      description: |
        This endpoint handles sensitive payment information and requires:
        - Strong authentication
        - HTTPS
        - PCI DSS compliance
        - Audit logging
    x-rate-limit:
      description: Rate limiting to prevent payment abuse
      requests: 10
      period: 60 # 10 requests per minute
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/PaymentCheckoutRequest'
    responses:
      '200':
        description: Payment checkout initiated successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/PaymentCheckoutResponse'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/payments/history:
  get:
    summary: Get the user's payment history
    tags:
      - Payments
    security:
      - bearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 20
        description: Maximum number of payment records to return
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
        description: Number of payment records to skip for pagination
    responses:
      '200':
        description: Payment history retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/PaymentHistoryResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/payments/webhook:
  post:
    summary: Webhook endpoint for payment processor callbacks
    tags:
      - Payments
    security:
      - apiKeyAuth: []
    x-security-considerations:
      description: |
        This endpoint receives callbacks from payment processors and requires:
        - API key authentication
        - Signature verification of payloads
        - IP address whitelisting
        - Idempotency handling
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: Payment processor webhook payload (varies by provider)
    responses:
      '200':
        description: Webhook processed successfully
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
