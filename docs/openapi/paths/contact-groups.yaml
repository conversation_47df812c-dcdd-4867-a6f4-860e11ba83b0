# docs/openapi/paths/contact-groups.yaml

/contact-groups:
  get:
    summary: List user's contact groups
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 50
        description: Maximum number of groups to return.
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
        description: Number of groups to skip for pagination.
    responses:
      '200':
        description: A list of contact groups the user has created.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroupListResponse'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  post:
    summary: Create a new contact group
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/CreateContactGroupRequest'
    responses:
      '201':
        description: Contact group created successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroup'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/contact-groups/{groupId}:
  parameters:
    - name: groupId
      in: path
      required: true
      description: The ID of the contact group.
      schema:
        type: string
        format: uuid
  get:
    summary: Get contact group details
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: Detailed information about the contact group.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroup'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  put:
    summary: Update contact group settings
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../schemas/schemas.yaml#/components/schemas/UpdateContactGroupRequest'
    responses:
      '200':
        description: Contact group updated successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroup'
      '400':
        $ref: '../components/responses.yaml#/BadRequestError'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Delete a contact group
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    responses:
      '204':
        description: Contact group deleted successfully.
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'

/contact-groups/{groupId}/members/{contactId}:
  parameters:
    - name: groupId
      in: path
      required: true
      description: The ID of the contact group.
      schema:
        type: string
        format: uuid
    - name: contactId
      in: path
      required: true
      description: The ID of the contact to manage within the group.
      schema:
        type: string
        format: uuid
  post:
    summary: Add a contact to a group
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: Contact added to group successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroup'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
  delete:
    summary: Remove a contact from a group
    tags:
      - Contact Groups
    security:
      - BearerAuth: []
    responses:
      '200':
        description: Contact removed from group successfully.
        content:
          application/json:
            schema:
              $ref: '../schemas/schemas.yaml#/components/schemas/ContactGroup'
      '401':
        $ref: '../components/responses.yaml#/UnauthorizedError'
      '403':
        $ref: '../components/responses.yaml#/ForbiddenError'
      '404':
        $ref: '../components/responses.yaml#/NotFoundError'
      '500':
        $ref: '../components/responses.yaml#/InternalServerError'
