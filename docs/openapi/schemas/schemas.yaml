# schemas/schemas.yaml
components:
  schemas:
    # Authentication Schemas
    RegisterRequest:
      type: object
      description: Request model for registering a new user. For anonymous sign-in, only password is required.
      properties:
        user_handle:
          type: string
          description: Optional unique identifier for the user (Meena ID). Server generates if missing for anonymous sign-in.
        password:
          type: string
          format: password
          description: User's password for authentication.
        email:
          type: string
          format: email
          description: Optional email address for account recovery.
        phone_number:
          type: string
          description: Optional phone number for account recovery.
        recovery_pin:
          type: string
          description: Optional PIN for account recovery.
        recovery_phrase:
          type: string
          description: Optional seed phrase for account recovery. Server generates if missing.
        display_name:
          type: string
          description: Optional display name shown to other users.
      required:
        - password

    LoginRequest:
      type: object
      description: Request model for user login.
      properties:
        identifier:
          type: string
          description: User's identifier (Meena ID, email, or phone number).
        password:
          type: string
          format: password
          description: User's password.
      required:
        - identifier
        - password

    AuthResponse:
      type: object
      description: Response model for authentication operations (register, login, token refresh).
      properties:
        user:
          $ref: '#/components/schemas/UserProfile'
        access_token:
          type: string
          format: jwt
          description: JWT access token for API authentication.
        refresh_token:
          type: string
          format: jwt
          description: JWT refresh token to obtain new access tokens.
        expires_in:
          type: integer
          description: Number of seconds until the access token expires.
        recovery_phrase:
          type: string
          description: Recovery phrase for account recovery (only included in registration response).
      required:
        - user
        - access_token
        - refresh_token
        - expires_in

    TokenRefreshRequest:
      type: object
      description: Request model for refreshing authentication tokens.
      properties:
        refresh_token:
          type: string
          format: jwt
          description: JWT refresh token obtained during login or registration.
      required:
        - refresh_token

LogoutRequest:
      type: object
      description: Request model for user logout.
      properties:
        refresh_token:
          type: string
          description: The refresh token to invalidate.
      required:
        - refresh_token

    InitiateRecoveryRequest:
      type: object
      description: Request model for initiating account recovery.
      properties:
        user_handle:
          type: string
          description: The user handle (Meena ID) of the account to recover.
        recovery_phrase:
          type: string
          description: The secret recovery phrase for the account.
        recovery_pin:
          type: string
          description: The PIN associated with the recovery phrase.
      required:
        - user_handle
        - recovery_phrase
        - recovery_pin

    InitiateRecoveryResponse:
      type: object
      description: Response model for successful account recovery initiation.
      properties:
        recovery_session_token:
          type: string
          description: Token to be used for resetting the password.
      required:
        - recovery_session_token

    # This ResetPasswordRequest is for the new flow using recovery_session_token
    # The existing PasswordResetRequest (for email-based reset) and 
    # PasswordResetConfirmRequest might need to be adjusted or removed later
    # if the /auth/request-password-reset endpoint is fully removed.
    ResetPasswordRequest: # New version for /auth/reset-password
      type: object
      description: Request model for resetting password using a recovery session token.
      properties:
        token:
          type: string
          description: The recovery_session_token received from initiate-recovery.
        new_password:
          type: string
          format: password
          minLength: 8
          description: The new password for the account.
      required:
        - token
        - new_password

    GeneralMessageResponse:
      type: object
      description: A general message response.
      properties:
        message:
          type: string
          description: A message detailing the result of the operation.
      required:
        - message
    PasswordResetRequest:
      type: object
      description: Request model for initiating a password reset.
      properties:
        email:
          type: string
          format: email
          description: Email address associated with the account.
      required:
        - email

    PasswordResetConfirmRequest:
      type: object
      description: Request model for confirming a password reset.
      properties:
        token:
          type: string
          description: The token received via email/SMS.
        new_password:
          type: string
          format: password
          description: The new password to set.
      required:
        - token
        - new_password

    AccountRecoveryRequest:
      type: object
      description: Request model for recovering an account using recovery phrase and PIN.
      properties:
        user_handle:
          type: string
          description: Meena ID of the account to recover.
        recovery_phrase:
          type: string
          description: Recovery phrase for the account.
        recovery_pin:
          type: string
          description: Recovery PIN for the account.
        new_password:
          type: string
          format: password
          description: New password to set for the account.
      required:
        - user_handle
        - recovery_phrase
        - recovery_pin
        - new_password

    RecoveryResponse:
      type: object
      description: Response model for account recovery request.
      properties:
        recovery_token:
          type: string
          description: Token for confirming account recovery.
      required:
        - recovery_token

    RecoveryConfirmRequest:
      type: object
      description: Request model for confirming account recovery.
      properties:
        recovery_token:
          type: string
          description: Token received from recovery request.
        new_password:
          type: string
          format: password
          description: New password to set for the account.
      required:
        - recovery_token
        - new_password

    PasswordChangeRequest:
      type: object
      description: Request model for changing a user's password.
      properties:
        current_password:
          type: string
          format: password
          description: Current password for verification.
        new_password:
          type: string
          format: password
          description: New password to set.
      required:
        - current_password
        - new_password

    # User Settings Schemas
    UserSettings:
      type: object
      properties:
        permissions:
          type: object
          description: User-specific permission overrides or settings.
          additionalProperties: true # Or define specific permissions
        notifications:
          type: object
          description: Notification preferences.
          properties:
            push_enabled:
              type: boolean
            email_summary_enabled:
              type: boolean
            sound_enabled:
              type: boolean
            # Add more specific notification settings
          additionalProperties: false
        two_fa_methods:
          type: array
          items:
            type: string
            enum: [totp, sms, email] # Example methods
          description: Enabled 2FA methods.
        theme:
          type: string
          enum: [light, dark, system]
          default: system
        language:
          type: string
          description: Preferred language code (e.g., 'en', 'es').
          default: 'en'
        face_id_enabled:
          type: boolean
          description: Whether Face ID/Biometric unlock is enabled on the device.
        fingerprint_enabled:
          type: boolean
          description: Whether Fingerprint unlock is enabled on the device.
      # Add required fields if any

    UserSettingsUpdate:
      type: object
      description: Fields for updating user settings. Use partial updates.
      properties:
        permissions:
          type: object
          additionalProperties: true
        notifications:
          type: object
          properties:
            push_enabled:
              type: boolean
            email_summary_enabled:
              type: boolean
            sound_enabled:
              type: boolean
          additionalProperties: false
        theme:
          type: string
          enum: [light, dark, system]
        language:
          type: string
        face_id_enabled:
          type: boolean
        fingerprint_enabled:
          type: boolean
      minProperties: 1 # Require at least one field to update

    # Contact & Blocking Schemas
    Contact:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        user_handle:
          type: string
        display_name:
          type: string
          description: Custom display name set by the current user for this contact.
          nullable: true
        profile_picture_url:
          type: string
          format: url
          nullable: true
      required:
        - user_id
        - user_handle

    ContactListResponse:
      type: object
      properties:
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/Contact'
        total_count:
          type: integer
      required:
        - contacts
        - total_count

    # Contact Group Schemas
    ContactGroup:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user_id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
          nullable: true
        member_ids:
          type: array
          items:
            type: string
            format: uuid
        member_count:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
        - id
        - user_id
        - name
        - member_ids
        - created_at
        - updated_at

    ContactGroupListResponse:
      type: object
      properties:
        groups:
          type: array
          items:
            $ref: '#/components/schemas/ContactGroup'
        total_count:
          type: integer
      required:
        - groups
        - total_count

    CreateContactGroupRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        member_ids:
          type: array
          items:
            type: string
            format: uuid
      required:
        - name

    UpdateContactGroupRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
      required:
        - name

    AddContactRequest:
      type: object
      properties:
        contact_user_handle:
          type: string
          description: The handle of the user to add as a contact.
        display_name:
          type: string
          description: Optional custom display name for the contact.
          nullable: true
      required:
        - contact_user_handle

    BlockedUser: # Similar to Contact, but maybe less info?
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        user_handle:
          type: string
        display_name:
          type: string # The user's actual display name, not custom
          nullable: true
        profile_picture_url:
          type: string
          format: url
          nullable: true
      required:
        - user_id
        - user_handle

    BlockedListResponse:
      type: object
      properties:
        blocked_users:
          type: array
          items:
            $ref: '#/components/schemas/BlockedUser'
        total_count:
          type: integer
      required:
        - blocked_users
        - total_count

    # Messaging Schemas
    Conversation:
      type: object
      description: Represents an entry in the user's conversation list.
      properties:
        user_id:
          type: string
          format: uuid
          description: The ID of the user this entry belongs to.
        conversation_id:
          type: string
          format: uuid
          description: The unique ID of the conversation (chat, group, channel).
        conversation_type:
          type: string
          enum: [direct, group, channel]
        privacy_type:
          type: string
          enum: [private, public, secret] # Relevant for groups/channels
        conversation_name:
          type: string
          nullable: true
          description: Name of the group/channel or the other user in a direct chat.
        conversation_avatar_url:
          type: string
          format: url
          nullable: true
        last_message_preview:
          type: string
          nullable: true
          description: A short preview of the last message.
        last_activity_timestamp:
          type: string
          format: date-time
          description: Timestamp of the last message or significant event.
        unread_count:
          type: integer
          default: 0
        is_muted:
          type: boolean
          default: false
        is_pinned:
          type: boolean
          default: false
      required:
        - user_id
        - conversation_id
        - conversation_type
        - last_activity_timestamp
        - unread_count
        - is_muted
        - is_pinned

    ConversationListResponse:
      type: object
      properties:
        conversations:
          type: array
          items:
            $ref: '#/components/schemas/Conversation'
        total_count:
          type: integer
      required:
        - conversations
        - total_count

    MessageSender:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        user_handle:
          type: string
        display_name:
          type: string
        avatar_url:
          type: string
          format: url
          nullable: true
      required:
        - user_id
        - user_handle
        - display_name

    MediaItem: # Define based on your media handling
      type: object
      properties:
        media_id:
          type: string
          format: uuid
        media_type:
          type: string
          enum: [image, video, audio, file]
        url:
          type: string
          format: url
        thumbnail_url:
          type: string
          format: url
          nullable: true
        file_name:
          type: string
          nullable: true
        file_size:
          type: integer
          nullable: true
        metadata:
          type: object
          additionalProperties: true
      required:
        - media_id
        - media_type
        - url

    Message:
      type: object
      properties:
        message_id:
          type: string
          format: uuid
        sender:
          $ref: '#/components/schemas/MessageSender'
        text_content:
          type: string
          nullable: true
        media:
          type: array
          items:
            $ref: '#/components/schemas/MediaItem'
          default: []
        sent_at:
          type: string
          format: date-time
        edited_at:
          type: string
          format: date-time
          nullable: true
        reply_to_message_id:
          type: string
          format: uuid
          nullable: true
        moderation_status:
          type: string
          enum: [approved, pending, rejected]
          default: approved
      required:
        - message_id
        - sender
        - sent_at

    MessageListResponse:
      type: object
      properties:
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        # Consider adding pagination info like next_cursor
      required:
        - messages

    SendMessageRequest:
      type: object
      properties:
        text_content:
          type: string
          nullable: true
        media_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
        reply_to_message_id:
          type: string
          format: uuid
          nullable: true
      # Require at least text_content or media_ids
      # oneOf: [ {required: [text_content]}, {required: [media_ids]} ] # More complex validation

    EditMessageRequest:
      type: object
      properties:
        text_content:
          type: string
          nullable: true
        media_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
      minProperties: 1 # Require at least one field

    ConversationSettingsUpdate:
      type: object
      properties:
        is_muted:
          type: boolean
        is_pinned:
          type: boolean
      minProperties: 1 # Require at least one field

    # --- Group Schemas --- #

    Group:
      type: object
      properties:
        group_id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
          nullable: true
        avatar_url:
          type: string
          format: url
          nullable: true
        member_count:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        # Add other relevant group properties (e.g., privacy, creator_id)
      required:
        - group_id
        - name
        - member_count
        - created_at
        - updated_at

    GroupListResponse:
      type: object
      properties:
        groups:
          type: array
          items:
            $ref: '#/components/schemas/Group'
        # Add pagination info if needed
      required:
        - groups

    CreateGroupRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        initial_members:
          type: array
          items:
            type: string
            format: uuid # User IDs
          description: Optional list of user IDs to invite initially.
      required:
        - name

    UpdateGroupRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        avatar_url:
          type: string
          format: url
          nullable: true
        # Add other updatable fields
      minProperties: 1

    GroupMember:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User' # Reference the base User schema
        role:
          type: string
          enum: [admin, member, moderator] # Example roles
          default: member
        joined_at:
          type: string
          format: date-time
      required:
        - user
        - role
        - joined_at

    GroupMemberListResponse:
      type: object
      properties:
        members:
          type: array
          items:
            $ref: '#/components/schemas/GroupMember'
        # Add pagination info if needed
      required:
        - members

    AddGroupMembersRequest:
      type: object
      properties:
        user_ids:
          type: array
          items:
            type: string
            format: uuid
          minItems: 1
      required:
        - user_ids

    UpdateGroupMemberRequest:
      type: object
      properties:
        role:
          type: string
          enum: [admin, member, moderator]
        # Add other permissions/settings if applicable
      required:
        - role

    # --- Channel Schemas --- #

    Channel:
      type: object
      properties:
        channel_id:
          type: string
          format: uuid # Or custom ID
        name:
          type: string
        topic:
          type: string
          nullable: true
        type:
          type: string
          enum: [public, private, announcement] # Example types
        subscriber_count:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        # Add other relevant channel properties (e.g., creator_id)
      required:
        - channel_id
        - name
        - type
        - created_at
        - updated_at

    ChannelListResponse:
      type: object
      properties:
        channels:
          type: array
          items:
            $ref: '#/components/schemas/Channel'
        # Add pagination info if needed
      required:
        - channels

    CreateChannelRequest:
      type: object
      properties:
        name:
          type: string
        topic:
          type: string
          nullable: true
        type:
          type: string
          enum: [public, private, announcement]
          default: public
      required:
        - name

    UpdateChannelRequest:
      type: object
      properties:
        name:
          type: string
        topic:
          type: string
          nullable: true
        # Add other updatable fields
      minProperties: 1

    # --- Media Schemas --- #

    Media:
      type: object
      properties:
        media_id:
          type: string
          # format: uuid or other identifier
        url:
          type: string
          format: url
        mime_type:
          type: string
        size_bytes:
          type: integer
        filename:
          type: string
          nullable: true
        uploaded_at:
          type: string
          format: date-time
        uploader_id:
          type: string
          format: uuid
        # Add dimensions for images/videos if needed
        # width:
        #   type: integer
        # height:
        #   type: integer
      required:
        - media_id
        - url
        - mime_type
        - size_bytes
        - uploaded_at
        - uploader_id

    MediaUploadResponse:
      type: object
      properties:
        media:
          $ref: '#/components/schemas/Media'
      required:
        - media



    # --- Story Schemas --- #

    Story:
      type: object
      properties:
        story_id:
          type: string
          format: uuid
        creator:
          $ref: '#/components/schemas/User'
        base_media_url:
          type: string
          format: url
        thumbnail_url:
          type: string
          format: url
        caption:
          type: string
          nullable: true
        elements:
          type: array
          items:
            $ref: '#/components/schemas/StoryElement'
        created_at:
          type: string
          format: date-time
        expires_at:
          type: string
          format: date-time
        viewer_count:
          type: integer
        is_viewed:
          type: boolean
          description: Whether the current user has viewed this story
      required:
        - story_id
        - creator
        - base_media_url
        - created_at
        - expires_at

    StoryElement:
      type: object
      properties:
        element_type:
          type: string
          enum: [text, sticker, gif, poll, mention, location]
        position_x:
          type: number
          format: float
          description: X position as percentage of width (0-100)
        position_y:
          type: number
          format: float
          description: Y position as percentage of height (0-100)
        element_data:
          type: object
          additionalProperties: true
          description: Element-specific data (e.g., text content, sticker ID)
      required:
        - element_type
        - position_x
        - position_y
        - element_data

    CreateStoryRequest:
      type: object
      properties:
        base_media_id:
          type: string
          format: uuid
          description: ID of the previously uploaded media
        caption:
          type: string
          nullable: true
        elements:
          type: array
          items:
            $ref: '#/components/schemas/StoryElement'
          default: []
      required:
        - base_media_id

    UserStoriesResponse:
      type: object
      properties:
        stories:
          type: array
          items:
            $ref: '#/components/schemas/Story'
      required:
        - stories

    StoryFeedResponse:
      type: object
      properties:
        feed:
          type: array
          items:
            type: object
            properties:
              user:
                $ref: '#/components/schemas/User'
              stories:
                type: array
                items:
                  type: object
                  properties:
                    story_id:
                      type: string
                      format: uuid
                    thumbnail_url:
                      type: string
                      format: url
                    expires_at:
                      type: string
                      format: date-time
                    is_viewed:
                      type: boolean
                  required:
                    - story_id
                    - thumbnail_url
                    - expires_at
                    - is_viewed
            required:
              - user
              - stories
      required:
        - feed

    StoryViewersResponse:
      type: object
      properties:
        viewers:
          type: array
          items:
            type: object
            properties:
              user:
                $ref: '#/components/schemas/User'
              viewed_at:
                type: string
                format: date-time
            required:
              - user
              - viewed_at
      required:
        - viewers

    # --- Call Schemas --- #

    CallLog:
      type: object
      properties:
        call_log_id:
          type: string
          format: uuid
        other_party:
          $ref: '#/components/schemas/User'
        type:
          type: string
          enum: [audio, video]
        status:
          type: string
          enum: [initiated, ringing, in_progress, completed, missed, rejected]
        start_time:
          type: string
          format: date-time
        duration_seconds:
          type: integer
          nullable: true
      required:
        - call_log_id
        - other_party
        - type
        - status
        - start_time

    CallLogsResponse:
      type: object
      properties:
        call_logs:
          type: array
          items:
            $ref: '#/components/schemas/CallLog'
        total_count:
          type: integer
        limit:
          type: integer
        offset:
          type: integer
      required:
        - call_logs
        - total_count
        - limit
        - offset

    CallUsageResponse:
      type: object
      properties:
        total_duration_seconds:
          type: integer
        limit_seconds:
          type: integer
        remaining_seconds:
          type: integer
      required:
        - total_duration_seconds
        - limit_seconds
        - remaining_seconds

    InitiateCallRequest:
      type: object
      properties:
        callee_user_handle:
          type: string
        call_type:
          type: string
          enum: [audio, video]
      required:
        - callee_user_handle
        - call_type

    InitiateCallResponse:
      type: object
      properties:
        call_id:
          type: string
          format: uuid
        signaling_server_url:
          type: string
          format: url
        ice_servers:
          type: array
          items:
            $ref: '#/components/schemas/IceServer'
        token:
          type: string
      required:
        - call_id
        - signaling_server_url
        - ice_servers
        - token

    IceServer:
      type: object
      properties:
        urls:
          type: array
          items:
            type: string
        username:
          type: string
          nullable: true
        credential:
          type: string
          nullable: true
      required:
        - urls

    EndCallRequest:
      type: object
      properties:
        duration_seconds:
          type: integer
      required:
        - duration_seconds

    # --- Payment Schemas --- #

    PaymentCheckoutRequest:
      type: object
      properties:
        purpose:
          type: string
          enum: [create_public_group, create_channel, other]
        related_entity_details:
          type: object
          additionalProperties: true
          nullable: true
        success_url:
          type: string
          format: url
        cancel_url:
          type: string
          format: url
      required:
        - purpose
        - success_url
        - cancel_url

    PaymentCheckoutResponse:
      type: object
      properties:
        checkout_url:
          type: string
          format: url
          nullable: true
        client_secret:
          type: string
          nullable: true
        payment_processor:
          type: string
          enum: [stripe, other]
      required:
        - payment_processor

    Payment:
      type: object
      properties:
        payment_id:
          type: string
        amount:
          type: number
          format: float
        currency:
          type: string
        status:
          type: string
          enum: [pending, completed, failed, refunded]
        purpose:
          type: string
        created_at:
          type: string
          format: date-time
      required:
        - payment_id
        - amount
        - currency
        - status
        - purpose
        - created_at

    PaymentHistoryResponse:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
        total_count:
          type: integer
        limit:
          type: integer
        offset:
          type: integer
      required:
        - payments
        - total_count
        - limit
        - offset

    # --- Subscription Schemas --- #

    Subscription:
      type: object
      properties:
        subscription_id:
          type: string
          nullable: true
        user_id:
          type: string
          format: uuid
        tier:
          type: string
          enum: [free, gold]
        status:
          type: string
          enum: [active, canceled, expired]
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
          nullable: true
        auto_renew:
          type: boolean
        payment_method:
          type: string
          nullable: true
      required:
        - user_id
        - tier
        - status
        - start_date
        - auto_renew

    SubscriptionCheckoutRequest:
      type: object
      properties:
        success_url:
          type: string
          format: url
        cancel_url:
          type: string
          format: url
      required:
        - success_url
        - cancel_url

    SubscriptionCheckoutResponse:
      type: object
      properties:
        checkout_url:
          type: string
          format: url
          nullable: true
        client_secret:
          type: string
          nullable: true
        payment_processor:
          type: string
          enum: [stripe, other]
      required:
        - payment_processor

    # --- Support Schemas --- #

    Ticket:
      type: object
      properties:
        ticket_id:
          type: string
          format: uuid
        subject:
          type: string
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
        priority:
          type: string
          enum: [low, medium, high, urgent]
        category:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        resolved_at:
          type: string
          format: date-time
          nullable: true
      required:
        - ticket_id
        - subject
        - status
        - priority
        - category
        - created_at
        - updated_at

    TicketsResponse:
      type: object
      properties:
        tickets:
          type: array
          items:
            $ref: '#/components/schemas/Ticket'
        total_count:
          type: integer
        limit:
          type: integer
        offset:
          type: integer
      required:
        - tickets
        - total_count
        - limit
        - offset

    TicketResponse:
      type: object
      properties:
        ticket:
          $ref: '#/components/schemas/Ticket'
      required:
        - ticket

    CreateTicketRequest:
      type: object
      properties:
        subject:
          type: string
        category:
          type: string
        message:
          type: string
        attachment_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
      required:
        - subject
        - category
        - message

    UpdateTicketRequest:
      type: object
      properties:
        status:
          type: string
          enum: [open, in_progress, resolved, closed]
          nullable: true
        priority:
          type: string
          enum: [low, medium, high, urgent]
          nullable: true
      minProperties: 1

    TicketMessage:
      type: object
      properties:
        message_id:
          type: string
          format: uuid
        sender_type:
          type: string
          enum: [user, support_agent, system]
        message_body:
          type: string
        sent_at:
          type: string
          format: date-time
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/MediaItem'
          default: []
      required:
        - message_id
        - sender_type
        - message_body
        - sent_at

    TicketMessagesResponse:
      type: object
      properties:
        messages:
          type: array
          items:
            $ref: '#/components/schemas/TicketMessage'
        total_count:
          type: integer
        limit:
          type: integer
        offset:
          type: integer
      required:
        - messages
        - total_count
        - limit
        - offset

    TicketMessageResponse:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/TicketMessage'
      required:
        - message

    AddTicketMessageRequest:
      type: object
      properties:
        message_body:
          type: string
        attachment_ids:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
      required:
        - message_body

    # --- Add other schemas below as needed --- #

    # User Schemas
    UserProfile:
      type: object
      description: User profile information returned in authentication responses.
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user.
        user_handle:
          type: string
          description: Unique Meena ID for the user.
        display_name:
          type: string
          description: User's display name shown to other users.
        bio:
          type: string
          description: User's biography or status message.
        avatar_url:
          type: string
          format: url
          description: URL to the user's profile picture.
        is_verified:
          type: boolean
          description: Whether the user's account is verified.
        is_gold_member:
          type: boolean
          description: Whether the user has a gold membership.
        last_active:
          type: string
          format: date-time
          description: When the user was last active.
        created_at:
          type: string
          format: date-time
          description: When the user account was created.
        follower_count:
          type: integer
          description: Number of users following this user.
        following_count:
          type: integer
          description: Number of users this user is following.
      required:
        - id
        - user_handle
        - display_name
        - created_at

    User:
      type: object
      description: Detailed user information for database storage and internal use.
      properties:
        userId:
          type: string
          format: uuid
          description: Unique identifier for the user.
        userHandle:
          type: string
          description: Unique Meena ID for the user.
        displayName:
          type: string
          description: User's display name shown to other users.
        profilePictureUrl:
          type: string
          format: url
          description: URL to the user's profile picture.
        bio:
          type: string
          description: User's biography or status message.
        phoneNumber:
          type: string
          description: User's phone number (if provided).
        email:
          type: string
          format: email
          description: User's email address (if provided).
        subscriptionTier:
          type: string
          description: User's subscription tier (e.g., free, gold).
        verificationStatus:
          type: string
          description: User's verification status.
        isActive:
          type: boolean
          description: Whether the user account is active.
        createdAt:
          type: string
          format: date-time
          description: When the user account was created.
        lastSeenAt:
          type: string
          format: date-time
          description: When the user was last seen.
      required:
        - userId
        - userHandle

    # Example Error Schema
    Error:
      type: object
      properties:
        code:
          type: string
          description: An application-specific error code.
        message:
          type: string
          description: A human-readable error message.
        details:
          type: object
          additionalProperties: true
          description: Optional additional details about the error.
      required:
        - code
        - message