# docs/openapi/components/responses.yaml

BadRequestError:
  description: Bad Request - The request was invalid or cannot be served.
  content:
    application/json:
      schema:
        $ref: '../schemas/schemas.yaml#/components/schemas/Error'

UnauthorizedError:
  description: Unauthorized - Authentication is required and has failed or has not been provided.
  content:
    application/json:
      schema:
        $ref: '../schemas/schemas.yaml#/components/schemas/Error'

ForbiddenError:
  description: Forbidden - The server understood the request but refuses to authorize it.
  content:
    application/json:
      schema:
        $ref: '../schemas/schemas.yaml#/components/schemas/Error'

NotFoundError:
  description: Not Found - The requested resource could not be found.
  content:
    application/json:
      schema:
        $ref: '../schemas/schemas.yaml#/components/schemas/Error'

InternalServerError:
  description: Internal Server Error - An unexpected condition was encountered.
  content:
    application/json:
      schema:
        $ref: '../schemas/schemas.yaml#/components/schemas/Error'
