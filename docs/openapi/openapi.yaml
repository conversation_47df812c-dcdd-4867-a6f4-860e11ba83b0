openapi: 3.0.3
info:
  title: Meena API
  version: 1.0.0
  description: |
    API specification for the Meena chat application.

    ## Security
    This API implements multiple security measures:
    - JWT-based authentication for most endpoints
    - API key authentication for server-to-server communication
    - Rate limiting to prevent abuse
    - Input validation on all endpoints
    - HTTPS required for all communications
    - End-to-end encryption for direct messages

    See the security documentation for more details.
servers:
  - url: /api/v1
    description: Development server

paths:
  # References to path definitions in separate files
  /auth/register:
    $ref: './paths/auth.yaml#/paths/~1auth~1register'
  /auth/login:
    $ref: './paths/auth.yaml#/paths/~1auth~1login'
  /auth/refresh:
    $ref: './paths/auth.yaml#/paths/~1auth~1refresh'
  /auth/logout:
    $ref: './paths/auth.yaml#/paths/~1auth~1logout'
  /auth/request-password-reset:
    $ref: './paths/auth.yaml#/paths/~1auth~1request-password-reset'
  /auth/reset-password:
    $ref: './paths/auth.yaml#/paths/~1auth~1reset-password'
  /auth/verify-account:
    $ref: './paths/auth.yaml#/paths/~1auth~1verify-account'
  /users/me:
    $ref: './paths/users.yaml#/paths/~1users~1me'
  /users/{user_handle}:
    $ref: './paths/users.yaml#/paths/~1users~1{user_handle}'
  /users/me/settings:
    $ref: './paths/users.yaml#/paths/~1users~1me~1settings'
  /users/me/delete/request:
    $ref: './paths/users.yaml#/paths/~1users~1me~1delete~1request'
  /users/me/remote-wipe/setup:
    $ref: './paths/users.yaml#/paths/~1users~1me~1remote-wipe~1setup'
  /contacts:
    $ref: './paths/contacts.yaml#/paths/~1contacts'
  /contacts/favorites:
    $ref: './paths/contacts.yaml#/paths/~1contacts~1favorites'
  /contacts/{contact_user_handle}:
    $ref: './paths/contacts.yaml#/paths/~1contacts~1{contact_user_handle}'
  /blocks:
    $ref: './paths/contacts.yaml#/paths/~1blocks'
  /blocks/{blocked_user_handle}:
    $ref: './paths/contacts.yaml#/paths/~1blocks~1{blocked_user_handle}'
  /conversations:
    $ref: './paths/messaging.yaml#/paths/~1conversations'
  /conversations/{conversation_id}/messages:
    $ref: './paths/messaging.yaml#/paths/~1conversations~1{conversation_id}~1messages'
  /messages/{message_id}:
    $ref: './paths/messaging.yaml#/paths/~1messages~1{message_id}'
  /conversations/{conversation_id}/read:
    $ref: './paths/messaging.yaml#/paths/~1conversations~1{conversation_id}~1read'
  /conversations/{conversation_id}/settings:
    $ref: './paths/messaging.yaml#/paths/~1conversations~1{conversation_id}~1settings'
  /groups:
    $ref: './paths/groups.yaml#/paths/~1groups'
  /groups/{group_id}:
    $ref: './paths/groups.yaml#/paths/~1groups~1{group_id}'
  /groups/{group_id}/members:
    $ref: './paths/groups.yaml#/paths/~1groups~1{group_id}~1members'
  /groups/{group_id}/members/{user_id}:
    $ref: './paths/groups.yaml#/paths/~1groups~1{group_id}~1members~1{user_id}'
  /contact-groups:
    $ref: './paths/contact-groups.yaml#/paths/~1contact-groups'
  /contact-groups/{groupId}:
    $ref: './paths/contact-groups.yaml#/paths/~1contact-groups~1{groupId}'
  /contact-groups/{groupId}/members/{contactId}:
    $ref: './paths/contact-groups.yaml#/paths/~1contact-groups~1{groupId}~1members~1{contactId}'
  /channels:
    $ref: './paths/channels.yaml#/paths/~1channels'
  /channels/{channel_id}:
    $ref: './paths/channels.yaml#/paths/~1channels~1{channel_id}'
  /channels/{channel_id}/subscribe:
    $ref: './paths/channels.yaml#/paths/~1channels~1{channel_id}~1subscribe'
  /channels/{channel_id}/unsubscribe:
    $ref: './paths/channels.yaml#/paths/~1channels~1{channel_id}~1unsubscribe'
  /channels/{channel_id}/messages:
    $ref: './paths/channels.yaml#/paths/~1channels~1{channel_id}~1messages'
  /media/upload:
    $ref: './paths/media.yaml#/paths/~1media~1upload'
  /media/upload/encrypted:
    $ref: './paths/media.yaml#/paths/~1media~1upload~1encrypted'
  /media/upload/chunked/init:
    $ref: './paths/media.yaml#/paths/~1media~1upload~1chunked~1init'
  /media/upload/chunked/{upload_id}/{chunk_index}:
    $ref: './paths/media.yaml#/paths/~1media~1upload~1chunked~1{upload_id}~1{chunk_index}'
  /media/upload/chunked/{upload_id}/status:
    $ref: './paths/media.yaml#/paths/~1media~1upload~1chunked~1{upload_id}~1status'
  /media/upload/chunked/{upload_id}/complete:
    $ref: './paths/media.yaml#/paths/~1media~1upload~1chunked~1{upload_id}~1complete'
  /media/{media_id}:
    $ref: './paths/media.yaml#/paths/~1media~1{media_id}'
  # Stories endpoints
  /stories:
    $ref: './paths/stories.yaml#/paths/~1stories'
  /stories/feed:
    $ref: './paths/stories.yaml#/paths/~1stories~1feed'
  /users/{user_handle}/stories:
    $ref: './paths/stories.yaml#/paths/~1users~1{user_handle}~1stories'
  /stories/{story_id}:
    $ref: './paths/stories.yaml#/paths/~1stories~1{story_id}'
  /stories/{story_id}/view:
    $ref: './paths/stories.yaml#/paths/~1stories~1{story_id}~1view'
  /stories/{story_id}/viewers:
    $ref: './paths/stories.yaml#/paths/~1stories~1{story_id}~1viewers'
  # Calls endpoints
  /calls/logs:
    $ref: './paths/calls.yaml#/paths/~1calls~1logs'
  /users/me/call-usage:
    $ref: './paths/calls.yaml#/paths/~1users~1me~1call-usage'
  /calls/initiate:
    $ref: './paths/calls.yaml#/paths/~1calls~1initiate'
  /calls/{call_id}/end:
    $ref: './paths/calls.yaml#/paths/~1calls~1{call_id}~1end'
  # Payments endpoints
  /payments/checkout:
    $ref: './paths/payments.yaml#/paths/~1payments~1checkout'
  /payments/history:
    $ref: './paths/payments.yaml#/paths/~1payments~1history'
  /payments/webhook:
    $ref: './paths/payments.yaml#/paths/~1payments~1webhook'
  # Subscriptions endpoints
  /subscriptions/me:
    $ref: './paths/subscriptions.yaml#/paths/~1subscriptions~1me'
  /subscriptions/gold/checkout:
    $ref: './paths/subscriptions.yaml#/paths/~1subscriptions~1gold~1checkout'
  # Support endpoints
  /support/tickets:
    $ref: './paths/support.yaml#/paths/~1support~1tickets'
  /support/tickets/{ticket_id}:
    $ref: './paths/support.yaml#/paths/~1support~1tickets~1{ticket_id}'
  /support/tickets/{ticket_id}/messages:
    $ref: './paths/support.yaml#/paths/~1support~1tickets~1{ticket_id}~1messages'

components:
  schemas:
    $ref: './schemas/schemas.yaml#/components/schemas'
  responses:
    $ref: './components/responses.yaml'
  # Security documentation
  x-security-documentation:
    $ref: './components/security.yaml'
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained after successful authentication. Required for most endpoints.
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for server-to-server communication (e.g., webhooks, integrations).
    refreshTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Refresh token used specifically for token refresh endpoint.

security:
  - bearerAuth: [] # Apply bearer token security globally by default