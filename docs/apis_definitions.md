Okay, this is a crucial step. Defining a clear API interface (or contract) upfront is essential, especially if you plan the Firebase -> Custom Backend migration. This API definition will guide both frontend and backend development.

We'll define the API endpoints primarily using a RESTful approach, which is common and well-understood. For each endpoint, we'll specify:

*   **HTTP Method:** GET, POST, PUT, PATCH, DELETE
*   **URL Path:** The endpoint URL structure.
*   **Purpose:** What the endpoint does.
*   **Authentication:** Required or Optional.
*   **Key Request Data:** Path parameters (`{param}`), Query parameters (`?param=value`), Request Body (JSON).
*   **Key Success Response Data:** Typical HTTP status code (e.g., 200 OK, 201 Created, 204 No Content), Response Body (JSON).

**Important Notes:**

*   **Authentication:** Assume most endpoints require authentication via a token (e.g., JWT Bearer token) sent in the `Authorization` header, unless specified otherwise.
*   **Authorization:** Specific roles (admin, owner, Gold member) might be required for certain operations. This is checked server-side but not detailed for every endpoint below.
*   **Error Handling:** Standard HTTP error codes (400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 500 Internal Server Error) should be used with informative JSON error bodies.
*   **Pagination:** Endpoints returning lists (messages, contacts, groups, etc.) should implement pagination (e.g., using `limit` and `offset` or cursor-based pagination).
*   **Data Structures:** Response/request bodies are described generally; a full OpenAPI/Swagger specification would detail the exact JSON schemas.
*   **WebSockets:** Real-time features (new messages, typing, presence, call signaling) rely heavily on WebSockets, which operate outside this REST API structure but are initiated/coordinated alongside it.
*   **API Versioning:** Consider versioning your API (e.g., `/api/v1/...`) from the start.

---

**API Endpoint Definitions:**

**1. Authentication & User Registration (`/api/v1/auth`)**

*   **`POST /auth/register`**
    *   Purpose: Create a new user account. Allows for potentially anonymous signup (handle only) or with email/phone.
    *   Auth: None
    *   Request Body: `{ user_handle? (optional, server generates if missing), password?, email?, phone_number?, recovery_pin?, recovery_phrase? (client generates/stores first?) }`
    *   Success Response: `201 Created` - `{ user_id, user_handle, tokens: { access_token, refresh_token } }`
*   **`POST /auth/login`**
    *   Purpose: Log in a user.
    *   Auth: None
    *   Request Body: `{ identifier (user_handle/email/phone), password }`
    *   Success Response: `200 OK` - `{ user_id, user_handle, tokens: { access_token, refresh_token }, requires_2fa? }`
*   **`POST /auth/login/2fa`**
    *   Purpose: Complete login with 2FA code after initial login indicates it's required.
    *   Auth: Requires a temporary token from initial login.
    *   Request Body: `{ temporary_token, totp_code }`
    *   Success Response: `200 OK` - `{ user_id, user_handle, tokens: { access_token, refresh_token } }`
*   **`POST /auth/refresh`**
    *   Purpose: Obtain a new access token using a refresh token.
    *   Auth: None (uses refresh token in body)
    *   Request Body: `{ refresh_token }`
    *   Success Response: `200 OK` - `{ access_token, refresh_token (optional, might rotate) }`
*   **`POST /auth/logout`**
    *   Purpose: Log out the user (server invalidates refresh token).
    *   Auth: Required
    *   Request Body: `{ refresh_token? (optional, to invalidate specific one) }`
    *   Success Response: `204 No Content`
*   **`POST /auth/password/change`**
    *   Purpose: Change password while logged in.
    *   Auth: Required
    *   Request Body: `{ current_password, new_password }`
    *   Success Response: `204 No Content`
*   **`POST /auth/recovery/request`**
    *   Purpose: Initiate account recovery using Handle + Secret Phrase + PIN.
    *   Auth: None
    *   Request Body: `{ user_handle, recovery_phrase_words: [...], recovery_pin }`
    *   Success Response: `200 OK` - `{ recovery_token }` (To use for setting new password)
*   **`POST /auth/recovery/confirm`**
    *   Purpose: Set a new password using the recovery token.
    *   Auth: None
    *   Request Body: `{ recovery_token, new_password }`
    *   Success Response: `200 OK` - `{ tokens: { access_token, refresh_token } }` (Logs user in)
*   **`POST /auth/2fa/setup`**
    *   Purpose: Start the setup process for TOTP 2FA.
    *   Auth: Required
    *   Request Body: `{}`
    *   Success Response: `200 OK` - `{ otpauth_url, secret_key (for manual entry) }`
*   **`POST /auth/2fa/verify`**
    *   Purpose: Verify the TOTP code to enable 2FA.
    *   Auth: Required
    *   Request Body: `{ totp_code }`
    *   Success Response: `204 No Content`
*   **`DELETE /auth/2fa`**
    *   Purpose: Disable TOTP 2FA (might require password/current code).
    *   Auth: Required
    *   Request Body: `{ password?, totp_code? }`
    *   Success Response: `204 No Content`

**2. User Profiles & Settings (`/api/v1/users`)**

*   **`GET /users/me`**
    *   Purpose: Get the profile of the currently authenticated user.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ user_id, user_handle, display_name, profile_picture_url, bio, email?, phone_number?, subscription_tier, verification_status, created_at }`
*   **`PATCH /users/me`**
    *   Purpose: Update the profile of the currently authenticated user.
    *   Auth: Required
    *   Request Body: `{ display_name?, bio?, profile_picture_url? }` (Partial updates)
    *   Success Response: `200 OK` - Updated User Profile Object
*   **`GET /users/{user_handle}`**
    *   Purpose: Get the public profile of another user.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ user_id, user_handle, display_name, profile_picture_url, bio, verification_status }` (Limited info)
*   **`GET /users/me/settings`**
    *   Purpose: Get the detailed settings of the current user.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ permissions: {...}, notifications: {...}, 2fa_methods: [...], theme, language, ... }` (Corresponds to `users.settings` JSONB)
*   **`PATCH /users/me/settings`**
    *   Purpose: Update specific settings.
    *   Auth: Required
    *   Request Body: `{ permissions?: {...}, notifications?: {...}, theme?, language?, face_id_enabled?, fingerprint_enabled? }` (Use JSON Patch or specific fields)
    *   Success Response: `200 OK` - Updated Settings Object
*   **`POST /users/me/delete/request`**
    *   Purpose: Request account deletion (schedules it).
    *   Auth: Required
    *   Request Body: `{ password? (Confirmation) }`
    *   Success Response: `202 Accepted` - `{ scheduled_deletion_at }`
*   **`DELETE /users/me/delete/request`**
    *   Purpose: Cancel a pending account deletion request.
    *   Auth: Required
    *   Success Response: `204 No Content`
*   **`POST /users/me/remote-wipe/setup`**
    *   Purpose: Create/update the PIN for remote wipe.
    *   Auth: Required
    *   Request Body: `{ password, remote_wipe_pin }`
    *   Success Response: `204 No Content`
*   **`POST /users/remote-wipe`**
    *   Purpose: Initiate remote wipe of an old account from a new account.
    *   Auth: Required (on the *new* account)
    *   Request Body: `{ old_user_handle, old_recovery_phrase_words: [...], remote_wipe_pin }`
    *   Success Response: `202 Accepted` (Deletion is scheduled)
*   **`POST /users/me/verification/request`**
    *   Purpose: Initiate the account verification process (V2 feature, might require payment).
    *   Auth: Required
    *   Request Body: `{ details?, payment_token? }`
    *   Success Response: `202 Accepted` - `{ verification_status: 'pending' }`

**3. Contacts & Relationships (`/api/v1/contacts`, `/api/v1/blocks`)**

*   **`GET /contacts`**
    *   Purpose: Get the user's contact list.
    *   Auth: Required
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ contacts: [{ user_id, user_handle, display_name (custom), profile_picture_url }, ...], total_count }`
*   **`GET /contacts/favorites`**
    *   Purpose: Get the user's favorite contacts.
    *   Auth: Required
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ contacts: [{ user_id, user_handle, display_name (custom), profile_picture_url, is_favorite: true }, ...], total_count }`
*   **`POST /contacts`**
    *   Purpose: Add another user as a contact.
    *   Auth: Required
    *   Request Body: `{ contact_user_handle, display_name? (Optional custom name) }`
    *   Success Response: `201 Created` - `{ user_id, user_handle, display_name, ... }` (The added contact info)
*   **`PATCH /contacts/{contact_user_handle}`**
    *   Purpose: Update the custom display name for a contact.
    *   Auth: Required
    *   Request Body: `{ display_name }`
    *   Success Response: `200 OK` - Updated contact info.
*   **`DELETE /contacts/{contact_user_handle}`**
    *   Purpose: Remove a user from the contact list.
    *   Auth: Required
    *   Success Response: `204 No Content`
*   **`POST /blocks`**
    *   Purpose: Block another user.
    *   Auth: Required
    *   Request Body: `{ blocked_user_handle }`
    *   Success Response: `204 No Content`
*   **`GET /blocks`**
    *   Purpose: Get the list of users blocked by the current user.
    *   Auth: Required
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ blocked_users: [{ user_id, user_handle, display_name, ... }, ...], total_count }`
*   **`DELETE /blocks/{blocked_user_handle}`**
    *   Purpose: Unblock a user.
    *   Auth: Required
    *   Success Response: `204 No Content`

**3.1. Contact Groups (`/api/v1/contact-groups`)**

*   **`GET /contact-groups`**
    *   Purpose: Get the user's contact groups.
    *   Auth: Required
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ groups: [{ id, name, description, member_ids, member_count, created_at, updated_at }, ...], total_count }`
*   **`POST /contact-groups`**
    *   Purpose: Create a new contact group.
    *   Auth: Required
    *   Request Body: `{ name, description?, member_ids?: [contact_id1, contact_id2, ...] }`
    *   Success Response: `201 Created` - `{ id, name, description, member_ids, member_count, created_at, updated_at }`
*   **`GET /contact-groups/{group_id}`**
    *   Purpose: Get details of a specific contact group.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ id, name, description, member_ids, member_count, created_at, updated_at }`
*   **`PUT /contact-groups/{group_id}`**
    *   Purpose: Update a contact group.
    *   Auth: Required
    *   Request Body: `{ name, description? }`
    *   Success Response: `200 OK` - Updated Contact Group Object
*   **`DELETE /contact-groups/{group_id}`**
    *   Purpose: Delete a contact group.
    *   Auth: Required
    *   Success Response: `204 No Content`
*   **`POST /contact-groups/{group_id}/members/{contact_id}`**
    *   Purpose: Add a contact to a group.
    *   Auth: Required
    *   Success Response: `200 OK` - Updated Contact Group Object
*   **`DELETE /contact-groups/{group_id}/members/{contact_id}`**
    *   Purpose: Remove a contact from a group.
    *   Auth: Required
    *   Success Response: `200 OK` - Updated Contact Group Object

**4. Conversations & Messaging (`/api/v1/conversations`, `/api/v1/messages`)**

*   **`GET /conversations`**
    *   Purpose: Get the user's list of conversations (chats, groups, channels) from the denormalized table.
    *   Auth: Required
    *   Query Params: `?limit=30&offset=0`
    *   Success Response: `200 OK` - `{ conversations: [ { user_id, conversation_id, conversation_type, privacy_type, conversation_name, conversation_avatar_url, last_message_preview, last_activity_timestamp, unread_count, is_muted, is_pinned }, ... ], total_count }`
*   **`GET /conversations/{conversation_id}/messages`**
    *   Purpose: Get messages for a specific conversation (1-to-1, group, or channel).
    *   Auth: Required (User must be participant/subscriber)
    *   Query Params: `?limit=50&before_message_id?={message_id}` (Cursor pagination recommended)
    *   Success Response: `200 OK` - `{ messages: [ { message_id, sender: { user_id, user_handle, display_name, avatar_url }, text_content?, media: [...], sent_at, edited_at?, reply_to_message_id?, moderation_status }, ... ] }`
*   **`POST /conversations/{conversation_id}/messages`**
    *   Purpose: Send a message to a conversation.
    *   Auth: Required (User must be participant/member with send permissions)
    *   Request Body: `{ text_content?, media_ids?: ["uuid1", "uuid2"], reply_to_message_id? }`
    *   Success Response: `201 Created` - The created Message Object (Server assigns `message_id`, `sent_at`)
*   **`PATCH /messages/{message_id}`**
    *   Purpose: Edit a message the user sent.
    *   Auth: Required (User must be sender, within time limits)
    *   Request Body: `{ text_content?, media_ids? }`
    *   Success Response: `200 OK` - Updated Message Object
*   **`DELETE /messages/{message_id}`**
    *   Purpose: Delete a message (soft delete).
    *   Auth: Required (User must be sender or admin with permissions)
    *   Success Response: `204 No Content`
*   **`POST /conversations/{conversation_id}/read`**
    *   Purpose: Mark all messages in the conversation up to a certain point (e.g., the latest) as read by the user. Triggers `user_conversations.unread_count` update.
    *   Auth: Required
    *   Request Body: `{ last_read_message_id? (optional) }`
    *   Success Response: `204 No Content`
*   **`PATCH /conversations/{conversation_id}/settings`**
    *   Purpose: Update user-specific settings for a conversation (mute/pin).
    *   Auth: Required
    *   Request Body: `{ is_muted?, is_pinned? }`
    *   Success Response: `204 No Content`

**5. Group Management (`/api/v1/groups`)**

*   **`POST /groups`**
    *   Purpose: Create a new group. Might require payment initiation.
    *   Auth: Required
    *   Request Body: `{ name, description?, privacy_type ('public'|'private'|'secret'), initial_members?: [user_handle1, user_handle2], payment_token? (if needed) }`
    *   Success Response: `201 Created` or `202 Accepted` (if payment pending) - Group Object
*   **`GET /groups/{group_id}`**
    *   Purpose: Get details of a specific group.
    *   Auth: Required (User must be member or group is public)
    *   Success Response: `200 OK` - `{ group_id, name, description, picture_url, privacy_type, creator: {...}, member_count (approx), settings }`
*   **`PATCH /groups/{group_id}`**
    *   Purpose: Update group details (name, description, picture).
    *   Auth: Required (Admin/Owner)
    *   Request Body: `{ name?, description?, picture_url? }`
    *   Success Response: `200 OK` - Updated Group Object
*   **`DELETE /groups/{group_id}`**
    *   Purpose: Delete a group.
    *   Auth: Required (Owner)
    *   Success Response: `204 No Content`
*   **`GET /groups/{group_id}/members`**
    *   Purpose: Get the list of members in a group.
    *   Auth: Required (Member)
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ members: [ { user: { user_id, user_handle, display_name, avatar_url }, role, joined_at }, ... ], total_count }`
*   **`POST /groups/{group_id}/members`**
    *   Purpose: Add members to a group (usually via invite link or direct add by admin).
    *   Auth: Required (Admin or via invite link)
    *   Request Body: `{ user_handles: [user_handle1, ...] }` (For admin add)
    *   Success Response: `201 Created` or `204 No Content`
*   **`DELETE /groups/{group_id}/members/{user_handle_or_id}`**
    *   Purpose: Remove/kick a member from a group.
    *   Auth: Required (Admin/Owner, or self-leave if `user_handle_or_id == 'me'`)
    *   Success Response: `204 No Content`
*   **`PATCH /groups/{group_id}/members/{user_handle_or_id}`**
    *   Purpose: Change a member's role.
    *   Auth: Required (Admin/Owner)
    *   Request Body: `{ role: ('admin'|'member') }`
    *   Success Response: `200 OK` - Updated Member Object
*   **`GET /groups/{group_id}/invite-link`**
    *   Purpose: Get/regenerate the invite link for a private/secret group.
    *   Auth: Required (Admin/Owner)
    *   Success Response: `200 OK` - `{ invite_link: "...", invite_link_token: "..." }`
*   **`POST /groups/join/{invite_link_token}`**
    *   Purpose: Join a private/secret group using an invite token.
    *   Auth: Required
    *   Success Response: `200 OK` - Joined Group Object
*   **`GET /groups/search`**
    *   Purpose: Search for public groups by name.
    *   Auth: Required
    *   Query Params: `?q={search_query}&limit=20`
    *   Success Response: `200 OK` - `{ groups: [ { group_id, name, picture_url, member_count }, ... ] }`
*   **`POST /groups/{group_id}/link-channel`**
    *   Purpose: Link a channel to this group for discussion.
    *   Auth: Required (Admin/Owner of both?)
    *   Request Body: `{ channel_id }`
    *   Success Response: `204 No Content`

**6. Channel Management (`/api/v1/channels`)**

*   *(Endpoints are very similar to Group Management, replacing `group` with `channel`, `member` with `subscriber`)*
*   **`POST /channels`** - Create Channel
*   **`GET /channels/{channel_id}`** - Get Channel Info
*   **`PATCH /channels/{channel_id}`** - Update Channel Info
*   **`DELETE /channels/{channel_id}`** - Delete Channel
*   **`GET /channels/{channel_id}/subscribers`** - Get Subscribers
*   **`DELETE /channels/{channel_id}/subscribers/{user_handle_or_id}`** - Remove Subscriber (Admin/Owner or self-leave 'me')
*   **`PATCH /channels/{channel_id}/subscribers/{user_handle_or_id}`** - Change Subscriber Role (Admin/Owner)
*   **`GET /channels/{channel_id}/invite-link`** - Get Invite Link
*   **`POST /channels/join/{invite_link_token}`** - Join via Invite Link
*   **`GET /channels/search`** - Search Public Channels
*   **`POST /channels/{channel_id}/subscribe`** - Subscribe to public channel (Alternative to Join)
*   **`DELETE /channels/{channel_id}/subscribe`** - Unsubscribe (Alias for leaving)

**7. Story Management (`/api/v1/stories`)**

*   **`POST /stories`**
    *   Purpose: Create a new story. Requires prior media upload.
    *   Auth: Required
    *   Request Body: `{ base_media_id, caption?, elements: [ { element_type, position_x, position_y, element_data: {...} }, ... ] }`
    *   Success Response: `201 Created` - Story Object
*   **`GET /stories/feed`**
    *   Purpose: Get the story feed (stories from contacts, needs logic). Grouped by user.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ feed: [ { user: {...}, stories: [ { story_id, thumbnail_url, expires_at, is_viewed? }, ... ] }, ... ] }`
*   **`GET /users/{user_handle}/stories`**
    *   Purpose: Get all active stories for a specific user.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ stories: [ { story_id, base_media_url, elements: [...], caption, expires_at, viewer_count? }, ... ] }`
*   **`POST /stories/{story_id}/view`**
    *   Purpose: Mark a story as viewed by the current user.
    *   Auth: Required
    *   Success Response: `204 No Content`
*   **`DELETE /stories/{story_id}`**
    *   Purpose: Delete a story created by the user.
    *   Auth: Required (Creator)
    *   Success Response: `204 No Content`
*   **`GET /stories/{story_id}/viewers`**
    *   Purpose: Get the list of users who viewed the user's own story.
    *   Auth: Required (Creator)
    *   Success Response: `200 OK` - `{ viewers: [ { user: {...}, viewed_at }, ... ] }`

**8. Call Management (`/api/v1/calls`)**

*   **`GET /calls/logs`**
    *   Purpose: Get the user's call history.
    *   Auth: Required
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ logs: [ { call_log_id, other_party: {...}, start_time, duration_seconds, call_type, status }, ... ] }`
*   **`GET /users/me/call-usage`**
    *   Purpose: Get remaining call time for the current month (for non-Gold users).
    *   Auth: Required
    *   Success Response: `200 OK` - `{ total_duration_seconds, limit_seconds, remaining_seconds }`
*   **`POST /calls/initiate`**
    *   Purpose: Set up signaling for a call (WebRTC).
    *   Auth: Required
    *   Request Body: `{ callee_user_handle, call_type ('audio'|'video') }`
    *   Success Response: `200 OK` - `{ call_id, signaling_server_url, ice_servers: [...], token (for auth with signaling server) }`
    *   *Note: Actual call signaling (offer, answer, candidates) happens via WebSockets connected to the signaling server.*
*   **`POST /calls/{call_id}/end`**
    *   Purpose: API endpoint to notify the backend the call ended (if WebSocket failsafe needed or for logging).
    *   Auth: Required
    *   Request Body: `{ duration_seconds }`
    *   Success Response: `204 No Content`

**9. Payments & Subscriptions (`/api/v1/payments`, `/api/v1/subscriptions`)**

*   **`GET /subscriptions/me`**
    *   Purpose: Get the current user's subscription status.
    *   Auth: Required
    *   Success Response: `200 OK` - `{ subscription_id?, user_id, tier, status, end_date?, ... }`
*   **`POST /subscriptions/gold/checkout`**
    *   Purpose: Initiate the payment process for Gold subscription.
    *   Auth: Required
    *   Request Body: `{ success_url, cancel_url }` (Client URLs for redirect)
    *   Success Response: `200 OK` - `{ checkout_url? (for redirect), client_secret? (for Stripe Elements), payment_processor }`
*   **`DELETE /subscriptions/me`**
    *   Purpose: Request cancellation of the current subscription (usually at period end).
    *   Auth: Required
    *   Success Response: `200 OK` - Updated Subscription Object showing cancellation pending.
*   **`POST /payments/checkout`**
    *   Purpose: Initiate payment for a one-time action (e.g., creating extra group/channel).
    *   Auth: Required
    *   Request Body: `{ purpose: ('create_public_group'|...), related_entity_details?: {...}, success_url, cancel_url }`
    *   Success Response: `200 OK` - `{ checkout_url?, client_secret?, payment_processor }`
*   **`GET /payments/history`**
    *   Purpose: Get the user's payment history.
    *   Auth: Required
    *   Query Params: `?limit=20&offset=0`
    *   Success Response: `200 OK` - `{ payments: [ { payment_id, amount, currency, status, purpose, created_at }, ... ] }`

**10. Moderation & Reporting (`/api/v1/reports`)**

*   **`POST /reports`**
    *   Purpose: Report a piece of content or a user.
    *   Auth: Required
    *   Request Body: `{ reported_entity_type: ('message'|'user'|'group'|...), reported_entity_id, reason: ('racism'|'sexual_content'|...), details? }`
    *   Success Response: `202 Accepted`

**11. Media Upload (`/api/v1/media`)**

*   **`POST /media/upload/request`**
    *   Purpose: Get a pre-signed URL to upload media directly to object storage.
    *   Auth: Required
    *   Request Body: `{ file_name, content_type, file_size, purpose ('profile_picture'|'message_media'|'story_base'|...) }`
    *   Success Response: `200 OK` - `{ media_id (provisional), upload_url, required_form_fields? (if POST upload) }`
*   **`POST /media/upload/complete`** (Optional - Can use storage webhooks)
    *   Purpose: Notify backend that direct upload finished successfully.
    *   Auth: Required
    *   Request Body: `{ media_id, final_url?, file_size?, metadata? }`
    *   Success Response: `200 OK` - Final Media Object details

**12. General / Miscellaneous (`/api/v1/content`, `/api/v1/support`)**

*   **`GET /content/{content_type}`**
    *   Purpose: Get static content like terms, privacy policy, community rules.
    *   Auth: Optional/None
    *   Path Param: `content_type` = `terms`, `privacy`, `community-rules`
    *   Success Response: `200 OK` - `{ content_type, title, body_html }`
*   **`POST /support/tickets`**
    *   Purpose: Create a support ticket.
    *   Auth: Required
    *   Request Body: `{ subject, message_body }`
    *   Success Response: `201 Created` - Ticket Object
*   **`GET /support/tickets`**
    *   Purpose: Get the user's support tickets.
    *   Auth: Required
    *   Query Params: `?limit=10&offset=0`
    *   Success Response: `200 OK` - `{ tickets: [ { ticket_id, subject, status, last_updated_at }, ... ] }`
*   **`GET /support/tickets/{ticket_id}/messages`**
    *   Purpose: Get messages for a specific support ticket.
    *   Auth: Required (User must own ticket)
    *   Query Params: `?limit=50&offset=0`
    *   Success Response: `200 OK` - `{ messages: [ { sender_type, message_body, sent_at }, ... ] }`
*   **`POST /support/tickets/{ticket_id}/messages`**
    *   Purpose: Add a reply to a support ticket.
    *   Auth: Required (User must own ticket)
    *   Request Body: `{ message_body }`
    *   Success Response: `201 Created` - Message Object

---

This detailed list provides a solid starting point for your API design. Remember to refine the exact request/response schemas and consider security implications for each endpoint during implementation.