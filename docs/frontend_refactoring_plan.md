# Frontend Android App Refactoring Plan (Post-Backend Changes)

## I. Executive Summary & Goals

The primary goal of this frontend refactoring is to align the Android application with the recent backend updates. This involves:
1.  Implementing the new server-side logout mechanism.
2.  Replacing the previous (potentially placeholder or email-based) account recovery flow with the new credential-based recovery system (<PERSON><PERSON> Handle, Recovery Phrase, Recovery PIN).
3.  Ensuring login error messages are displayed generically.
4.  Verifying that token refresh logic correctly interacts with the more reliable backend refresh mechanism.

## II. Affected Frontend Areas & Proposed Modifications

**A. Authentication - Logout**

*   **Corresponding Backend Change:** Introduction of `POST /auth/logout` endpoint.
*   **Frontend Impact:** The app needs to call this new endpoint for user logout.
*   **Proposed Frontend Modifications:**
    1.  **API Service Update:**
        *   Add a new function to your API service interface (e.g., Retrofit interface) to define the `POST /auth/logout` call.
        *   Request body: `{"refresh_token": "string"}`.
        *   Expected success response: HTTP 204 No Content.
    2.  **Auth Repository/ViewModel Update:**
        *   In the `AuthRepository` (or equivalent data layer component), implement a function that calls the new API service logout method. This function should retrieve the current refresh token from local storage.
        *   In the relevant `AuthViewModel` (or equivalent presentation layer component), expose a function that triggers the logout process via the repository.
    3.  **UI Logic Update:**
        *   When the user initiates logout (e.g., taps a "Logout" button):
            *   Call the `AuthViewModel`'s logout function.
            *   Upon successful API call (HTTP 204):
                *   Clear all locally stored authentication tokens (access token, refresh token) and any other user session data (e.g., from SharedPreferences, DataStore, or local database).
                *   Navigate the user to the login screen.
            *   Handle potential API errors gracefully (e.g., network issues, server errors).
    *   **Files to Consider (Typical Android Structure):**
        *   `AuthApiService.kt` (or similar for Retrofit/Ktor interface)
        *   `AuthRepository.kt`
        *   `AuthViewModel.kt`
        *   Logout button's `OnClickListener` in relevant Activity/Fragment.
        *   Token storage utility/manager class.

**B. Authentication - Account Recovery**

*   **Corresponding Backend Change:** Replacement of email-based password reset with a two-step credential-based recovery:
    1.  `POST /auth/initiate-recovery` (User Handle, Recovery Phrase, PIN) -> returns `recovery_session_token`.
    2.  `POST /auth/reset-password` (Recovery Session Token, New Password).
*   **Frontend Impact:** This is a major change. Any existing "Forgot Password" UI/logic based on email must be removed and replaced with the new flow.
*   **Proposed Frontend Modifications:**
    1.  **Remove Old Recovery Flow:**
        *   Delete any Activities, Fragments, ViewModels, and Repository methods related to an email-based password reset.
    2.  **API Service Update:**
        *   Add a function for `POST /auth/initiate-recovery`.
            *   Request: `{"user_handle": "string", "recovery_phrase": "string", "recovery_pin": "string"}`.
            *   Response: `{"recovery_session_token": "string"}`.
        *   Add/Update a function for `POST /auth/reset-password`.
            *   Request: `{"token": "string" (recovery_session_token), "new_password": "string"}`.
            *   Response: `{"message": "string"}`.
    3.  **New UI Flow (Activities/Fragments & ViewModels):**
        *   **Screen 1: Initiate Recovery**
            *   UI: Input fields for "User Handle", "Secret Recovery Phrase", and "Recovery PIN". A "Submit" or "Verify Credentials" button.
            *   ViewModel (`InitiateRecoveryViewModel.kt`):
                *   Holds state for input fields, loading status, error messages.
                *   Function to call the repository method for `initiate-recovery`.
                *   On success (API returns `recovery_session_token`), store this token temporarily (e.g., in ViewModel state or pass to next screen) and navigate to Screen 2.
                *   Handle errors (400, 401, 404, 500) by displaying appropriate messages.
        *   **Screen 2: Reset Password**
            *   UI: Input fields for "New Password" and "Confirm New Password". A "Reset Password" button.
            *   ViewModel (`ResetPasswordViewModel.kt`):
                *   Receives/holds the `recovery_session_token` from Screen 1.
                *   Holds state for password fields, loading status, error messages.
                *   Function to call the repository method for `reset-password`.
                *   On success (API returns success message), navigate to the login screen with a success indication.
                *   Handle errors (400 for invalid token/password, 500) by displaying messages.
    4.  **Auth Repository Update:**
        *   Implement functions to call the new `initiate-recovery` and `reset-password` API service methods.
    5.  **Navigation:** Update navigation graphs to include these new screens, accessible from the login screen (e.g., a "Trouble logging in?" or "Recover Account" link).
    6.  **Diagram: Account Recovery Flow**
        ```mermaid
        graph TD
            A[Login Screen] -- "Recover Account" --> B(Initiate Recovery Screen: Enter Handle, Phrase, PIN);
            B -- Submit --> C{Call POST /auth/initiate-recovery};
            C -- Success (recovery_session_token) --> D(Reset Password Screen: Enter New Password);
            C -- Failure (e.g., 401) --> B;
            D -- Submit --> E{Call POST /auth/reset-password with session_token};
            E -- Success --> F(Login Screen with Success Message);
            E -- Failure (e.g., 400) --> D;
        end
        ```
    *   **Files to Consider (Typical Android Structure):**
        *   `AuthApiService.kt`
        *   `AuthRepository.kt`
        *   `InitiateRecoveryViewModel.kt`, `InitiateRecoveryFragment.kt` (or Activity)
        *   `ResetPasswordViewModel.kt`, `ResetPasswordFragment.kt` (or Activity)
        *   Navigation graph XML file.
        *   String resources for new UI text and error messages.

**C. Authentication - Login Error Handling**

*   **Corresponding Backend Change:** Login endpoint now returns a generic "invalid credentials" error for both "user not found" and "incorrect password" scenarios.
*   **Frontend Impact:** The frontend should display a single, generic error message for login failures.
*   **Proposed Frontend Modifications:**
    1.  **Login UI/ViewModel Update:**
        *   When the login API call (e.g., to `POST /auth/login`) fails with an authentication error (e.g., 401 Unauthorized, or a specific error code/message that now represents generic failure):
            *   Display a generic error message to the user, such as "Invalid credentials. Please check your details and try again." or "Incorrect User Handle or Password."
            *   Avoid messages like "User not found" or "Incorrect password" specifically, as the backend no longer provides this distinction.
    *   **Files to Consider (Typical Android Structure):**
        *   `LoginViewModel.kt`
        *   `LoginFragment.kt` (or Activity)
        *   Error handling logic within the `AuthRepository.kt` or API call interceptors.

**D. Authentication - Token Refresh Mechanism**

*   **Corresponding Backend Change:** Refresh token `expires_at` is now correctly set in the database using the refresh token's actual (longer) lifetime.
*   **Frontend Impact:** This backend fix should make the existing frontend token refresh logic more reliable, as refresh tokens will not be considered prematurely expired by the database.
*   **Proposed Frontend Modifications:**
    1.  **No Direct Code Change (Likely):** If the frontend already has a standard token refresh mechanism (e.g., using an OkHttp Authenticator or Interceptor to catch 401s, call a `/auth/refresh` endpoint, and retry the original request), this mechanism should now work more reliably.
    2.  **Thorough Testing:**
        *   Extensively test scenarios involving access token expiration and refresh:
            *   App in foreground when token expires.
            *   App brought to foreground after token has expired.
            *   Actions performed right as token is about to expire.
        *   Verify that new access and refresh tokens are correctly received and stored after a successful refresh.
        *   Verify behavior when the refresh token itself is expired or invalid (user should be logged out and redirected to login).
    *   **Files to Consider (Typical Android Structure):**
        *   OkHttp `Authenticator` or `Interceptor` classes.
        *   `AuthApiService.kt` (for the refresh token call).
        *   Token storage utility/manager class.

**III. General Considerations & Testing**

1.  **String Resources:** Add all new user-facing strings (button labels, input field hints, error messages, success messages) to `strings.xml` for localization.
2.  **Error Handling:** Ensure robust error handling for all new API calls (network errors, server errors, specific HTTP status codes like 400, 401, 403, 404, 500). Provide clear feedback to the user.
3.  **State Management:** Utilize ViewModels and LiveData/StateFlow/RxJava to manage UI state effectively during API calls (loading indicators, error states, success states).
4.  **Input Validation:** Implement client-side input validation for new forms (e.g., non-empty fields, password complexity if desired on client-side before sending to backend).
5.  **Testing:**
    *   **Unit Tests:** For ViewModels, Repositories, and any utility classes.
    *   **Integration Tests:** For API service calls (consider using mock web server).
    *   **UI Tests (Espresso/Compose Tests):**
        *   Verify the new logout flow.
        *   Verify the complete new account recovery flow (both screens, successful and error paths).
        *   Verify generic error message display on login.
        *   Re-test token refresh scenarios thoroughly.
6.  **Code Review:** Ensure all changes are peer-reviewed.