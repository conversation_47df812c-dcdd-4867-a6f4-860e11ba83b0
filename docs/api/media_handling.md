# Media Handling Specification

This document defines the media handling protocol used by the Meena application, covering uploads, downloads, encryption, and background processing.

## Overview

Meena implements a robust media handling system that supports:

1. **End-to-End Encryption** for media files
2. **Chunked Uploads** for large files
3. **Background Processing** for uploads and downloads

These features ensure that media sharing is secure, reliable, and provides a good user experience even with large files or unstable network connections.

## 1. End-to-End Encryption for Media Files

### Encryption Process

Media files are encrypted using the same E2EE principles as text messages:

1. **Generate Media Key**: A unique 256-bit AES key is generated for each media file
2. **Encrypt Media**: The file is encrypted using AES-GCM with the media key
3. **Encrypt Media Key**: The media key is encrypted with the recipient's public key
4. **Upload Encrypted Media**: The encrypted file is uploaded to the server
5. **Send Encrypted Key**: The encrypted media key is sent in the message

### Implementation Details

#### Client-Side Encryption

```kotlin
// Pseudocode for media encryption
fun encryptMedia(file: File, recipientPublicKey: PublicKey): EncryptedMedia {
    // Generate a random AES key
    val mediaKey = generateRandomAESKey()
    
    // Encrypt the file with the media key
    val encryptedFile = encryptFileWithAES(file, mediaKey)
    
    // Encrypt the media key with the recipient's public key
    val encryptedMediaKey = encryptWithRSA(mediaKey, recipientPublicKey)
    
    return EncryptedMedia(encryptedFile, encryptedMediaKey)
}
```

#### Client-Side Decryption

```kotlin
// Pseudocode for media decryption
fun decryptMedia(encryptedFile: File, encryptedMediaKey: ByteArray, privateKey: PrivateKey): File {
    // Decrypt the media key with the private key
    val mediaKey = decryptWithRSA(encryptedMediaKey, privateKey)
    
    // Decrypt the file with the media key
    return decryptFileWithAES(encryptedFile, mediaKey)
}
```

### API Endpoints

#### Upload Encrypted Media

```
POST /api/v1/media/upload/encrypted
```

Request:
```json
{
  "file_name": "encrypted_image.bin",
  "content_type": "application/octet-stream",
  "file_size": 1024000,
  "is_encrypted": true
}
```

Response:
```json
{
  "media_id": "media123",
  "upload_url": "https://storage.example.com/upload?token=abc123",
  "expires_at": "2023-07-15T12:34:56Z"
}
```

## 2. Chunked Uploads for Large Files

### Chunking Process

Large files are split into manageable chunks (e.g., 5MB each) and uploaded sequentially:

1. **Initiate Upload**: Request an upload session for the complete file
2. **Upload Chunks**: Upload each chunk with its sequence number
3. **Complete Upload**: Signal that all chunks have been uploaded

### Implementation Details

#### Chunk Management

```kotlin
// Pseudocode for chunked upload
fun uploadLargeFile(file: File, chunkSize: Int = 5 * 1024 * 1024): MediaAttachment {
    // Initiate upload session
    val session = mediaApi.initiateChunkedUpload(file.name, file.length(), file.contentType)
    
    // Split file into chunks
    val chunks = file.splitIntoChunks(chunkSize)
    
    // Upload each chunk
    chunks.forEachIndexed { index, chunk ->
        mediaApi.uploadChunk(session.uploadId, index, chunks.size, chunk)
    }
    
    // Complete upload
    return mediaApi.completeChunkedUpload(session.uploadId)
}
```

### API Endpoints

#### Initiate Chunked Upload

```
POST /api/v1/media/upload/chunked/init
```

Request:
```json
{
  "file_name": "large_video.mp4",
  "content_type": "video/mp4",
  "total_size": 104857600,
  "chunk_size": 5242880,
  "is_encrypted": false
}
```

Response:
```json
{
  "upload_id": "upload123",
  "expires_at": "2023-07-15T12:34:56Z"
}
```

#### Upload Chunk

```
PUT /api/v1/media/upload/chunked/{upload_id}/{chunk_index}
```

Request Body: Binary chunk data

Response:
```json
{
  "chunk_index": 1,
  "received_size": 5242880,
  "total_received": 10485760
}
```

#### Complete Chunked Upload

```
POST /api/v1/media/upload/chunked/{upload_id}/complete
```

Response:
```json
{
  "media_id": "media123",
  "url": "https://storage.example.com/media123",
  "file_name": "large_video.mp4",
  "content_type": "video/mp4",
  "size": 104857600
}
```

## 3. Background Uploads/Downloads

### Background Processing

Media operations continue even when the app is in the background:

1. **WorkManager Tasks**: For operations that can be deferred
2. **Foreground Service**: For immediate operations that need to continue

### Implementation Details

#### WorkManager for Uploads

```kotlin
// Pseudocode for background upload with WorkManager
fun scheduleMediaUpload(uri: Uri, messageId: String) {
    val uploadWorkRequest = OneTimeWorkRequestBuilder<MediaUploadWorker>()
        .setInputData(workDataOf(
            "uri" to uri.toString(),
            "messageId" to messageId
        ))
        .setConstraints(Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build())
        .build()
    
    WorkManager.getInstance(context).enqueue(uploadWorkRequest)
}
```

#### Foreground Service for Downloads

```kotlin
// Pseudocode for foreground service download
fun startMediaDownload(mediaId: String, url: String) {
    val intent = Intent(context, MediaDownloadService::class.java).apply {
        action = MediaDownloadService.ACTION_DOWNLOAD
        putExtra(MediaDownloadService.EXTRA_MEDIA_ID, mediaId)
        putExtra(MediaDownloadService.EXTRA_URL, url)
    }
    
    context.startForegroundService(intent)
}
```

### Service Configuration

The app must declare the following in the AndroidManifest.xml:

```xml
<service
    android:name=".services.MediaUploadService"
    android:foregroundServiceType="dataSync"
    android:exported="false" />

<service
    android:name=".services.MediaDownloadService"
    android:foregroundServiceType="dataSync"
    android:exported="false" />
```

## Integration with E2EE

When using both E2EE and chunked uploads, the process is:

1. Encrypt the entire file first
2. Split the encrypted file into chunks
3. Upload chunks using the chunked upload API
4. Send the encrypted media key in the message

## Error Handling and Recovery

### Upload Recovery

If a chunked upload fails:

1. The client can query the server for the last successfully uploaded chunk
2. Resume uploading from the next chunk
3. Complete the upload once all chunks are uploaded

### Download Recovery

If a download fails:

1. The client can use HTTP Range requests to resume from the last received byte
2. Continue downloading until the file is complete

## Security Considerations

1. **Temporary Files**: Encrypted files should be stored in secure, app-private storage
2. **Key Management**: Media keys should never be stored unencrypted
3. **Metadata Protection**: File names should be generic or encrypted
4. **Secure Deletion**: Temporary files should be securely deleted after use

## Performance Optimization

1. **Parallel Chunk Uploads**: Multiple chunks can be uploaded in parallel
2. **Adaptive Chunk Size**: Chunk size can be adjusted based on network conditions
3. **Compression**: Files can be compressed before encryption to reduce size

## Conclusion

This media handling specification provides a comprehensive framework for secure, reliable, and efficient media sharing in the Meena application. By implementing E2E encryption, chunked uploads, and background processing, Meena ensures that media sharing is secure, works with large files, and provides a good user experience even with unstable network connections.
