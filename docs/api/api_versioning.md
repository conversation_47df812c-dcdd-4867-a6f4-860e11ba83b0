# API Versioning Strategy

This document outlines the strategy for versioning both REST and WebSocket APIs in the Meena application to ensure backward compatibility and smooth evolution of the API.

## Principles

1. **Backward Compatibility**: New API versions should maintain backward compatibility when possible
2. **Clear Communication**: Version changes and deprecation schedules must be clearly communicated
3. **Gradual Transition**: Allow clients to migrate to new versions at their own pace
4. **Minimal Disruption**: Minimize disruption to existing clients when introducing changes
5. **Consistent Approach**: Apply consistent versioning across REST and WebSocket APIs

## Versioning Scheme

Meena uses a two-part versioning scheme:

```
v{MAJOR}.{MINOR}
```

- **MAJOR**: Incremented for backward-incompatible changes
- **MINOR**: Incremented for backward-compatible feature additions

Examples: `v1.0`, `v1.1`, `v2.0`

## REST API Versioning

### URL Path Versioning

The primary versioning mechanism for REST APIs is through the URL path:

```
https://api.meena.com/v1/users
https://api.meena.com/v2/users
```

This approach provides:
- Clear visibility of the API version
- Easy routing to different API implementations
- Ability for clients to use multiple API versions simultaneously

### Version Selection

1. **Default Version**:
   - If no version is specified, the latest stable version is used
   - Example: `/users` redirects to `/v1/users`

2. **Explicit Version**:
   - Clients should explicitly specify the version in production environments
   - Example: `/v1/users`

### HTTP Headers

In addition to URL path versioning, the following headers are used:

1. **Response Headers**:
   ```
   API-Version: 1.2
   API-Deprecated: false
   API-Deprecation-Date: 2024-06-30
   API-Sunset-Date: 2025-06-30
   ```

2. **Request Headers** (optional):
   ```
   Accept-Version: 1.2
   ```

## WebSocket API Versioning

### Connection Versioning

WebSocket connections include the version in the connection URL:

```
wss://api.meena.com/v1/ws
wss://api.meena.com/v2/ws
```

### Protocol Versioning

In addition to connection versioning, the WebSocket protocol itself is versioned:

1. **Version Negotiation**:
   - Client specifies desired protocol version in connection URL
   - Example: `wss://api.meena.com/v1/ws?protocol_version=1.2`

2. **Welcome Message**:
   - Server confirms protocol version in welcome message
   - Example:
     ```json
     {
       "type": "welcome",
       "payload": {
         "protocol_version": "1.2",
         "supported_versions": ["1.0", "1.1", "1.2"]
       }
     }
     ```

3. **Fallback Mechanism**:
   - If requested version is not supported, server suggests alternative
   - Example:
     ```json
     {
       "type": "error",
       "payload": {
         "code": 4006,
         "message": "Protocol version 1.3 not supported",
         "suggested_version": "1.2"
       }
     }
     ```

## Version Lifecycle

Each API version goes through the following lifecycle stages:

```mermaid
graph LR
    A[Beta] --> B[Stable]
    B --> C[Deprecated]
    C --> D[Sunset]
    D --> E[Removed]
```

### 1. Beta

- New features are introduced in beta versions
- Beta versions may change without notice
- Beta endpoints are clearly marked in documentation
- Example URL: `/v2-beta/users`

### 2. Stable

- API is fully supported and recommended for production use
- Changes are backward compatible within the same major version
- New features may be added in minor version increments

### 3. Deprecated

- API is still available but no longer recommended
- Deprecation is announced at least 6 months before sunset
- Deprecation notices are included in API responses
- Documentation clearly marks deprecated endpoints

### 4. Sunset

- API is scheduled for removal
- Sunset period lasts at least 3 months
- Requests return warning headers with removal date
- Temporary redirects (HTTP 307) may be used to guide clients to new endpoints

### 5. Removed

- API is no longer available
- Requests return HTTP 410 Gone with information about the replacement API

## Backward Compatibility Guidelines

### Compatible Changes (No Version Increment Required)

- Adding new API endpoints
- Adding optional request parameters
- Adding response fields (clients should ignore unknown fields)
- Adding new enum values (clients should handle unknown values gracefully)
- Adding new WebSocket message types (clients should ignore unknown types)

### Non-Compatible Changes (Major Version Increment Required)

- Removing or renaming API endpoints
- Removing or renaming required request parameters
- Removing or renaming response fields
- Changing field types or formats
- Changing error codes or response structure
- Changing authentication mechanisms

## Documentation Requirements

For each API version, documentation must include:

1. **Version Information**:
   - Current lifecycle stage (beta, stable, deprecated, sunset)
   - Release date
   - Deprecation date (if applicable)
   - Sunset date (if applicable)

2. **Changelog**:
   - List of changes from previous versions
   - Migration guide for non-compatible changes

3. **Compatibility Notes**:
   - Known compatibility issues
   - Recommended client versions

## Implementation Strategy

### REST API Implementation

1. **API Gateway Routing**:
   - Route requests based on version prefix
   - Example: `/v1/*` routes to v1 API implementation

2. **Controller Versioning**:
   - Use separate controllers for different major versions
   - Share common logic through services
   ```go
   // v1 controller
   func GetUserV1(c *gin.Context) {
       user := services.GetUser(c.Param("id"))
       c.JSON(200, user.ToV1Response())
   }
   
   // v2 controller
   func GetUserV2(c *gin.Context) {
       user := services.GetUser(c.Param("id"))
       c.JSON(200, user.ToV2Response())
   }
   ```

3. **Response Transformation**:
   - Transform internal models to version-specific responses
   - Use separate response structs for each version
   ```go
   // V1 response
   type UserResponseV1 struct {
       ID        string `json:"id"`
       Username  string `json:"username"`
       Email     string `json:"email"`
   }
   
   // V2 response
   type UserResponseV2 struct {
       ID        string `json:"id"`
       Handle    string `json:"handle"`  // renamed from username
       Email     string `json:"email"`
       CreatedAt string `json:"created_at"` // new field
   }
   ```

### WebSocket Implementation

1. **Connection Handlers**:
   - Separate connection handlers for each major version
   - Example: `/v1/ws` connects to v1 WebSocket handler

2. **Protocol Negotiation**:
   - Implement protocol version negotiation in welcome message
   - Support multiple protocol versions within same connection handler

3. **Message Transformation**:
   - Transform internal messages to version-specific formats
   - Handle version-specific message processing

## Monitoring and Analytics

To track API version usage and plan deprecation:

1. **Usage Metrics**:
   - Track requests per API version
   - Monitor client adoption of new versions
   - Identify clients using deprecated versions

2. **Error Tracking**:
   - Monitor version-related errors
   - Track compatibility issues

3. **Reporting**:
   - Generate monthly reports on API version usage
   - Use data to inform deprecation decisions

## Communication Plan

When introducing new API versions or deprecating existing ones:

1. **Advance Notice**:
   - Announce new versions at least 1 month before release
   - Announce deprecation at least 6 months before sunset
   - Announce sunset at least 3 months before removal

2. **Communication Channels**:
   - Developer newsletter
   - API dashboard notifications
   - API response headers
   - Documentation updates

3. **Migration Support**:
   - Provide migration guides
   - Offer developer support for migration
   - Consider providing migration tools for major changes

## Example Version Timeline

```mermaid
gantt
    title API Version Timeline
    dateFormat  YYYY-MM-DD
    section v1.0
    Stable           :2023-01-01, 2023-12-31
    Deprecated       :2024-01-01, 2024-12-31
    Sunset           :2025-01-01, 2025-03-31
    section v1.1
    Beta             :2023-06-01, 2023-07-31
    Stable           :2023-08-01, 2024-12-31
    Deprecated       :2025-01-01, 2025-12-31
    section v2.0
    Beta             :2023-10-01, 2023-12-31
    Stable           :2024-01-01, 2026-12-31
```

## Case Study: API v1 to v2 Migration

### Scenario

The v1 API represents users with a `username` field, while v2 uses `handle` for the same concept.

### V1 API Response

```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "username": "johndoe",
  "email": "<EMAIL>"
}
```

### V2 API Response

```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "handle": "johndoe",
  "email": "<EMAIL>",
  "created_at": "2023-01-15T12:00:00Z"
}
```

### Migration Approach

1. **Transition Period**:
   - v1 continues to return `username`
   - v2 returns both `username` and `handle` initially
   - After 6 months, v2 deprecates `username`

2. **Documentation**:
   - Clear migration guide explaining the change
   - Examples of both v1 and v2 responses
   - Timeline for deprecation

3. **Client Support**:
   - Update client libraries to support both fields
   - Add warnings when clients use deprecated fields

## Conclusion

This versioning strategy ensures that the Meena API can evolve while maintaining backward compatibility and minimizing disruption to clients. By following these guidelines, we can introduce new features and improvements while giving clients sufficient time to adapt to changes.

The strategy applies consistently to both REST and WebSocket APIs, providing a unified approach to API versioning across the platform.
