# End-to-End Encryption (E2EE) Specification

This document defines the end-to-end encryption protocol used by the Me<PERSON> application to secure one-to-one conversations.

## Overview

Meena implements end-to-end encryption for one-to-one conversations to ensure that only the sender and recipient can read the messages. This means that even <PERSON><PERSON>'s servers cannot decrypt the message content, providing strong privacy and security for users.

The E2EE implementation is based on the Signal Protocol, which provides:

- **Forward secrecy**: If a private key is compromised, it cannot be used to decrypt past messages
- **Future secrecy**: If a private key is compromised, it cannot be used to decrypt future messages
- **Deniability**: Messages cannot be cryptographically proven to have come from a specific sender
- **Asynchronous messaging**: Recipients don't need to be online to receive encrypted messages

## Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Client A   │     │    Server   │     │  Client B   │
│  (Sender)   │     │  (Relay)    │     │ (Recipient) │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   │
       │ Generate Keys     │                   │ Generate Keys
       │                   │                   │
       │ Register Public Keys                  │ Register Public Keys
       │─────────────────>│<──────────────────│
       │                   │                   │
       │ Fetch B's Keys    │                   │
       │─────────────────>│                   │
       │                   │                   │
       │ Encrypt Message   │                   │
       │                   │                   │
       │ Send Encrypted Message               │
       │─────────────────>│─────────────────>│
       │                   │                   │ Decrypt Message
```

## Key Components

### 1. Identity Keys

Each user has a long-term identity key pair:
- **Identity Private Key**: Stored only on the user's devices
- **Identity Public Key**: Registered with the server and shared with contacts

### 2. Prekey Bundles

To enable asynchronous messaging, each client generates and registers prekey bundles with the server:
- **Signed Prekey**: A medium-term key signed by the identity key
- **One-time Prekeys**: Multiple single-use keys for initial message encryption

### 3. Session Keys

For each conversation, a session is established with unique encryption keys:
- **Root Key**: The master key for deriving chain keys
- **Chain Keys**: Keys for encrypting messages in a chain
- **Message Keys**: One-time keys derived from chain keys for encrypting individual messages

## Protocol Flow

### 1. Key Generation and Registration

When a user installs the app or adds a new device:

1. **Generate Identity Key Pair**:
   - Generate a Curve25519 key pair for identity
   - Store private key securely on the device
   - Register public key with the server

2. **Generate and Register Prekeys**:
   - Generate a signed prekey (rotated periodically)
   - Generate multiple one-time prekeys (100+)
   - Sign the prekeys with the identity key
   - Register prekeys with the server

```json
// Example prekey bundle registration request
POST /api/v1/keys/register
{
  "identity_key": "BASE64_ENCODED_PUBLIC_KEY",
  "signed_prekey": {
    "key_id": 1,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY",
    "signature": "BASE64_ENCODED_SIGNATURE"
  },
  "one_time_prekeys": [
    {
      "key_id": 101,
      "public_key": "BASE64_ENCODED_PUBLIC_KEY"
    },
    // More one-time prekeys...
  ]
}
```

### 2. Session Establishment

When User A wants to send a message to User B for the first time:

1. **Fetch Prekey Bundle**:
   - User A requests User B's prekey bundle from the server
   - Server returns User B's identity key, signed prekey, and one one-time prekey
   - Server marks the one-time prekey as used

```json
// Example prekey bundle request
GET /api/v1/keys/bundle/{user_id}

// Example response
{
  "identity_key": "BASE64_ENCODED_PUBLIC_KEY",
  "signed_prekey": {
    "key_id": 1,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY",
    "signature": "BASE64_ENCODED_SIGNATURE"
  },
  "one_time_prekey": {
    "key_id": 101,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY"
  }
}
```

2. **Verify Signed Prekey**:
   - User A verifies the signature on User B's signed prekey using User B's identity key

3. **Establish Initial Session**:
   - User A uses the X3DH (Extended Triple Diffie-Hellman) key agreement protocol to establish a shared secret
   - The shared secret is used to initialize the Double Ratchet Algorithm

### 3. Message Encryption and Decryption

For each message in the conversation:

1. **Encrypt Message**:
   - Generate a message key using the Double Ratchet Algorithm
   - Encrypt the message content with the message key using AES-256-GCM
   - Include necessary key information in the message header

2. **Send Encrypted Message**:
   - Send the encrypted message to the server
   - Server stores and forwards the message without being able to decrypt it

3. **Decrypt Message**:
   - Recipient receives the encrypted message
   - Extract key information from the message header
   - Use the Double Ratchet Algorithm to derive the correct message key
   - Decrypt the message content

```json
// Example encrypted message structure
{
  "type": "encrypted_message",
  "sender_id": "123e4567-e89b-12d3-a456-************",
  "recipient_id": "456e7890-e89b-12d3-a456-************",
  "message": {
    "header": {
      "sender_ratchet_key": "BASE64_ENCODED_PUBLIC_KEY",
      "previous_counter": 42,
      "counter": 43
    },
    "cipher_text": "BASE64_ENCODED_ENCRYPTED_CONTENT",
    "iv": "BASE64_ENCODED_INITIALIZATION_VECTOR",
    "auth_tag": "BASE64_ENCODED_AUTHENTICATION_TAG"
  }
}
```

### 4. Key Rotation

To maintain forward secrecy:

1. **Ratchet Updates**:
   - The Double Ratchet Algorithm updates keys with each message
   - New ratchet keys are generated periodically

2. **Signed Prekey Rotation**:
   - Clients rotate their signed prekey periodically (e.g., weekly)
   - Old signed prekeys are kept for a period to allow delayed message decryption

## API Endpoints

### Key Management

#### Register Keys

```
POST /api/v1/keys/register
```

Register identity key and prekeys with the server.

**Request:**
```json
{
  "identity_key": "BASE64_ENCODED_PUBLIC_KEY",
  "signed_prekey": {
    "key_id": 1,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY",
    "signature": "BASE64_ENCODED_SIGNATURE"
  },
  "one_time_prekeys": [
    {
      "key_id": 101,
      "public_key": "BASE64_ENCODED_PUBLIC_KEY"
    },
    // More one-time prekeys...
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "registered_keys": {
    "identity_key": true,
    "signed_prekey": true,
    "one_time_prekeys": 100
  }
}
```

#### Get Prekey Bundle

```
GET /api/v1/keys/bundle/{user_id}
```

Retrieve a user's prekey bundle for establishing a session.

**Response:**
```json
{
  "identity_key": "BASE64_ENCODED_PUBLIC_KEY",
  "signed_prekey": {
    "key_id": 1,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY",
    "signature": "BASE64_ENCODED_SIGNATURE"
  },
  "one_time_prekey": {
    "key_id": 101,
    "public_key": "BASE64_ENCODED_PUBLIC_KEY"
  }
}
```

#### Get Prekey Count

```
GET /api/v1/keys/count
```

Check how many one-time prekeys are available on the server.

**Response:**
```json
{
  "count": 42
}
```

### Encrypted Messaging

#### Send Encrypted Message

```
POST /api/v1/messages/encrypted
```

Send an encrypted message to another user.

**Request:**
```json
{
  "recipient_id": "456e7890-e89b-12d3-a456-************",
  "message": {
    "header": {
      "sender_ratchet_key": "BASE64_ENCODED_PUBLIC_KEY",
      "previous_counter": 42,
      "counter": 43
    },
    "cipher_text": "BASE64_ENCODED_ENCRYPTED_CONTENT",
    "iv": "BASE64_ENCODED_INITIALIZATION_VECTOR",
    "auth_tag": "BASE64_ENCODED_AUTHENTICATION_TAG"
  }
}
```

**Response:**
```json
{
  "message_id": "msg-123456",
  "timestamp": 1626962475,
  "status": "sent"
}
```

## Client Implementation Guidelines

### Key Storage

Keys must be stored securely on the client device:

1. **Identity Private Key**:
   - Store in secure storage (Keychain on iOS, KeyStore on Android)
   - Protect with biometric authentication if available
   - Never transmit to the server or other devices

2. **Session Keys**:
   - Store in encrypted database
   - Encrypt database with a key derived from the user's password
   - Implement secure key derivation (e.g., PBKDF2 with high iteration count)

### Key Verification

To prevent man-in-the-middle attacks:

1. **Safety Numbers**:
   - Generate a fingerprint from both users' identity keys
   - Display as a numeric code or QR code
   - Allow users to compare safety numbers out-of-band

2. **Trust Decisions**:
   - Allow users to mark contacts as verified after checking safety numbers
   - Warn users when a contact's identity key changes

### Multi-Device Support

To support multiple devices per user:

1. **Separate Keys Per Device**:
   - Each device generates its own identity and prekeys
   - Server maintains separate key bundles for each device

2. **Fan-out Encryption**:
   - When sending to a user with multiple devices, encrypt separately for each device
   - Server delivers the appropriate encrypted message to each device

```json
// Example multi-device encrypted message
{
  "recipient_id": "456e7890-e89b-12d3-a456-************",
  "device_messages": [
    {
      "device_id": "device-1",
      "message": {
        "header": { /* ... */ },
        "cipher_text": "BASE64_ENCODED_ENCRYPTED_CONTENT_FOR_DEVICE_1",
        "iv": "BASE64_ENCODED_IV_1",
        "auth_tag": "BASE64_ENCODED_AUTH_TAG_1"
      }
    },
    {
      "device_id": "device-2",
      "message": {
        "header": { /* ... */ },
        "cipher_text": "BASE64_ENCODED_ENCRYPTED_CONTENT_FOR_DEVICE_2",
        "iv": "BASE64_ENCODED_IV_2",
        "auth_tag": "BASE64_ENCODED_AUTH_TAG_2"
      }
    }
  ]
}
```

## Security Considerations

### Key Verification

To mitigate man-in-the-middle attacks:

1. **Out-of-Band Verification**:
   - Users should verify each other's identity keys through a separate channel
   - Implement safety numbers or QR codes for easy verification

2. **Key Change Notifications**:
   - Notify users when a contact's identity key changes
   - Require explicit trust decision before continuing communication

### Forward Secrecy

To ensure forward secrecy:

1. **Key Ratcheting**:
   - Implement the Double Ratchet Algorithm correctly
   - Ensure keys are updated with each message

2. **Key Deletion**:
   - Delete message keys after use
   - Implement secure deletion (overwrite memory before freeing)

### Metadata Protection

While E2EE protects message content, metadata requires additional protection:

1. **Minimize Metadata**:
   - Store minimal metadata on the server
   - Use generic message sizes to prevent content inference

2. **Secure Connections**:
   - Use TLS for all API connections
   - Implement certificate pinning to prevent MITM attacks

### Implementation Verification

To ensure correct implementation:

1. **Cryptographic Library**:
   - Use well-audited cryptographic libraries
   - Avoid implementing cryptographic primitives from scratch

2. **Security Audits**:
   - Conduct regular security audits of the E2EE implementation
   - Fix vulnerabilities promptly

## Limitations

End-to-end encryption in Meena has the following limitations:

1. **Group Conversations**:
   - E2EE is only implemented for one-to-one conversations in the initial version
   - Group conversations will use server-side encryption initially

2. **Backup Limitations**:
   - E2EE messages cannot be included in standard backups
   - Implement secure backup solutions for E2EE messages

3. **Search Limitations**:
   - Server-side search cannot include E2EE message content
   - Client-side search must be implemented for E2EE messages

## Key Backup and Recovery

To address the challenge of key management across multiple devices and during device changes, Meena implements a secure key backup system:

### Key Backup Options

1. **Password-Protected Cloud Backup**:
   - Keys are encrypted with a key derived from the user's password
   - Encrypted keys are stored on Meena servers
   - Neither the password nor the decryption key is ever sent to the server

   ```javascript
   // Key backup process
   async function backupKeys(keys, password) {
     // Generate a strong salt
     const salt = crypto.getRandomValues(new Uint8Array(16));

     // Derive encryption key from password using PBKDF2
     const encryptionKey = await deriveKeyFromPassword(password, salt, 100000);

     // Encrypt keys
     const iv = crypto.getRandomValues(new Uint8Array(12));
     const encryptedKeys = await crypto.subtle.encrypt(
       { name: 'AES-GCM', iv },
       encryptionKey,
       new TextEncoder().encode(JSON.stringify(keys))
     );

     // Prepare backup data
     const backupData = {
       version: 1,
       salt: arrayBufferToBase64(salt),
       iv: arrayBufferToBase64(iv),
       encryptedKeys: arrayBufferToBase64(encryptedKeys)
     };

     // Upload to server
     await api.post('/api/v1/keys/backup', backupData);
   }
   ```

2. **Recovery Phrase**:
   - User is provided with a 12-24 word mnemonic recovery phrase
   - Recovery phrase can be used to regenerate all encryption keys
   - User must store the recovery phrase securely offline

   ```javascript
   // Generate recovery phrase
   function generateRecoveryPhrase() {
     // Generate 128-256 bits of entropy
     const entropy = crypto.getRandomValues(new Uint8Array(32));

     // Convert to BIP-39 mnemonic
     const mnemonic = bip39.entropyToMnemonic(entropy);

     // Display to user for secure storage
     return mnemonic.split(' ');
   }

   // Recover keys from phrase
   async function recoverKeysFromPhrase(phrase) {
     // Convert mnemonic to seed
     const seed = await bip39.mnemonicToSeed(phrase.join(' '));

     // Derive master key from seed
     const masterKey = await crypto.subtle.importKey(
       'raw',
       seed.slice(0, 32),
       { name: 'HKDF' },
       false,
       ['deriveKey', 'deriveBits']
     );

     // Derive identity key and other keys
     const identityKey = await deriveIdentityKey(masterKey);
     const signedPreKey = await deriveSignedPreKey(masterKey);
     const oneTimePreKeys = await deriveOneTimePreKeys(masterKey, 100);

     return {
       identityKey,
       signedPreKey,
       oneTimePreKeys
     };
   }
   ```

### Performance Considerations

E2EE operations can be computationally intensive, especially on mobile devices. The implementation includes several optimizations:

1. **Key Generation Optimization**:
   - Generate keys in background threads/workers
   - Pre-generate one-time keys during idle periods
   - Cache derived keys for frequently used operations

2. **Message Processing Optimization**:
   - Use WebCrypto API for hardware-accelerated cryptography
   - Implement batch processing for multiple messages
   - Prioritize decryption of visible messages

3. **Battery Usage Optimization**:
   - Defer non-critical cryptographic operations when battery is low
   - Adjust key rotation frequency based on device capabilities
   - Use efficient serialization formats to reduce processing overhead

```javascript
// Example of optimized batch decryption
async function decryptMessages(encryptedMessages, sessionCipher) {
  // Create a worker for background processing
  const worker = new Worker('crypto-worker.js');

  // Process messages in batches
  const batchSize = 10;
  const results = [];

  for (let i = 0; i < encryptedMessages.length; i += batchSize) {
    const batch = encryptedMessages.slice(i, i + batchSize);

    // Process batch in worker
    const decryptedBatch = await new Promise((resolve, reject) => {
      worker.onmessage = (e) => resolve(e.data);
      worker.onerror = (e) => reject(e);
      worker.postMessage({
        action: 'decryptBatch',
        messages: batch,
        session: sessionCipher.exportSession()
      });
    });

    results.push(...decryptedBatch);

    // Yield to main thread periodically
    if (i % 50 === 0 && i > 0) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  worker.terminate();
  return results;
}
```

## Future Enhancements

Planned enhancements to the E2EE implementation:

1. **Group E2EE**:
   - Implement E2EE for group conversations using the Signal group protocol

2. **Secure Backup**:
   - Implement encrypted backups for E2EE messages
   - Allow recovery across devices with user-controlled keys

3. **Perfect Forward Secrecy**:
   - Enhance the protocol to provide perfect forward secrecy
   - Implement more frequent key rotation

## Conclusion

This end-to-end encryption specification provides a comprehensive framework for securing one-to-one conversations in the Meena application. By implementing this protocol, Meena ensures that user messages remain private and secure, even from the service provider.

The protocol is based on well-established cryptographic principles and the Signal Protocol, which has been extensively reviewed and is widely trusted. By following this specification, developers can implement a secure E2EE system that protects user privacy while maintaining usability.
