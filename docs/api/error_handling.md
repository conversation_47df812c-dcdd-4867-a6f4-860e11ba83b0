# Error Handling Standards

This document defines the standardized approach to error handling across the Meena application, covering both REST APIs and WebSocket communication.

## Principles

1. **Consistency**: Errors should be formatted consistently across all APIs
2. **Clarity**: Error messages should be clear and actionable
3. **Security**: Error details should not expose sensitive information
4. **Localization**: Error messages should support multiple languages
5. **Traceability**: Errors should be traceable for debugging

## REST API Error Format

### Error Response Structure

All API errors use a consistent JSON structure:

```json
{
  "error": {
    "code": "resource_not_found",
    "message": "The requested resource was not found",
    "details": [
      {
        "field": "user_id",
        "issue": "not_found",
        "message": "User with ID '123' does not exist"
      }
    ],
    "trace_id": "abcd1234-ef56-7890-abcd-1234ef567890",
    "documentation_url": "https://docs.meena.com/errors/resource_not_found"
  }
}
```

### Fields Explanation

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `code` | string | Yes | Machine-readable error code |
| `message` | string | Yes | Human-readable error message |
| `details` | array | No | Additional error details |
| `details[].field` | string | No | Field that caused the error |
| `details[].issue` | string | No | Specific issue with the field |
| `details[].message` | string | No | Human-readable explanation |
| `trace_id` | string | Yes | Unique identifier for the error instance |
| `documentation_url` | string | No | URL to error documentation |

### HTTP Status Codes

Use appropriate HTTP status codes to indicate the nature of the error:

| Status Code | Category | Usage |
|-------------|----------|-------|
| 400 | Bad Request | Client error (invalid input, validation error) |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Authentication succeeded but insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Request conflicts with current state |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error (unexpected condition) |
| 503 | Service Unavailable | Service temporarily unavailable |

### Error Codes

Error codes should be:
- Lowercase with underscores
- Descriptive and specific
- Grouped by domain

#### Authentication Errors

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `auth_required` | Authentication is required | 401 |
| `invalid_credentials` | Invalid username or password | 401 |
| `invalid_token` | Invalid or expired token | 401 |
| `account_locked` | Account is locked | 403 |
| `insufficient_permissions` | Insufficient permissions | 403 |

#### Resource Errors

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `resource_not_found` | Resource not found | 404 |
| `resource_already_exists` | Resource already exists | 409 |
| `resource_gone` | Resource no longer available | 410 |

#### Validation Errors

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `validation_failed` | Validation failed | 422 |
| `invalid_parameter` | Invalid parameter | 400 |
| `missing_parameter` | Required parameter missing | 400 |
| `invalid_format` | Invalid format | 400 |

#### Rate Limiting Errors

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `rate_limit_exceeded` | Rate limit exceeded | 429 |
| `quota_exceeded` | Quota exceeded | 429 |

#### Server Errors

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `internal_error` | Internal server error | 500 |
| `service_unavailable` | Service temporarily unavailable | 503 |
| `database_error` | Database error | 500 |

## WebSocket Error Format

### Error Message Structure

WebSocket errors use a similar structure to REST API errors:

```json
{
  "type": "error",
  "id": "msg123",
  "payload": {
    "code": "invalid_message_format",
    "message": "The message format is invalid",
    "details": [
      {
        "field": "content",
        "issue": "required",
        "message": "Content is required"
      }
    ],
    "trace_id": "abcd1234-ef56-7890-abcd-1234ef567890",
    "request_id": "req456"
  },
  "timestamp": 1626962475
}
```

### WebSocket Error Codes

| Code | Description |
|------|-------------|
| `connection_error` | Connection error |
| `authentication_failed` | Authentication failed |
| `invalid_message_format` | Invalid message format |
| `invalid_message_type` | Invalid message type |
| `subscription_failed` | Subscription failed |
| `rate_limit_exceeded` | Rate limit exceeded |
| `permission_denied` | Permission denied |
| `internal_error` | Internal server error |

## Error Handling Implementation

### REST API Implementation

#### Go (Gin Framework) Example

```go
// Error response structure
type ErrorResponse struct {
    Error struct {
        Code            string        `json:"code"`
        Message         string        `json:"message"`
        Details         []ErrorDetail `json:"details,omitempty"`
        TraceID         string        `json:"trace_id"`
        DocumentationURL string       `json:"documentation_url,omitempty"`
    } `json:"error"`
}

type ErrorDetail struct {
    Field   string `json:"field,omitempty"`
    Issue   string `json:"issue,omitempty"`
    Message string `json:"message"`
}

// Error handling middleware
func ErrorHandlerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Generate trace ID
        traceID := uuid.New().String()
        c.Set("trace_id", traceID)
        
        // Process request
        c.Next()
        
        // Check if there was an error
        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err
            
            // Default to internal server error
            statusCode := http.StatusInternalServerError
            errorCode := "internal_error"
            errorMessage := "An unexpected error occurred"
            
            // Check for custom error types
            if appErr, ok := err.(*AppError); ok {
                statusCode = appErr.StatusCode
                errorCode = appErr.Code
                errorMessage = appErr.Message
                
                // Log error with trace ID
                log.Printf("[%s] %s: %s", traceID, errorCode, errorMessage)
                
                // Create error response
                response := ErrorResponse{}
                response.Error.Code = errorCode
                response.Error.Message = errorMessage
                response.Error.TraceID = traceID
                
                if appErr.Details != nil {
                    response.Error.Details = appErr.Details
                }
                
                if errorCode != "internal_error" {
                    response.Error.DocumentationURL = fmt.Sprintf("https://docs.meena.com/errors/%s", errorCode)
                }
                
                c.JSON(statusCode, response)
                c.Abort()
                return
            }
            
            // Handle unexpected errors
            log.Printf("[%s] Unexpected error: %v", traceID, err)
            
            response := ErrorResponse{}
            response.Error.Code = errorCode
            response.Error.Message = errorMessage
            response.Error.TraceID = traceID
            
            c.JSON(statusCode, response)
            c.Abort()
        }
    }
}

// Custom error type
type AppError struct {
    StatusCode int
    Code       string
    Message    string
    Details    []ErrorDetail
}

func (e *AppError) Error() string {
    return e.Message
}

// Error creation helpers
func NewNotFoundError(resource string, id string) *AppError {
    return &AppError{
        StatusCode: http.StatusNotFound,
        Code:       "resource_not_found",
        Message:    fmt.Sprintf("The requested %s was not found", resource),
        Details: []ErrorDetail{
            {
                Field:   resource + "_id",
                Issue:   "not_found",
                Message: fmt.Sprintf("%s with ID '%s' does not exist", resource, id),
            },
        },
    }
}

func NewValidationError(details []ErrorDetail) *AppError {
    return &AppError{
        StatusCode: http.StatusUnprocessableEntity,
        Code:       "validation_failed",
        Message:    "Validation failed",
        Details:    details,
    }
}

// Usage example
func GetUser(c *gin.Context) {
    userID := c.Param("id")
    
    user, err := userService.GetUser(userID)
    if err != nil {
        if err == sql.ErrNoRows {
            c.Error(NewNotFoundError("user", userID))
            return
        }
        c.Error(err)
        return
    }
    
    c.JSON(http.StatusOK, user)
}
```

### WebSocket Implementation

```go
// WebSocket error handling
func handleWebSocketError(conn *websocket.Conn, code string, message string, requestID string, details []ErrorDetail) {
    traceID := uuid.New().String()
    
    // Log error
    log.Printf("[%s] WebSocket error: %s - %s", traceID, code, message)
    
    // Create error message
    errorMsg := map[string]interface{}{
        "type": "error",
        "payload": map[string]interface{}{
            "code":     code,
            "message":  message,
            "trace_id": traceID,
        },
        "timestamp": time.Now().Unix(),
    }
    
    if requestID != "" {
        errorMsg["id"] = requestID
        errorMsg["payload"].(map[string]interface{})["request_id"] = requestID
    }
    
    if len(details) > 0 {
        errorMsg["payload"].(map[string]interface{})["details"] = details
    }
    
    // Send error message
    conn.WriteJSON(errorMsg)
}

// Usage example
func handleMessage(conn *websocket.Conn, msg map[string]interface{}) {
    // Validate message
    if msg["type"] == nil {
        handleWebSocketError(conn, "invalid_message_format", "Message type is required", "", nil)
        return
    }
    
    // Process message based on type
    switch msg["type"].(string) {
    case "message":
        handleChatMessage(conn, msg)
    default:
        handleWebSocketError(conn, "invalid_message_type", "Unsupported message type", "", nil)
    }
}
```

## Client-Side Error Handling

### REST API Client

```typescript
// TypeScript client error handling
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: Array<{
      field?: string;
      issue?: string;
      message: string;
    }>;
    trace_id: string;
    documentation_url?: string;
  };
}

class ApiClient {
  async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorData: ErrorResponse = await response.json();
        throw new ApiError(
          errorData.error.code,
          errorData.error.message,
          response.status,
          errorData.error.details,
          errorData.error.trace_id
        );
      }
      
      return await response.json() as T;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors
      throw new ApiError(
        'network_error',
        'Network error occurred',
        0,
        [],
        ''
      );
    }
  }
}

class ApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public status: number,
    public details: Array<{ field?: string; issue?: string; message: string }> = [],
    public traceId: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
  
  getFieldError(fieldName: string): string | undefined {
    const fieldError = this.details.find(detail => detail.field === fieldName);
    return fieldError?.message;
  }
  
  isValidationError(): boolean {
    return this.code === 'validation_failed';
  }
}

// Usage example
async function getUser(userId: string) {
  try {
    const user = await apiClient.request<User>(`/api/v1/users/${userId}`);
    return user;
  } catch (error) {
    if (error instanceof ApiError) {
      if (error.code === 'resource_not_found') {
        console.log(`User ${userId} not found`);
        return null;
      }
      
      // Handle other specific errors
      console.error(`API Error: ${error.code} - ${error.message}`);
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}
```

### WebSocket Client

```typescript
// TypeScript WebSocket client error handling
interface WebSocketErrorMessage {
  type: 'error';
  id?: string;
  payload: {
    code: string;
    message: string;
    details?: Array<{
      field?: string;
      issue?: string;
      message: string;
    }>;
    trace_id: string;
    request_id?: string;
  };
  timestamp: number;
}

class WebSocketClient {
  private socket: WebSocket;
  private messageHandlers: Map<string, (message: any) => void> = new Map();
  private errorHandler: (error: WebSocketError) => void;
  
  constructor(url: string, errorHandler: (error: WebSocketError) => void) {
    this.socket = new WebSocket(url);
    this.errorHandler = errorHandler;
    
    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      
      if (message.type === 'error') {
        const error = new WebSocketError(
          message.payload.code,
          message.payload.message,
          message.payload.details || [],
          message.payload.trace_id,
          message.payload.request_id
        );
        
        // Check if there's a specific handler for this message ID
        if (message.id && this.messageHandlers.has(message.id)) {
          const handler = this.messageHandlers.get(message.id)!;
          this.messageHandlers.delete(message.id);
          handler(error);
        } else {
          // Use global error handler
          this.errorHandler(error);
        }
        
        return;
      }
      
      // Handle normal messages
      if (message.id && this.messageHandlers.has(message.id)) {
        const handler = this.messageHandlers.get(message.id)!;
        this.messageHandlers.delete(message.id);
        handler(message);
      }
    };
  }
  
  sendMessage(type: string, payload: any, timeout: number = 10000): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const message = {
        type,
        id,
        payload,
        timestamp: Math.floor(Date.now() / 1000)
      };
      
      // Set timeout
      const timeoutId = setTimeout(() => {
        if (this.messageHandlers.has(id)) {
          this.messageHandlers.delete(id);
          reject(new WebSocketError(
            'timeout',
            'Request timed out',
            [],
            '',
            id
          ));
        }
      }, timeout);
      
      // Set response handler
      this.messageHandlers.set(id, (response) => {
        clearTimeout(timeoutId);
        
        if (response instanceof WebSocketError) {
          reject(response);
        } else {
          resolve(response);
        }
      });
      
      // Send message
      this.socket.send(JSON.stringify(message));
    });
  }
}

class WebSocketError extends Error {
  constructor(
    public code: string,
    message: string,
    public details: Array<{ field?: string; issue?: string; message: string }> = [],
    public traceId: string,
    public requestId?: string
  ) {
    super(message);
    this.name = 'WebSocketError';
  }
}

// Usage example
const wsClient = new WebSocketClient('wss://api.meena.com/v1/ws', (error) => {
  console.error(`WebSocket Error: ${error.code} - ${error.message}`);
});

async function sendChatMessage(chatId: string, content: string) {
  try {
    const response = await wsClient.sendMessage('message', {
      chat_id: chatId,
      content: content
    });
    
    return response;
  } catch (error) {
    if (error instanceof WebSocketError) {
      if (error.code === 'permission_denied') {
        console.log(`No permission to send message to chat ${chatId}`);
        return null;
      }
      
      // Handle other specific errors
      console.error(`WebSocket Error: ${error.code} - ${error.message}`);
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}
```

## Error Logging and Monitoring

### Logging Strategy

1. **Structured Logging**:
   - Log errors in structured format (JSON)
   - Include trace ID, error code, and message
   - Include request context (URL, method, user ID)

2. **Log Levels**:
   - ERROR: For all API errors (4xx, 5xx)
   - WARN: For potential issues (deprecated API usage)
   - INFO: For normal operations

3. **Sensitive Data**:
   - Redact sensitive data in logs (passwords, tokens)
   - Mask PII (email, phone) when appropriate

### Example Log Format

```json
{
  "timestamp": "2023-07-15T12:34:56.789Z",
  "level": "ERROR",
  "trace_id": "abcd1234-ef56-7890-abcd-1234ef567890",
  "user_id": "user123",
  "request": {
    "method": "GET",
    "path": "/api/v1/users/456",
    "ip": "***********"
  },
  "error": {
    "code": "resource_not_found",
    "message": "The requested user was not found",
    "status": 404
  }
}
```

### Monitoring and Alerting

1. **Error Rate Monitoring**:
   - Track error rates by endpoint and error code
   - Set alerts for unusual error rates
   - Monitor 4xx vs 5xx error ratios

2. **Error Dashboards**:
   - Create dashboards for error trends
   - Group errors by code, endpoint, and client

3. **Alert Thresholds**:
   - Critical: >1% 5xx errors
   - Warning: >5% 4xx errors
   - Critical: Any authentication bypass attempts

## Error Documentation

### Error Catalog

Maintain a comprehensive error catalog in the API documentation:

1. **For Each Error Code**:
   - Description and common causes
   - How to resolve the error
   - Example error response

2. **Documentation URL**:
   - Each error response includes a documentation URL
   - URL points to specific error documentation
   - Example: `https://docs.meena.com/errors/resource_not_found`

### Example Error Documentation

```markdown
# Resource Not Found

## Description

This error occurs when the requested resource does not exist or has been deleted.

## Error Code

`resource_not_found`

## HTTP Status Code

404 Not Found

## Common Causes

- The resource ID is incorrect
- The resource has been deleted
- The resource exists but you don't have permission to access it

## Resolution

- Check that the resource ID is correct
- Verify that the resource still exists
- Check your permissions for the resource

## Example Response

```json
{
  "error": {
    "code": "resource_not_found",
    "message": "The requested resource was not found",
    "details": [
      {
        "field": "user_id",
        "issue": "not_found",
        "message": "User with ID '123' does not exist"
      }
    ],
    "trace_id": "abcd1234-ef56-7890-abcd-1234ef567890",
    "documentation_url": "https://docs.meena.com/errors/resource_not_found"
  }
}
```
```

## Localization

### Error Message Localization

1. **Message Keys**:
   - Use message keys instead of hardcoded messages
   - Example: `errors.resource_not_found.message`

2. **Accept-Language Header**:
   - Use the `Accept-Language` header to determine response language
   - Fall back to default language (English)

3. **Translation Files**:
   - Maintain translation files for all supported languages
   - Include variables in translation strings

### Example Implementation

```go
// Translation files
var translations = map[string]map[string]string{
    "en": {
        "errors.resource_not_found.message": "The requested %s was not found",
        "errors.validation_failed.message": "Validation failed",
    },
    "es": {
        "errors.resource_not_found.message": "No se encontró el %s solicitado",
        "errors.validation_failed.message": "La validación falló",
    },
}

// Get localized message
func getLocalizedMessage(key string, language string, args ...interface{}) string {
    // Default to English if language not supported
    if _, ok := translations[language]; !ok {
        language = "en"
    }
    
    // Get message template
    template, ok := translations[language][key]
    if !ok {
        // Fall back to English
        template = translations["en"][key]
        if template == "" {
            return key // Last resort
        }
    }
    
    // Format message with arguments
    return fmt.Sprintf(template, args...)
}

// Usage in error handler
func NewLocalizedNotFoundError(resource string, id string, language string) *AppError {
    message := getLocalizedMessage("errors.resource_not_found.message", language, resource)
    detailMessage := getLocalizedMessage("errors.resource_not_found.detail", language, resource, id)
    
    return &AppError{
        StatusCode: http.StatusNotFound,
        Code:       "resource_not_found",
        Message:    message,
        Details: []ErrorDetail{
            {
                Field:   resource + "_id",
                Issue:   "not_found",
                Message: detailMessage,
            },
        },
    }
}
```

## Conclusion

This standardized error handling approach ensures that all errors in the Meena application are:

1. **Consistent**: Following the same format across all APIs
2. **Informative**: Providing clear and actionable information
3. **Traceable**: Including trace IDs for debugging
4. **Secure**: Not exposing sensitive information
5. **Documented**: Linked to comprehensive documentation

By implementing these standards, we improve the developer experience, make debugging easier, and ensure that errors are handled consistently throughout the application.
