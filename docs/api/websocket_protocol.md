# WebSocket Protocol Specification

This document defines the WebSocket protocol used by the Meena application for real-time communication between clients and the server.

## Overview

Meena uses WebSockets to provide real-time features including:

- Instant messaging
- Presence indicators (online status)
- Typing indicators
- Read receipts
- Call signaling
- Real-time notifications

While REST APIs handle most CRUD operations, WebSockets enable bidirectional, low-latency communication for time-sensitive events.

## Connection Establishment

### WebSocket Endpoint

```
wss://api.meena.com/v1/ws
```

### Authentication

Authentication is performed using a token in the connection request:

```
wss://api.meena.com/v1/ws?token=<jwt_token>
```

Alternatively, authentication can be performed using a header:

```javascript
const socket = new WebSocket('wss://api.meena.com/v1/ws');
socket.setRequestHeader('Authorization', 'Bearer <jwt_token>');
```

### Compression

WebSocket compression is supported to reduce bandwidth usage, especially for mobile clients:

```javascript
// Client-side compression setup
const socket = new WebSocket('wss://api.meena.com/v1/ws', {
  perMessageDeflate: true,
  threshold: 1024 // Only compress messages larger than 1KB
});
```

Server-side configuration:

```go
// Server-side compression setup
upgrader := websocket.Upgrader{
    ReadBufferSize:  1024,
    WriteBufferSize: 1024,
    CheckOrigin:     checkOrigin,
    EnableCompression: true,
    CompressionLevel: 3, // Balance between compression ratio and CPU usage
}
```

Compression statistics are included in the welcome message to help clients optimize their settings:

```json
{
  "type": "welcome",
  "payload": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "server_time": 1626962475,
    "compression": {
      "enabled": true,
      "level": 3,
      "threshold": 1024
    }
  },
  "timestamp": 1626962475
}
```

### Connection Lifecycle

1. **Connection Establishment**:
   - Client initiates WebSocket connection with authentication
   - Server validates token and accepts connection
   - Server sends welcome message with session information

2. **Heartbeat**:
   - Client sends ping every 30 seconds
   - Server responds with pong
   - If no ping is received for 90 seconds, server closes connection

3. **Reconnection**:
   - Client should implement exponential backoff for reconnection attempts
   - Client should maintain a reconnection counter to avoid excessive reconnection attempts

4. **Disconnection**:
   - Client can send a close message to gracefully disconnect
   - Server may close connection due to inactivity, authentication expiration, or server maintenance

## Message Format

All messages exchanged over the WebSocket connection use JSON format with the following structure:

```json
{
  "type": "string",       // Message type
  "id": "string",         // Optional client-generated ID for request-response correlation
  "payload": {},          // Message payload (varies by type)
  "timestamp": 1626962475 // Unix timestamp (seconds)
}
```

### Common Message Types

#### System Messages

- `welcome`: Server sends after successful connection
- `error`: Error notification
- `ping`/`pong`: Heartbeat messages
- `notification`: System notification

#### Presence Messages

- `presence`: User presence update
- `typing_start`: User started typing
- `typing_stop`: User stopped typing

#### Chat Messages

- `message`: New chat message
- `message_update`: Message edited
- `message_delete`: Message deleted
- `message_delivered`: Message delivery confirmation
- `message_read`: Message read confirmation

#### Call Messages

- `call_offer`: WebRTC offer for call initiation
- `call_answer`: WebRTC answer for call acceptance
- `call_ice_candidate`: WebRTC ICE candidate
- `call_end`: Call termination

## Detailed Message Specifications

### System Messages

#### Welcome Message

Sent by the server after successful connection:

```json
{
  "type": "welcome",
  "payload": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "server_time": 1626962475
  },
  "timestamp": 1626962475
}
```

#### Error Message

Sent by the server when an error occurs:

```json
{
  "type": "error",
  "payload": {
    "code": 4001,
    "message": "Invalid message format",
    "request_id": "abc-123" // If the error relates to a specific request
  },
  "timestamp": 1626962475
}
```

#### Ping/Pong Messages

Used for connection heartbeat:

```json
{
  "type": "ping",
  "id": "ping-123",
  "timestamp": 1626962475
}
```

```json
{
  "type": "pong",
  "id": "ping-123", // Echo the ping ID
  "timestamp": 1626962476
}
```

#### Notification Message

System notifications:

```json
{
  "type": "notification",
  "payload": {
    "notification_id": "notif-123",
    "category": "friend_request",
    "title": "New Friend Request",
    "body": "John Doe sent you a friend request",
    "data": {
      "user_id": "456e7890-e89b-12d3-a456-426614174000"
    },
    "read": false
  },
  "timestamp": 1626962475
}
```

### Presence Messages

#### Presence Update

Sent when a user's presence status changes:

```json
{
  "type": "presence",
  "payload": {
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "online", // online, away, offline
    "last_active": 1626962475
  },
  "timestamp": 1626962475
}
```

#### Typing Indicator

Sent when a user starts or stops typing:

```json
{
  "type": "typing_start",
  "payload": {
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000"
  },
  "timestamp": 1626962475
}
```

```json
{
  "type": "typing_stop",
  "payload": {
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000"
  },
  "timestamp": 1626962480
}
```

### Chat Messages

#### New Message

Sent when a new message is created:

```json
{
  "type": "message",
  "payload": {
    "message_id": "abc123-message",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000",
    "sender_id": "123e4567-e89b-12d3-a456-426614174000",
    "sender_handle": "johndoe",
    "content": "Hello, how are you?",
    "type": "text",
    "reply_to_id": null,
    "media": [],
    "created_at": 1626962475
  },
  "timestamp": 1626962475
}
```

#### Message Update

Sent when a message is edited:

```json
{
  "type": "message_update",
  "payload": {
    "message_id": "abc123-message",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000",
    "content": "Hello, how are you today?",
    "edited_at": 1626962500
  },
  "timestamp": 1626962500
}
```

#### Message Delete

Sent when a message is deleted:

```json
{
  "type": "message_delete",
  "payload": {
    "message_id": "abc123-message",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000",
    "deleted_at": 1626962550
  },
  "timestamp": 1626962550
}
```

#### Message Delivered

Sent when a message is delivered to a recipient:

```json
{
  "type": "message_delivered",
  "payload": {
    "message_id": "abc123-message",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000",
    "user_id": "456e7890-e89b-12d3-a456-426614174000",
    "delivered_at": 1626962480
  },
  "timestamp": 1626962480
}
```

#### Message Read

Sent when a message is read by a recipient:

```json
{
  "type": "message_read",
  "payload": {
    "message_id": "abc123-message",
    "chat_id": "789e0123-e89b-12d3-a456-426614174000",
    "user_id": "456e7890-e89b-12d3-a456-426614174000",
    "read_at": 1626962490
  },
  "timestamp": 1626962490
}
```

### Call Messages

#### Call Offer

Sent to initiate a call:

```json
{
  "type": "call_offer",
  "id": "call-offer-123",
  "payload": {
    "call_id": "call-789",
    "caller_id": "123e4567-e89b-12d3-a456-426614174000",
    "callee_id": "456e7890-e89b-12d3-a456-426614174000",
    "call_type": "video", // audio, video
    "sdp": "v=0\no=- 7614219274584779017 2 IN IP4 127.0.0.1\ns=-\nt=0 0\na=group:BUNDLE 0\na=msid-semantic: WMS\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\nc=IN IP4 0.0.0.0\na=ice-ufrag:XgKb\na=ice-pwd:H0kJwPevOto4aJtMJLOXjOXy\na=ice-options:trickle\na=fingerprint:sha-256 58:B5:15:3A:46:D9:5D:63:5D:A7:78:65:75:85:9B:B9:7B:10:23:B8:A9:7F:9D:95:C8:8D:CD:37:85:1F:67:D5\na=setup:actpass\na=mid:0\na=sctp-port:5000\na=max-message-size:262144\n"
  },
  "timestamp": 1626962600
}
```

#### Call Answer

Sent to accept a call:

```json
{
  "type": "call_answer",
  "id": "call-answer-123",
  "payload": {
    "call_id": "call-789",
    "user_id": "456e7890-e89b-12d3-a456-426614174000",
    "sdp": "v=0\no=- 7614219274584779017 2 IN IP4 127.0.0.1\ns=-\nt=0 0\na=group:BUNDLE 0\na=msid-semantic: WMS\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\nc=IN IP4 0.0.0.0\na=ice-ufrag:XgKb\na=ice-pwd:H0kJwPevOto4aJtMJLOXjOXy\na=ice-options:trickle\na=fingerprint:sha-256 58:B5:15:3A:46:D9:5D:63:5D:A7:78:65:75:85:9B:B9:7B:10:23:B8:A9:7F:9D:95:C8:8D:CD:37:85:1F:67:D5\na=setup:active\na=mid:0\na=sctp-port:5000\na=max-message-size:262144\n"
  },
  "timestamp": 1626962610
}
```

#### ICE Candidate

Sent during call setup for WebRTC ICE negotiation:

```json
{
  "type": "call_ice_candidate",
  "payload": {
    "call_id": "call-789",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "candidate": "candidate:1 1 UDP 2122252543 ************* 49152 typ host",
    "sdp_mid": "0",
    "sdp_m_line_index": 0
  },
  "timestamp": 1626962615
}
```

#### Call End

Sent to terminate a call:

```json
{
  "type": "call_end",
  "payload": {
    "call_id": "call-789",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "reason": "completed", // completed, missed, rejected, error
    "duration": 120 // seconds, if completed
  },
  "timestamp": 1626962720
}
```

## Subscription Model

Clients can subscribe to specific topics to receive relevant events:

### Subscribe Message

Sent by client to subscribe to a topic:

```json
{
  "type": "subscribe",
  "id": "sub-123",
  "payload": {
    "topic": "chat:789e0123-e89b-12d3-a456-426614174000",
    "options": {
      "include_history": true,
      "history_size": 50
    }
  },
  "timestamp": 1626962475
}
```

### Unsubscribe Message

Sent by client to unsubscribe from a topic:

```json
{
  "type": "unsubscribe",
  "id": "unsub-123",
  "payload": {
    "topic": "chat:789e0123-e89b-12d3-a456-426614174000"
  },
  "timestamp": 1626962575
}
```

### Subscription Confirmation

Sent by server to confirm subscription:

```json
{
  "type": "subscribed",
  "id": "sub-123", // Echo the subscribe request ID
  "payload": {
    "topic": "chat:789e0123-e89b-12d3-a456-426614174000",
    "status": "success"
  },
  "timestamp": 1626962476
}
```

### Available Topics

- `user:{user_id}`: User-specific events (notifications, friend requests)
- `presence:{user_id}`: Presence updates for a specific user
- `chat:{chat_id}`: Messages and typing indicators for a specific chat
- `call:{call_id}`: Call signaling for a specific call
- `group:{group_id}`: Group-related events (member changes, settings updates)

## Error Handling

### Error Codes

- `4000`: Generic error
- `4001`: Invalid message format
- `4002`: Authentication error
- `4003`: Authorization error (insufficient permissions)
- `4004`: Resource not found
- `4005`: Rate limit exceeded
- `4006`: Invalid operation
- `4007`: Server error

### Error Response

```json
{
  "type": "error",
  "payload": {
    "code": 4003,
    "message": "Not authorized to access this chat",
    "request_id": "sub-123" // If the error relates to a specific request
  },
  "timestamp": 1626962476
}
```

## Rate Limiting

To prevent abuse, the WebSocket connection is subject to rate limiting:

- Maximum 10 messages per second per connection
- Maximum 100 messages per minute per connection
- Maximum 50 subscription requests per minute per connection

When a rate limit is exceeded, the server will send an error message with code `4005`.

## Security Considerations

### Authentication

- WebSocket connections require a valid JWT token
- Tokens have a limited lifespan (typically 1 hour)
- When a token expires, the server will send a token expiration message
- Clients should reconnect with a new token

### Authorization

- Clients can only subscribe to topics they have permission to access
- Permissions are verified on each subscription request
- Permissions may change during a session (e.g., if removed from a group)

### Transport Security

- All WebSocket connections must use WSS (WebSocket Secure)
- TLS 1.2 or higher is required

## Client Implementation Guidelines

### Connection Management

1. **Establish Connection**:
   ```javascript
   const socket = new WebSocket('wss://api.meena.com/v1/ws?token=' + jwtToken);
   ```

2. **Handle Connection Events**:
   ```javascript
   socket.onopen = (event) => {
     console.log('WebSocket connection established');
     startHeartbeat();
   };

   socket.onclose = (event) => {
     console.log('WebSocket connection closed:', event.code, event.reason);
     stopHeartbeat();
     if (shouldReconnect(event)) {
       scheduleReconnection();
     }
   };

   socket.onerror = (error) => {
     console.error('WebSocket error:', error);
   };
   ```

### Offline Message Handling

When a client goes offline, messages should be queued locally and synchronized when the connection is restored:

```javascript
class WebSocketClient {
  constructor(url, token) {
    this.url = url;
    this.token = token;
    this.messageQueue = [];
    this.isConnected = false;
    this.pendingMessages = new Map(); // Messages waiting for acknowledgment
    this.connect();

    // Listen for online/offline events
    window.addEventListener('online', () => this.handleOnline());
    window.addEventListener('offline', () => this.handleOffline());
  }

  connect() {
    this.socket = new WebSocket(`${this.url}?token=${this.token}`);

    this.socket.onopen = () => {
      this.isConnected = true;
      console.log('WebSocket connected');

      // Process queued messages
      this.processQueue();
    };

    this.socket.onclose = () => {
      this.isConnected = false;
      console.log('WebSocket disconnected');
    };

    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);

      // Check if this is an acknowledgment for a pending message
      if (message.type === 'ack' && message.payload.original_id) {
        const originalId = message.payload.original_id;
        if (this.pendingMessages.has(originalId)) {
          // Message was delivered successfully
          const pendingMessage = this.pendingMessages.get(originalId);
          this.pendingMessages.delete(originalId);

          // Notify application layer of successful delivery
          if (pendingMessage.onAck) {
            pendingMessage.onAck(message);
          }
        }
      }

      // Handle normal message
      this.handleMessage(message);
    };
  }

  sendMessage(type, payload, options = {}) {
    const message = {
      type,
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      payload,
      timestamp: Math.floor(Date.now() / 1000),
      requires_ack: options.requiresAck || false
    };

    if (this.isConnected) {
      this.socket.send(JSON.stringify(message));

      // If acknowledgment is required, store the message
      if (options.requiresAck) {
        this.pendingMessages.set(message.id, {
          message,
          timestamp: Date.now(),
          onAck: options.onAck,
          onTimeout: options.onTimeout
        });

        // Set timeout for acknowledgment
        setTimeout(() => {
          if (this.pendingMessages.has(message.id)) {
            const pendingMessage = this.pendingMessages.get(message.id);
            this.pendingMessages.delete(message.id);

            // If still offline, requeue the message
            if (!this.isConnected) {
              this.queueMessage(message);
            }

            // Notify application layer of timeout
            if (pendingMessage.onTimeout) {
              pendingMessage.onTimeout(message);
            }
          }
        }, options.ackTimeout || 10000);
      }
    } else {
      // Queue message for later
      this.queueMessage(message);
    }

    return message.id;
  }

  queueMessage(message) {
    // Store in memory
    this.messageQueue.push(message);

    // Also persist to local storage for resilience
    try {
      const storedQueue = JSON.parse(localStorage.getItem('websocket_queue') || '[]');
      storedQueue.push(message);
      localStorage.setItem('websocket_queue', JSON.stringify(storedQueue));
    } catch (e) {
      console.error('Failed to persist message to local storage:', e);
    }
  }

  processQueue() {
    // First try to load any messages from local storage
    try {
      const storedQueue = JSON.parse(localStorage.getItem('websocket_queue') || '[]');
      this.messageQueue = [...storedQueue, ...this.messageQueue];
      localStorage.removeItem('websocket_queue');
    } catch (e) {
      console.error('Failed to load messages from local storage:', e);
    }

    // Process queued messages
    if (this.messageQueue.length > 0 && this.isConnected) {
      console.log(`Processing ${this.messageQueue.length} queued messages`);

      // Clone the queue and clear it
      const queue = [...this.messageQueue];
      this.messageQueue = [];

      // Send each message
      queue.forEach(message => {
        // Update timestamp before sending
        message.timestamp = Math.floor(Date.now() / 1000);
        this.socket.send(JSON.stringify(message));
      });
    }
  }

  handleOnline() {
    console.log('Device is online');
    if (!this.isConnected) {
      this.connect();
    }
  }

  handleOffline() {
    console.log('Device is offline');
    // Connection will close automatically
  }

  handleMessage(message) {
    // Application-specific message handling
  }
}
```

3. **Implement Heartbeat**:
   ```javascript
   let heartbeatInterval;

   function startHeartbeat() {
     heartbeatInterval = setInterval(() => {
       if (socket.readyState === WebSocket.OPEN) {
         const pingMessage = {
           type: 'ping',
           id: 'ping-' + Date.now(),
           timestamp: Math.floor(Date.now() / 1000)
         };
         socket.send(JSON.stringify(pingMessage));
       }
     }, 30000); // 30 seconds
   }

   function stopHeartbeat() {
     clearInterval(heartbeatInterval);
   }
   ```

4. **Implement Reconnection**:
   ```javascript
   let reconnectAttempts = 0;
   const maxReconnectAttempts = 10;

   function shouldReconnect(closeEvent) {
     // Don't reconnect if closure was clean and intentional
     return closeEvent.code !== 1000 && reconnectAttempts < maxReconnectAttempts;
   }

   function scheduleReconnection() {
     const delay = Math.min(30, Math.pow(2, reconnectAttempts)) * 1000;
     setTimeout(() => {
       reconnectAttempts++;
       // Reconnect logic here
     }, delay);
   }
   ```

### Message Handling

1. **Process Incoming Messages**:
   ```javascript
   socket.onmessage = (event) => {
     try {
       const message = JSON.parse(event.data);
       handleMessage(message);
     } catch (error) {
       console.error('Error parsing message:', error);
     }
   };

   function handleMessage(message) {
     switch (message.type) {
       case 'welcome':
         handleWelcome(message.payload);
         break;
       case 'message':
         handleChatMessage(message.payload);
         break;
       case 'presence':
         handlePresenceUpdate(message.payload);
         break;
       case 'error':
         handleError(message.payload);
         break;
       case 'pong':
         // Heartbeat response, no action needed
         break;
       default:
         console.log('Unhandled message type:', message.type);
     }
   }
   ```

2. **Send Messages**:
   ```javascript
   function sendMessage(chatId, content, type = 'text') {
     const message = {
       type: 'message',
       id: 'msg-' + Date.now(),
       payload: {
         chat_id: chatId,
         content: content,
         type: type
       },
       timestamp: Math.floor(Date.now() / 1000)
     };
     socket.send(JSON.stringify(message));
   }
   ```

3. **Subscribe to Topics**:
   ```javascript
   function subscribeToChatMessages(chatId) {
     const subscribeMessage = {
       type: 'subscribe',
       id: 'sub-' + Date.now(),
       payload: {
         topic: `chat:${chatId}`
       },
       timestamp: Math.floor(Date.now() / 1000)
     };
     socket.send(JSON.stringify(subscribeMessage));
   }
   ```

## Server Implementation Guidelines

### Connection Handling

1. **Authenticate Connection**:
   ```go
   func handleWebSocketConnection(w http.ResponseWriter, r *http.Request) {
       // Extract and validate token
       token := r.URL.Query().Get("token")
       if token == "" {
           token = extractTokenFromHeader(r)
       }

       userId, err := validateToken(token)
       if err != nil {
           http.Error(w, "Unauthorized", http.StatusUnauthorized)
           return
       }

       // Upgrade HTTP connection to WebSocket
       conn, err := upgrader.Upgrade(w, r, nil)
       if err != nil {
           log.Printf("Error upgrading to WebSocket: %v", err)
           return
       }

       // Create client session
       client := &Client{
           ID:     generateSessionId(),
           UserID: userId,
           Conn:   conn,
           Send:   make(chan []byte, 256),
       }

       // Register client
       hub.register <- client

       // Send welcome message
       welcomeMsg := createWelcomeMessage(client)
       client.Send <- welcomeMsg

       // Start client goroutines
       go client.readPump()
       go client.writePump()
   }
   ```

2. **Handle Client Messages**:
   ```go
   func (c *Client) readPump() {
       defer func() {
           hub.unregister <- c
           c.Conn.Close()
       }()

       c.Conn.SetReadLimit(maxMessageSize)
       c.Conn.SetReadDeadline(time.Now().Add(pongWait))
       c.Conn.SetPongHandler(func(string) error {
           c.Conn.SetReadDeadline(time.Now().Add(pongWait))
           return nil
       })

       for {
           _, message, err := c.Conn.ReadMessage()
           if err != nil {
               if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                   log.Printf("WebSocket error: %v", err)
               }
               break
           }

           var msg Message
           if err := json.Unmarshal(message, &msg); err != nil {
               errorMsg := createErrorMessage(4001, "Invalid message format", "")
               c.Send <- errorMsg
               continue
           }

           // Process message based on type
           processMessage(c, &msg)
       }
   }
   ```

3. **Implement Subscription Logic**:
   ```go
   func handleSubscribe(client *Client, msg *Message) {
       var subRequest SubscribeRequest
       if err := json.Unmarshal(msg.Payload, &subRequest); err != nil {
           errorMsg := createErrorMessage(4001, "Invalid subscription request", msg.ID)
           client.Send <- errorMsg
           return
       }

       // Check if client has permission to subscribe to this topic
       if !hasPermission(client.UserID, subRequest.Topic) {
           errorMsg := createErrorMessage(4003, "Not authorized to access this topic", msg.ID)
           client.Send <- errorMsg
           return
       }

       // Subscribe client to topic
       hub.subscribe <- Subscription{
           Client: client,
           Topic:  subRequest.Topic,
       }

       // Send confirmation
       confirmMsg := createSubscriptionConfirmation(subRequest.Topic, msg.ID)
       client.Send <- confirmMsg

       // Send history if requested
       if subRequest.Options.IncludeHistory {
           history := getTopicHistory(subRequest.Topic, subRequest.Options.HistorySize)
           for _, historyMsg := range history {
               client.Send <- historyMsg
           }
       }
   }
   ```

## Versioning and Compatibility

### Protocol Versioning

The WebSocket protocol is versioned to allow for future changes:

1. **Version Negotiation**:
   - Client can specify desired protocol version in connection URL
   - Server responds with supported version in welcome message
   - If client version is not supported, server suggests alternative version

2. **Backward Compatibility**:
   - Minor version changes maintain backward compatibility
   - Major version changes may break compatibility
   - Server supports multiple versions simultaneously during transition periods

### Example Version Negotiation

Client connection with version:
```
wss://api.meena.com/v1/ws?token=<jwt_token>&version=1.1
```

Server welcome message:
```json
{
  "type": "welcome",
  "payload": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "server_time": 1626962475,
    "protocol_version": "1.1"
  },
  "timestamp": 1626962475
}
```

## Conclusion

This WebSocket protocol specification provides a comprehensive framework for real-time communication in the Meena application. By following this specification, clients and servers can establish reliable, secure, and efficient real-time communication channels.

The protocol supports all the real-time features required by the application, including instant messaging, presence indicators, typing indicators, read receipts, and call signaling. It also provides mechanisms for error handling, rate limiting, and security.

Developers should refer to this document when implementing WebSocket clients and servers for the Meena application to ensure interoperability and consistency across all platforms.
