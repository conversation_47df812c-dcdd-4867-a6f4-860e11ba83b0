# Contact Components Documentation

This document provides an overview of the reusable UI components for the contacts feature in the Meena app.

## Screens

### ContactListScreen

**Purpose:** Displays a list of the user's contacts with sections for favorites, recent contacts, and all contacts.

**Key Features:**
- Search functionality
- Relationship filtering
- Horizontal sections for favorites and recent contacts
- Vertical list for all contacts
- Empty state handling

**Usage:**
```kotlin
ContactListScreen(
    onNavigateToAddContact = { /* Navigate to add contact screen */ },
    onNavigateToContactDetail = { contactId -> /* Navigate to contact detail screen */ },
    onNavigateToContactGroups = { /* Navigate to contact groups screen */ }
)
```

### ContactDetailScreen

**Purpose:** Displays detailed information about a contact and provides actions like messaging, editing, blocking/unblocking, and adding/removing from favorites.

**Key Features:**
- Contact information display
- Action buttons for messaging, editing, blocking/unblocking, and favorites
- Confirmation dialogs for blocking/unblocking

**Usage:**
```kotlin
ContactDetailScreen(
    contactId = "contact-id",
    onNavigateBack = { /* Navigate back */ },
    onNavigateToEditContact = { contactId -> /* Navigate to edit contact screen */ }
)
```

### AddContactScreen

**Purpose:** Allows users to add a new contact by entering a Meena ID or scanning a QR code.

**Key Features:**
- Form for entering contact details
- QR code scanning functionality
- Validation

**Usage:**
```kotlin
AddContactScreen(
    onNavigateBack = { /* Navigate back */ }
)
```

### EditContactScreen

**Purpose:** Allows users to edit an existing contact's details.

**Key Features:**
- Form for editing contact details
- Validation

**Usage:**
```kotlin
EditContactScreen(
    contactId = "contact-id",
    onNavigateBack = { /* Navigate back */ }
)
```

### ContactGroupsScreen

**Purpose:** Displays a list of the user's contact groups and allows creating new groups.

**Key Features:**
- Group list
- Create group dialog
- Empty state handling

**Usage:**
```kotlin
ContactGroupsScreen(
    onNavigateToGroupDetail = { groupId -> /* Navigate to group detail screen */ }
)
```

### ContactGroupDetailScreen

**Purpose:** Displays detailed information about a contact group and allows managing its members.

**Key Features:**
- Group information display
- Member list
- Add/remove members functionality

**Usage:**
```kotlin
ContactGroupDetailScreen(
    groupId = "group-id",
    onNavigateBack = { /* Navigate back */ },
    onNavigateToContactDetail = { contactId -> /* Navigate to contact detail screen */ }
)
```

## Components

### ContactListItem

**Purpose:** Displays a contact in a vertical list.

**Key Features:**
- Avatar with initial
- Contact name and details
- Indicators for favorite and blocked status
- Long-press context menu with actions
- Optional remove button

**Usage:**
```kotlin
ContactListItem(
    contact = contact,
    onClick = { /* Handle click */ },
    onRemove = { /* Handle remove */ },
    onEdit = { /* Handle edit */ },
    onMessage = { /* Handle message */ },
    onToggleFavorite = { /* Handle toggle favorite */ },
    onToggleBlocked = { /* Handle toggle blocked */ }
)
```

### ContactHorizontalItem

**Purpose:** Displays a contact in a horizontal list.

**Key Features:**
- Avatar with initial
- Contact name
- Indicators for favorite and blocked status

**Usage:**
```kotlin
ContactHorizontalItem(
    contact = contact,
    onClick = { /* Handle click */ }
)
```

### ContactSection

**Purpose:** Displays a section of contacts in a horizontal list with a title.

**Key Features:**
- Section title
- Horizontal list of contacts
- Empty state handling

**Usage:**
```kotlin
ContactSection(
    title = "Favorites",
    contacts = favoriteContacts,
    onContactClick = { contact -> /* Handle click */ },
    emptyText = "No favorite contacts"
)
```

### ContactSearchBar

**Purpose:** Provides a search bar for filtering contacts.

**Key Features:**
- Search input field
- Clear button

**Usage:**
```kotlin
ContactSearchBar(
    query = searchQuery,
    onQueryChange = { /* Update query */ },
    onSearch = { query -> /* Perform search */ }
)
```

### RelationshipFilterChips

**Purpose:** Provides filter chips for filtering contacts by relationship.

**Key Features:**
- Horizontal list of filter chips
- Selection state

**Usage:**
```kotlin
RelationshipFilterChips(
    selectedRelationship = selectedRelationship,
    onRelationshipSelected = { relationship -> /* Update filter */ }
)
```

### RelationshipDropdown

**Purpose:** Provides a dropdown menu for selecting a relationship.

**Key Features:**
- Dropdown menu with relationship options

**Usage:**
```kotlin
RelationshipDropdown(
    selectedRelationship = relationship,
    onRelationshipSelected = { /* Update relationship */ },
    relationships = relationships,
    isExpanded = isExpanded,
    onExpandedChange = { /* Update expanded state */ }
)
```

### ContactGroupItem

**Purpose:** Displays a contact group in a vertical list.

**Key Features:**
- Group name and member count
- Click handling

**Usage:**
```kotlin
ContactGroupItem(
    group = group,
    onClick = { /* Handle click */ }
)
```

### SwipeToDelete

**Purpose:** Provides swipe-to-delete functionality for list items.

**Key Features:**
- Swipe animation
- Delete confirmation
- Customizable content

**Usage:**
```kotlin
SwipeToDelete(
    item = item,
    onDelete = { /* Handle delete */ }
) { swipeItem ->
    // Content to display
    YourListItem(item = swipeItem)
}
```

### LongPressContextMenu

**Purpose:** Provides a context menu on long-press.

**Key Features:**
- Customizable menu items
- Icons for menu items
- Handles click events

**Usage:**
```kotlin
LongPressContextMenu(
    menuItems = listOf(
        ContextMenuItem(
            text = "Edit",
            icon = Icons.Default.Edit,
            onClick = { /* Handle edit */ }
        ),
        ContextMenuItem(
            text = "Delete",
            icon = Icons.Default.Delete,
            onClick = { /* Handle delete */ }
        )
    )
) { clickableModifier ->
    // Content to display
    YourContent(modifier = clickableModifier)
}
```

## Extension Functions

### Contact.isBlocked()

**Purpose:** Checks if a contact is blocked.

**Usage:**
```kotlin
if (contact.isBlocked()) {
    // Contact is blocked
}
```

### Contact.isFavorite()

**Purpose:** Checks if a contact is a favorite.

**Usage:**
```kotlin
if (contact.isFavorite()) {
    // Contact is a favorite
}
```

### Contact.getInitial()

**Purpose:** Gets the first letter of a contact's display name.

**Usage:**
```kotlin
val initial = contact.getInitial()
```

## Models

### UIContact

**Purpose:** UI model for displaying a contact in the UI.

**Key Properties:**
- id: String
- userId: String
- contactUserId: String
- handle: String
- name: String
- avatarUrl: String?
- isBlocked: Boolean

**Usage:**
```kotlin
val uiContact = UIContact.fromContactAndUser(contact, user)
```
