# Error Handling Guide

This guide provides detailed instructions for handling errors in our new architecture approach.

## Overview

Error handling is a critical aspect of any application. In our architecture, we've implemented a centralized approach to error handling that ensures errors are handled consistently across the app.

The key components of our error handling approach are:

1. **Result<T>**: A Kotlin standard library class that represents either success (`Result.success(value)`) or failure (`Result.failure(throwable)`).
2. **ErrorHandler**: A class that provides methods for handling errors consistently across the app.
3. **OperationState**: A class that represents the state of a specific operation, including whether it's in progress, successful, or has an error.
4. **UiStateProperties**: A class that includes common properties for UI state classes, including an error message.

## Error Handling Flow

The error handling flow in our architecture is as follows:

1. **Data Layer**: Errors are caught and wrapped in `Result.failure(throwable)`.
2. **Domain Layer**: Errors are propagated up the call stack using `Result<T>`.
3. **Presentation Layer**: Errors are handled by the ViewModel and displayed to the user.

## Error Handling in the Data Layer

In the data layer, errors are caught and wrapped in `Result.failure(throwable)`. This is done using the `executeNetworkOperation`, `executeDatabaseOperation`, and `executeFileOperation` methods in the `BaseRepository` interface.

```kotlin
interface BaseRepository {
    /**
     * Execute a network operation and return a Result.
     *
     * @param block The network operation to execute.
     * @return A Result containing the result of the operation.
     */
    suspend fun <T> executeNetworkOperation(block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Execute a database operation and return a Result.
     *
     * @param block The database operation to execute.
     * @return A Result containing the result of the operation.
     */
    suspend fun <T> executeDatabaseOperation(block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Execute a file operation and return a Result.
     *
     * @param block The file operation to execute.
     * @return A Result containing the result of the operation.
     */
    suspend fun <T> executeFileOperation(block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

Example of using these methods in a repository implementation:

```kotlin
class AuthRepositoryImpl @Inject constructor(
    private val authApi: AuthApi,
    private val authDao: AuthDao,
    private val tokenManager: TokenManager
) : AuthRepository, BaseRepository {

    override suspend fun login(identifier: String, password: String): Result<LoginResponse> {
        return executeNetworkOperation {
            val response = authApi.login(LoginRequest(identifier, password))
            tokenManager.saveTokens(response.accessToken, response.refreshToken)
            LoginResponse(
                userId = response.userId,
                userHandle = response.userHandle,
                requires2fa = response.requires2fa
            )
        }
    }
    
    // Other methods...
}
```

## Error Handling in the Domain Layer

In the domain layer, errors are propagated up the call stack using `Result<T>`. Use cases simply pass the `Result<T>` from the repository to the ViewModel.

```kotlin
class LoginUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(identifier: String, password: String): Result<LoginResponse> {
        return authRepository.login(identifier, password)
    }
}
```

## Error Handling in the Presentation Layer

In the presentation layer, errors are handled by the ViewModel and displayed to the user. The `BaseViewModel` class provides methods for handling errors consistently across the app.

```kotlin
abstract class BaseViewModel(
    protected val errorHandler: ErrorHandler
) : ViewModel() {

    // Application context from the ErrorHandler
    protected val appContext = errorHandler.getApplicationContext()

    // Error state
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    /**
     * Clear the error message.
     */
    open fun clearError() {
        _error.value = null
    }

    /**
     * Set an error message.
     *
     * @param message The error message.
     */
    protected fun setError(message: String) {
        _error.value = message
    }

    /**
     * Set an error from a throwable.
     *
     * @param throwable The throwable.
     * @param fallbackMessage Optional fallback message if the throwable doesn't have a message.
     */
    protected fun setError(throwable: Throwable, fallbackMessage: String? = null) {
        _error.value = errorHandler.getErrorMessage(throwable, fallbackMessage)
    }

    /**
     * Set the loading state.
     *
     * @param isLoading Whether the ViewModel is loading data.
     */
    protected fun setLoading(isLoading: Boolean) {
        _isLoading.value = isLoading
    }

    /**
     * Launch a coroutine with error handling.
     *
     * @param block The coroutine block to execute.
     */
    protected fun launchWithErrorHandling(block: suspend CoroutineScope.() -> Unit) {
        viewModelScope.launch(
            CoroutineExceptionHandler { _, throwable ->
                setError(throwable)
                setLoading(false)
            }
        ) {
            block()
        }
    }

    /**
     * Execute a use case and handle the result.
     *
     * @param useCase The use case to execute.
     * @param onSuccess The callback to execute on success.
     * @param onError The callback to execute on error. If not provided, the error will be set automatically.
     * @param showLoading Whether to show the loading state.
     */
    protected suspend fun <T> executeUseCase(
        useCase: suspend () -> Result<T>,
        onSuccess: (T) -> Unit,
        onError: ((Throwable) -> Unit)? = null,
        showLoading: Boolean = true
    ) {
        if (showLoading) {
            setLoading(true)
        }

        try {
            val result = useCase()
            result.fold(
                onSuccess = {
                    onSuccess(it)
                    if (showLoading) {
                        setLoading(false)
                    }
                },
                onFailure = { throwable ->
                    if (onError != null) {
                        onError(throwable)
                    } else {
                        setError(throwable)
                    }
                    if (showLoading) {
                        setLoading(false)
                    }
                }
            )
        } catch (e: Exception) {
            if (onError != null) {
                onError(e)
            } else {
                setError(e)
            }
            if (showLoading) {
                setLoading(false)
            }
        }
    }
}
```

Example of using these methods in a ViewModel:

```kotlin
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    private val registerUseCase: RegisterUseCase,
    private val twoFactorAuthUseCase: TwoFactorAuthUseCase,
    private val recoverAccountUseCase: RecoverAccountUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    fun login(identifier: String, password: String) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                loginOperation = it.loginOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }
            
            executeUseCase(
                useCase = { loginUseCase(identifier, password) },
                onSuccess = { response ->
                    _authState.update { it.copy(
                        loginOperation = it.loginOperation.success(),
                        isLoggedIn = !response.requires2fa,
                        requires2fa = response.requires2fa,
                        userId = response.userId,
                        userHandle = response.userHandle,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Login failed")
                    _authState.update { it.copy(
                        loginOperation = it.loginOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }
    
    // Other methods...
}
```

## Error Handler

The `ErrorHandler` class provides methods for handling errors consistently across the app. It converts exceptions into user-friendly error messages.

```kotlin
class ErrorHandler @Inject constructor(
    private val context: Context
) {
    /**
     * Get the application context.
     *
     * @return The application context.
     */
    fun getApplicationContext(): Context {
        return context
    }

    /**
     * Get a user-friendly error message from a throwable.
     *
     * @param throwable The throwable.
     * @param fallbackMessage Optional fallback message if the throwable doesn't have a message.
     * @return A user-friendly error message.
     */
    fun getErrorMessage(throwable: Throwable, fallbackMessage: String? = null): String {
        return when (throwable) {
            is IOException -> context.getString(R.string.error_network)
            is HttpException -> {
                when (throwable.code()) {
                    401 -> context.getString(R.string.error_unauthorized)
                    403 -> context.getString(R.string.error_forbidden)
                    404 -> context.getString(R.string.error_not_found)
                    else -> throwable.message() ?: fallbackMessage ?: context.getString(R.string.error_unknown)
                }
            }
            else -> throwable.message ?: fallbackMessage ?: context.getString(R.string.error_unknown)
        }
    }
}
```

## Operation State

The `OperationState` class represents the state of a specific operation, including whether it's in progress, successful, or has an error.

```kotlin
data class OperationState(
    val isInProgress: Boolean = false,
    val isSuccessful: Boolean = false,
    val error: String? = null
)
```

Extension functions for `OperationState`:

```kotlin
/**
 * Extension function to start an operation.
 *
 * @return A new OperationState with isInProgress set to true.
 */
fun OperationState.start(): OperationState {
    return copy(
        isInProgress = true,
        isSuccessful = false,
        error = null
    )
}

/**
 * Extension function to mark an operation as successful.
 *
 * @return A new OperationState with isInProgress set to false and isSuccessful set to true.
 */
fun OperationState.success(): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = true,
        error = null
    )
}

/**
 * Extension function to mark an operation as failed.
 *
 * @param error The error message.
 * @return A new OperationState with isInProgress set to false and the error message set.
 */
fun OperationState.failure(error: String): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = false,
        error = error
    )
}

/**
 * Extension function to reset an operation.
 *
 * @return A new OperationState with all fields reset to their default values.
 */
fun OperationState.reset(): OperationState {
    return OperationState()
}
```

## UI State Properties

The `UiStateProperties` class includes common properties for UI state classes, including an error message.

```kotlin
data class UiStateProperties(
    val isLoading: Boolean = false,
    val error: String? = null
)
```

## Displaying Errors in the UI

In the UI, errors are displayed using a `SnackbarHost` and `LaunchedEffect`.

```kotlin
@Composable
fun LoginScreen(
    onNavigateToRegister: () -> Unit,
    onNavigateToRecovery: () -> Unit,
    onLoginSuccess: () -> Unit,
    onNavigateTo2FA: () -> Unit,
    viewModel: AuthViewModel = hiltViewModel()
) {
    val authState by viewModel.authState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Show error message
    LaunchedEffect(authState.error) {
        authState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }
    
    // Show operation error message
    LaunchedEffect(authState.loginOperation.error) {
        authState.loginOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetOperationStates()
        }
    }
    
    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        // UI content
    }
}
```

## Best Practices

### 1. Use Result<T> for Error Propagation

Use `Result<T>` to propagate errors up the call stack. This ensures that errors are handled consistently across the app.

```kotlin
suspend fun login(identifier: String, password: String): Result<LoginResponse> {
    return executeNetworkOperation {
        val response = authApi.login(LoginRequest(identifier, password))
        tokenManager.saveTokens(response.accessToken, response.refreshToken)
        LoginResponse(
            userId = response.userId,
            userHandle = response.userHandle,
            requires2fa = response.requires2fa
        )
    }
}
```

### 2. Use the ErrorHandler for User-Friendly Error Messages

Use the `ErrorHandler` to convert exceptions into user-friendly error messages.

```kotlin
private fun getErrorMessage(throwable: Throwable, fallback: String? = null): String {
    return errorHandler.getErrorMessage(throwable, fallback)
}
```

### 3. Use OperationState for Operation-Specific Errors

Use `OperationState` to track the state of specific operations, including whether they're in progress, successful, or have an error.

```kotlin
fun login(identifier: String, password: String) {
    launchWithErrorHandling {
        _authState.update { it.copy(
            loginOperation = it.loginOperation.start(),
            properties = it.properties.copy(isLoading = true, error = null)
        ) }
        
        executeUseCase(
            useCase = { loginUseCase(identifier, password) },
            onSuccess = { response ->
                _authState.update { it.copy(
                    loginOperation = it.loginOperation.success(),
                    isLoggedIn = !response.requires2fa,
                    requires2fa = response.requires2fa,
                    userId = response.userId,
                    userHandle = response.userHandle,
                    properties = it.properties.copy(isLoading = false)
                ) }
            },
            onError = { error ->
                val errorMessage = getErrorMessage(error, "Login failed")
                _authState.update { it.copy(
                    loginOperation = it.loginOperation.failure(errorMessage),
                    properties = it.properties.copy(isLoading = false, error = errorMessage)
                ) }
            },
            showLoading = false // We're manually handling loading state
        )
    }
}
```

### 4. Use LaunchedEffect for Displaying Errors

Use `LaunchedEffect` to display errors when the UI state changes. This ensures that errors are only displayed when the state changes, not on every recomposition.

```kotlin
// Show error message
LaunchedEffect(authState.error) {
    authState.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.clearError()
    }
}

// Show operation error message
LaunchedEffect(authState.loginOperation.error) {
    authState.loginOperation.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.resetOperationStates()
    }
}
```

### 5. Reset Error State After Displaying Errors

Reset the error state after displaying errors to prevent the same error from being displayed multiple times.

```kotlin
// Show error message
LaunchedEffect(authState.error) {
    authState.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.clearError()
    }
}

// Show operation error message
LaunchedEffect(authState.loginOperation.error) {
    authState.loginOperation.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.resetOperationStates()
    }
}
```

## Conclusion

By following these guidelines, you can implement error handling that is consistent, maintainable, and user-friendly. The new architecture approach provides a solid foundation for handling errors at all levels of the app.
