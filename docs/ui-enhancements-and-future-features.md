# UI Enhancements and Future Features

This document outlines UI enhancements that have been implemented in the Meena app, as well as planned features that require backend modifications. It serves as a reference for future development.

## Table of Contents

1. [Implemented UI Enhancements](#implemented-ui-enhancements)
   - [Swipe-to-Delete Functionality](#swipe-to-delete-functionality)
   - [Long-Press Context Menu](#long-press-context-menu)
2. [Planned Features Requiring Backend Changes](#planned-features-requiring-backend-changes)
   - [Enhanced Meena ID Integration](#enhanced-meena-id-integration)
   - [Contact Management Enhancements](#contact-management-enhancements)
   - [Privacy-Focused Features](#privacy-focused-features)
3. [Backend Implementation Details](#backend-implementation-details)
   - [Database Schema Changes](#database-schema-changes)
   - [API Specification Updates](#api-specification-updates)
4. [Implementation Priorities](#implementation-priorities)

## Implemented UI Enhancements

### Swipe-to-Delete Functionality

**Description:**
A gesture-based interaction that allows users to delete contacts by swiping them from right to left in the contact list.

**Implementation Details:**
- Created a reusable `SwipeToDelete` component in `app/src/main/java/com/tfkcolin/meena/ui/components/SwipeToDelete.kt`
- Implemented using Material 3's experimental `SwipeToDismissBox` component
- Applied to contact list items in the main contact list and search results

**User Experience Benefits:**
- Provides a natural, intuitive way to delete contacts
- Reduces the number of taps required to delete a contact
- Offers visual feedback during the deletion process

**Code Example:**
```kotlin
SwipeToDelete(
    item = contact,
    onDelete = { viewModel.deleteContact(it.id) }
) { swipeContact ->
    ContactListItem(
        contact = swipeContact,
        onClick = { onNavigateToContactDetail(swipeContact.id) },
        // Other parameters...
    )
}
```

### Long-Press Context Menu

**Description:**
A context menu that appears when a user long-presses on a contact, providing quick access to common actions.

**Implementation Details:**
- Created a reusable `LongPressContextMenu` component in `app/src/main/java/com/tfkcolin/meena/ui/components/LongPressContextMenu.kt`
- Implemented a `ContextMenuItem` data class for menu items with text, icon, and click handler
- Integrated with the `ContactListItem` component to provide context-specific actions

**Available Actions:**
- Message: Start a conversation with the contact
- Edit: Navigate to the edit contact screen
- Add/Remove from Favorites: Toggle favorite status
- Block/Unblock: Toggle blocked status
- Delete: Remove the contact

**User Experience Benefits:**
- Provides quick access to common actions without navigating to another screen
- Reduces the number of taps required to perform actions
- Contextual actions adapt based on the contact's current state

**Code Example:**
```kotlin
LongPressContextMenu(
    menuItems = listOf(
        ContextMenuItem(
            text = "Edit",
            icon = Icons.Default.Edit,
            onClick = { /* Handle edit */ }
        ),
        ContextMenuItem(
            text = "Delete",
            icon = Icons.Default.Delete,
            onClick = { /* Handle delete */ }
        )
    )
) { clickableModifier ->
    // Content to display
    YourContent(modifier = clickableModifier)
}
```

## Planned Features Requiring Backend Changes

### Enhanced Meena ID Integration

#### QR Code Generation for Sharing Meena ID

**Description:**
Allow users to generate and share a QR code containing their Meena ID, making it easier for others to add them as a contact.

**Required Backend Changes:**
- New endpoint for generating QR codes with Meena ID data
- Validation mechanism for QR code data
- Optional: Analytics for QR code usage

**Database Changes:**
- No schema changes required, uses existing user data

**API Specification Update:**
```yaml
/users/qr-code:
  get:
    summary: Generate QR code for the current user's Meena ID
    security:
      - BearerAuth: []
    responses:
      200:
        description: QR code generated successfully
        content:
          image/png:
            schema:
              type: string
              format: binary
          application/json:
            schema:
              type: object
              properties:
                qr_code_url:
                  type: string
                  description: URL to the generated QR code image
                qr_code_data:
                  type: string
                  description: Data encoded in the QR code
```

#### Meena ID Verification System

**Description:**
Add a verification system for Meena IDs to increase trust and security in the platform.

**Required Backend Changes:**
- Verification process and logic
- Storage of verification status
- API endpoints for verification requests and status checks

**Database Changes:**
```sql
ALTER TABLE users ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE users ADD COLUMN verification_date TIMESTAMP WITH TIME ZONE;
```

**API Specification Update:**
```yaml
/users/verify:
  post:
    summary: Request verification for the current user's Meena ID
    security:
      - BearerAuth: []
    responses:
      202:
        description: Verification request accepted
      400:
        description: Invalid request or user already verified

/users/{user_id}/verification-status:
  get:
    summary: Get verification status for a user
    parameters:
      - name: user_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Verification status retrieved
        content:
          application/json:
            schema:
              type: object
              properties:
                is_verified:
                  type: boolean
                verification_date:
                  type: string
                  format: date-time
```

### Contact Management Enhancements

#### Contact Tagging System

**Description:**
Allow users to create custom tags for contacts and filter contacts by these tags.

**Required Backend Changes:**
- Storage and management of user-defined tags
- Association of tags with contacts
- API endpoints for CRUD operations on tags

**Database Changes:**
```sql
CREATE TABLE contact_tags (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) NOT NULL DEFAULT '#CCCCCC',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, name)
);

CREATE TABLE contact_tag_assignments (
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES contact_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    PRIMARY KEY (contact_id, tag_id)
);
```

**API Specification Update:**
```yaml
/contacts/tags:
  get:
    summary: Get all tags for the current user
    security:
      - BearerAuth: []
    responses:
      200:
        description: Tags retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                tags:
                  type: array
                  items:
                    $ref: '#/components/schemas/ContactTag'
  post:
    summary: Create a new tag
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
              color:
                type: string
    responses:
      201:
        description: Tag created successfully

/contacts/tags/{tag_id}:
  put:
    summary: Update a tag
    security:
      - BearerAuth: []
    parameters:
      - name: tag_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
              color:
                type: string
    responses:
      200:
        description: Tag updated successfully
  delete:
    summary: Delete a tag
    security:
      - BearerAuth: []
    parameters:
      - name: tag_id
        in: path
        required: true
        schema:
          type: string
    responses:
      204:
        description: Tag deleted successfully

/contacts/{contact_id}/tags:
  get:
    summary: Get tags for a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Tags retrieved successfully
  post:
    summary: Add tags to a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              tag_ids:
                type: array
                items:
                  type: string
    responses:
      200:
        description: Tags added successfully
  delete:
    summary: Remove tags from a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              tag_ids:
                type: array
                items:
                  type: string
    responses:
      200:
        description: Tags removed successfully
```

#### Batch Operations

**Description:**
Allow users to perform actions on multiple contacts at once, such as adding to a group, tagging, or deleting.

**Required Backend Changes:**
- API endpoints that accept multiple contact IDs
- Transaction handling for batch operations
- Error handling for partial successes

**Database Changes:**
No schema changes required, uses existing tables.

**API Specification Update:**
```yaml
/contacts/batch:
  post:
    summary: Perform batch operations on contacts
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              contact_ids:
                type: array
                items:
                  type: string
              operation:
                type: string
                enum: [delete, block, unblock, add_to_favorites, remove_from_favorites, add_to_group, remove_from_group, add_tags, remove_tags]
              operation_data:
                type: object
                description: Additional data required for the operation (e.g., group_id, tag_ids)
    responses:
      200:
        description: Batch operation completed
        content:
          application/json:
            schema:
              type: object
              properties:
                success_count:
                  type: integer
                failed_count:
                  type: integer
                failed_ids:
                  type: array
                  items:
                    type: string
```

### Privacy-Focused Features

#### Privacy Settings per Contact

**Description:**
Allow users to set different privacy levels for each contact, controlling what information is shared.

**Required Backend Changes:**
- Storage of privacy settings per contact
- Logic to enforce privacy settings when sharing information
- API endpoints for managing privacy settings

**Database Changes:**
```sql
CREATE TABLE contact_privacy_settings (
    contact_id UUID PRIMARY KEY REFERENCES contacts(id) ON DELETE CASCADE,
    share_status BOOLEAN NOT NULL DEFAULT TRUE,
    share_profile_picture BOOLEAN NOT NULL DEFAULT TRUE,
    share_last_seen BOOLEAN NOT NULL DEFAULT TRUE,
    share_read_receipts BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**API Specification Update:**
```yaml
/contacts/{contact_id}/privacy:
  get:
    summary: Get privacy settings for a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Privacy settings retrieved successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactPrivacySettings'
  put:
    summary: Update privacy settings for a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ContactPrivacySettings'
    responses:
      200:
        description: Privacy settings updated successfully
```

#### Temporary Contact Access

**Description:**
Allow users to grant temporary access to their profile and communication channels to a contact, with automatic expiration.

**Required Backend Changes:**
- Time-limited access token generation and validation
- Scheduled tasks for expiring access
- API endpoints for managing temporary access

**Database Changes:**
```sql
CREATE TABLE temporary_contact_access (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    access_level VARCHAR(20) NOT NULL DEFAULT 'basic',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, contact_id)
);

CREATE INDEX idx_temporary_contact_access_expiry ON temporary_contact_access(expires_at);
```

**API Specification Update:**
```yaml
/contacts/{contact_id}/temporary-access:
  post:
    summary: Grant temporary access to a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              duration_hours:
                type: integer
                minimum: 1
                maximum: 720
              access_level:
                type: string
                enum: [basic, extended, full]
    responses:
      201:
        description: Temporary access granted successfully
  get:
    summary: Get temporary access status for a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Temporary access status retrieved
  delete:
    summary: Revoke temporary access for a contact
    security:
      - BearerAuth: []
    parameters:
      - name: contact_id
        in: path
        required: true
        schema:
          type: string
    responses:
      204:
        description: Temporary access revoked successfully
```

## Backend Implementation Details

### Database Schema Changes

The following tables would need to be added or modified to support the planned features:

1. **User Verification**
   ```sql
   ALTER TABLE users ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT FALSE;
   ALTER TABLE users ADD COLUMN verification_date TIMESTAMP WITH TIME ZONE;
   ```

2. **Contact Tags**
   ```sql
   CREATE TABLE contact_tags (
       id UUID PRIMARY KEY,
       user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
       name VARCHAR(50) NOT NULL,
       color VARCHAR(7) NOT NULL DEFAULT '#CCCCCC',
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       UNIQUE(user_id, name)
   );

   CREATE TABLE contact_tag_assignments (
       contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
       tag_id UUID NOT NULL REFERENCES contact_tags(id) ON DELETE CASCADE,
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       PRIMARY KEY (contact_id, tag_id)
   );
   ```

3. **Contact Privacy Settings**
   ```sql
   CREATE TABLE contact_privacy_settings (
       contact_id UUID PRIMARY KEY REFERENCES contacts(id) ON DELETE CASCADE,
       share_status BOOLEAN NOT NULL DEFAULT TRUE,
       share_profile_picture BOOLEAN NOT NULL DEFAULT TRUE,
       share_last_seen BOOLEAN NOT NULL DEFAULT TRUE,
       share_read_receipts BOOLEAN NOT NULL DEFAULT TRUE,
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
   );
   ```

4. **Temporary Contact Access**
   ```sql
   CREATE TABLE temporary_contact_access (
       id UUID PRIMARY KEY,
       user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
       contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
       expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
       access_level VARCHAR(20) NOT NULL DEFAULT 'basic',
       created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
       UNIQUE(user_id, contact_id)
   );

   CREATE INDEX idx_temporary_contact_access_expiry ON temporary_contact_access(expires_at);
   ```

### API Specification Updates

The OpenAPI specification would need to be updated with the following components:

1. **New Schemas**
   ```yaml
   components:
     schemas:
       ContactTag:
         type: object
         properties:
           id:
             type: string
           user_id:
             type: string
           name:
             type: string
           color:
             type: string
           created_at:
             type: string
             format: date-time
           updated_at:
             type: string
             format: date-time

       ContactPrivacySettings:
         type: object
         properties:
           contact_id:
             type: string
           share_status:
             type: boolean
           share_profile_picture:
             type: boolean
           share_last_seen:
             type: boolean
           share_read_receipts:
             type: boolean
           created_at:
             type: string
             format: date-time
           updated_at:
             type: string
             format: date-time

       TemporaryContactAccess:
         type: object
         properties:
           id:
             type: string
           user_id:
             type: string
           contact_id:
             type: string
           expires_at:
             type: string
             format: date-time
           access_level:
             type: string
             enum: [basic, extended, full]
           created_at:
             type: string
             format: date-time
   ```

2. **New Endpoints**
   - `/users/qr-code` - Generate QR code for Meena ID
   - `/users/verify` - Request verification for Meena ID
   - `/users/{user_id}/verification-status` - Check verification status
   - `/contacts/tags` - CRUD operations for tags
   - `/contacts/{contact_id}/tags` - Manage tags for a contact
   - `/contacts/batch` - Perform batch operations on contacts
   - `/contacts/{contact_id}/privacy` - Manage privacy settings for a contact
   - `/contacts/{contact_id}/temporary-access` - Manage temporary access for a contact

## Implementation Priorities

Based on user value and implementation complexity, here's a suggested priority order for the planned features:

1. **QR Code Generation for Sharing Meena ID**
   - High user value, relatively low implementation complexity
   - Enhances the core anonymous authentication model

2. **Contact Tagging System**
   - Medium-high user value, medium implementation complexity
   - Improves contact organization and filtering

3. **Batch Operations**
   - Medium user value, medium implementation complexity
   - Improves efficiency for power users

4. **Privacy Settings per Contact**
   - High user value, medium-high implementation complexity
   - Addresses privacy concerns and enhances security

5. **Temporary Contact Access**
   - Medium user value, high implementation complexity
   - Advanced feature for specific use cases

6. **Meena ID Verification System**
   - Medium-high user value, high implementation complexity
   - Enhances trust but requires careful implementation

Each feature should be implemented incrementally, with frontend and backend components developed in parallel where possible. User testing should be conducted after each feature implementation to gather feedback and make adjustments as needed.
