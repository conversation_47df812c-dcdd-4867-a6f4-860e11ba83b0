# ViewModel Implementation Guide

This guide provides detailed instructions for implementing ViewModels and UI components using our new architecture approach.

## ViewModel Implementation

### 1. Create a UI State Class

First, create a data class that implements the `UiState` interface. This class will represent the state of the UI that the ViewModel manages.

```kotlin
data class ContactsState(
    // Base state properties (composition)
    val properties: UiStateProperties = UiStateProperties(),

    // Operation states
    val fetchContactsOperation: OperationState = OperationState(),
    val addContactOperation: OperationState = OperationState(),
    val updateContactOperation: OperationState = OperationState(),
    val deleteContactOperation: OperationState = OperationState(),
    val blockContactOperation: OperationState = OperationState(),
    val unblockContactOperation: OperationState = OperationState(),

    // Data
    val contacts: List<Contact> = emptyList(),
    val selectedContact: Contact? = null,
    val isContactAdded: Boolean = false,
    val isContactUpdated: Boolean = false,
    val isContactDeleted: Boolean = false
) : UiState {
    // Delegate to properties
    override val isLoading: Boolean get() = properties.isLoading
    override val error: String? get() = properties.error
}
```

### 2. Create Extension Functions for the UI State Class

Create extension functions for the UI state class to check if any operation is in progress and to reset all operation states.

```kotlin
/**
 * Extension function to check if any operation is in progress.
 */
fun ContactsState.isAnyOperationInProgress(): Boolean {
    return fetchContactsOperation.isInProgress ||
            addContactOperation.isInProgress ||
            updateContactOperation.isInProgress ||
            deleteContactOperation.isInProgress ||
            blockContactOperation.isInProgress ||
            unblockContactOperation.isInProgress
}

/**
 * Extension function to reset all operation states.
 */
fun ContactsState.resetAllOperations(): ContactsState {
    return copy(
        properties = properties.copy(error = null),
        fetchContactsOperation = OperationState(),
        addContactOperation = OperationState(),
        updateContactOperation = OperationState(),
        deleteContactOperation = OperationState(),
        blockContactOperation = OperationState(),
        unblockContactOperation = OperationState()
    )
}
```

### 3. Create the ViewModel Class

Create a ViewModel class that extends `BaseViewModel`. This class will manage the UI state and handle user actions.

```kotlin
@HiltViewModel
class ContactsViewModel @Inject constructor(
    private val getContactsUseCase: GetContactsUseCase,
    private val addContactUseCase: AddContactUseCase,
    private val updateContactUseCase: UpdateContactUseCase,
    private val deleteContactUseCase: DeleteContactUseCase,
    private val blockContactUseCase: BlockContactUseCase,
    private val unblockContactUseCase: UnblockContactUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    private val _contactsState = MutableStateFlow(ContactsState())
    val contactsState: StateFlow<ContactsState> = _contactsState.asStateFlow()

    init {
        fetchContacts()
    }

    /**
     * Fetch contacts.
     */
    fun fetchContacts() {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                fetchContactsOperation = it.fetchContactsOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { getContactsUseCase() },
                onSuccess = { contacts ->
                    _contactsState.update { it.copy(
                        fetchContactsOperation = it.fetchContactsOperation.success(),
                        contacts = contacts,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Failed to fetch contacts")
                    _contactsState.update { it.copy(
                        fetchContactsOperation = it.fetchContactsOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Add a contact.
     */
    fun addContact(userHandle: String, displayName: String, notes: String?) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                addContactOperation = it.addContactOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { addContactUseCase(userHandle, displayName, notes) },
                onSuccess = { contact ->
                    _contactsState.update { it.copy(
                        addContactOperation = it.addContactOperation.success(),
                        isContactAdded = true,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                    fetchContacts() // Refresh the contacts list
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Failed to add contact")
                    _contactsState.update { it.copy(
                        addContactOperation = it.addContactOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Update a contact.
     */
    fun updateContact(contactId: String, displayName: String, notes: String?) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                updateContactOperation = it.updateContactOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { updateContactUseCase(contactId, displayName, notes) },
                onSuccess = { contact ->
                    _contactsState.update { it.copy(
                        updateContactOperation = it.updateContactOperation.success(),
                        isContactUpdated = true,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                    fetchContacts() // Refresh the contacts list
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Failed to update contact")
                    _contactsState.update { it.copy(
                        updateContactOperation = it.updateContactOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Block a contact.
     */
    fun blockContact(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                blockContactOperation = it.blockContactOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { blockContactUseCase(contactId) },
                onSuccess = { success ->
                    _contactsState.update { it.copy(
                        blockContactOperation = it.blockContactOperation.success(),
                        isContactBlocked = true,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                    fetchContacts() // Refresh the contacts list
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Failed to block contact")
                    _contactsState.update { it.copy(
                        blockContactOperation = it.blockContactOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Unblock a contact.
     */
    fun unblockContact(contactId: String) {
        launchWithErrorHandling {
            _contactsState.update { it.copy(
                unblockContactOperation = it.unblockContactOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { unblockContactUseCase(contactId) },
                onSuccess = { success ->
                    _contactsState.update { it.copy(
                        unblockContactOperation = it.unblockContactOperation.success(),
                        isContactUnblocked = true,
                        properties = it.properties.copy(isLoading = false)
                    ) }
                    fetchContacts() // Refresh the contacts list
                },
                onError = { error ->
                    val errorMessage = getErrorMessage(error, "Failed to unblock contact")
                    _contactsState.update { it.copy(
                        unblockContactOperation = it.unblockContactOperation.failure(errorMessage),
                        properties = it.properties.copy(isLoading = false, error = errorMessage)
                    ) }
                },
                showLoading = false // We're manually handling loading state
            )
        }
    }

    /**
     * Reset contact operation states.
     */
    fun resetContactOperationStates() {
        _contactsState.update { it.copy(
            isContactAdded = false,
            isContactUpdated = false,
            isContactDeleted = false,
            isContactBlocked = false,
            isContactUnblocked = false,
            addContactOperation = OperationState(),
            updateContactOperation = OperationState(),
            deleteContactOperation = OperationState(),
            blockContactOperation = OperationState(),
            unblockContactOperation = OperationState()
        ) }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _contactsState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
    }

    /**
     * Get error message from a throwable.
     */
    private fun getErrorMessage(throwable: Throwable, fallback: String? = null): String {
        return errorHandler.getErrorMessage(throwable, fallback)
    }
}
```

## UI Component Implementation

### 1. Create a Composable Function for the UI Component

Create a composable function for the UI component that observes the UI state from the ViewModel and renders the UI accordingly.

```kotlin
@Composable
fun ContactListScreen(
    onNavigateToAddContact: () -> Unit,
    onNavigateToContactDetail: (String) -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    // Show operation success/error messages
    LaunchedEffect(contactsState.fetchContactsOperation.error) {
        contactsState.fetchContactsOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Contacts") },
                actions = {
                    IconButton(onClick = onNavigateToAddContact) {
                        Icon(Icons.Default.Add, contentDescription = "Add Contact")
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (contactsState.contacts.isEmpty() && !contactsState.properties.isLoading && !contactsState.fetchContactsOperation.isInProgress) {
                Text(
                    text = "No contacts found",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize()
                ) {
                    items(contactsState.contacts) { contact ->
                        ContactItem(
                            contact = contact,
                            onClick = { onNavigateToContactDetail(contact.id) }
                        )
                    }
                }
            }

            // Show loading indicator
            if (contactsState.properties.isLoading || contactsState.fetchContactsOperation.isInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}
```

### 2. Create Composable Functions for UI Components

Create composable functions for UI components that are used in the main UI component.

```kotlin
@Composable
fun ContactItem(
    contact: Contact,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Avatar
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary)
            ) {
                Text(
                    text = contact.displayName.first().toString(),
                    color = MaterialTheme.colorScheme.onPrimary,
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.align(Alignment.Center)
                )
            }

            // Contact details
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp)
            ) {
                Text(
                    text = contact.displayName,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = contact.userHandle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Blocked indicator
            if (contact.isBlocked) {
                Icon(
                    imageVector = Icons.Default.Block,
                    contentDescription = "Blocked",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}
```

## Best Practices

### 1. Use LaunchedEffect for Side Effects

Use `LaunchedEffect` to perform side effects when the UI state changes. This ensures that side effects are only performed when the state changes, not on every recomposition.

```kotlin
// Show error message
LaunchedEffect(contactsState.error) {
    contactsState.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.clearError()
    }
}

// Show operation success/error messages
LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
    if (contactsState.addContactOperation.isSuccessful) {
        snackbarHostState.showSnackbar("Contact added successfully")
        viewModel.resetContactOperationStates()
    }
}

LaunchedEffect(contactsState.addContactOperation.error) {
    contactsState.addContactOperation.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.resetContactOperationStates()
    }
}
```

### 2. Use Proper Loading State Management

Use the `properties.isLoading` and `operation.isInProgress` properties to show loading indicators and disable buttons.

```kotlin
// Show loading indicator
if (contactsState.properties.isLoading || contactsState.fetchContactsOperation.isInProgress) {
    CircularProgressIndicator(
        modifier = Modifier.align(Alignment.Center)
    )
}

// Disable button when loading
Button(
    onClick = { viewModel.addContact(userHandle, displayName, notes) },
    enabled = !contactsState.properties.isLoading && !contactsState.addContactOperation.isInProgress
) {
    Text("Add Contact")
}
```

### 3. Use Proper Error Handling

Use the `error` property and `operation.error` property to show error messages.

```kotlin
// Show error message
LaunchedEffect(contactsState.error) {
    contactsState.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.clearError()
    }
}

// Show operation error message
LaunchedEffect(contactsState.addContactOperation.error) {
    contactsState.addContactOperation.error?.let {
        snackbarHostState.showSnackbar(it)
        viewModel.resetContactOperationStates()
    }
}
```

### 4. Use Proper Success Handling

Use the `operation.isSuccessful` property to handle successful operations.

```kotlin
// Show success message
LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
    if (contactsState.addContactOperation.isSuccessful) {
        snackbarHostState.showSnackbar("Contact added successfully")
        viewModel.resetContactOperationStates()
    }
}
```

### 5. Use Proper State Reset

Reset operation states after handling success or error.

```kotlin
// Reset operation states
fun resetContactOperationStates() {
    _contactsState.update { it.copy(
        isContactAdded = false,
        isContactUpdated = false,
        isContactDeleted = false,
        isContactBlocked = false,
        isContactUnblocked = false,
        addContactOperation = OperationState(),
        updateContactOperation = OperationState(),
        deleteContactOperation = OperationState(),
        blockContactOperation = OperationState(),
        unblockContactOperation = OperationState()
    ) }
}
```

## Conclusion

By following these guidelines, you can implement ViewModels and UI components that are consistent, maintainable, and easy to understand. The new architecture approach provides a solid foundation for building complex UIs with proper state management and error handling.
