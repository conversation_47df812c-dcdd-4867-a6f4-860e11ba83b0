# Anonymous Sign-in with Meena IDs

This document defines the standard for anonymous sign-in with Meena IDs in the Meena application.

## Overview

Meena supports anonymous sign-in, allowing users to start using the application without providing an email address or phone number. This is achieved through the use of unique 9-character Meena IDs and recovery phrases.

## Meena ID Specification

A Meena ID is a unique identifier for a user in the Meena system:

- **Length**: Exactly 9 characters
- **Character Set**: Alphanumeric (a-z, A-Z, 0-9)
- **Generation**: Server-generated when a user signs up without providing a user handle
- **Uniqueness**: Guaranteed to be unique across all users
- **Immutable**: Cannot be changed after account creation

## Recovery Phrase Specification

A recovery phrase is a sequence of words that can be used to recover an account:

- **Length**: 9 words
- **Word Set**: Common English words from a predefined dictionary
- **Generation**: Server-generated during registration
- **Storage**: Stored as a secure hash in the database
- **Usage**: Used in combination with a recovery PIN to recover an account

## Recovery PIN Specification

A recovery PIN is a numeric code that adds an additional layer of security to account recovery:

- **Length**: 6 digits
- **Character Set**: Numeric (0-9)
- **Generation**: User-defined during account setup
- **Storage**: Stored as a secure hash in the database
- **Usage**: Used in combination with a recovery phrase to recover an account

## Anonymous Sign-in Flow

1. **Registration**:
   - User initiates registration without providing an email or phone number
   - Server generates a unique 9-character Meena ID
   - Server generates a 9-word recovery phrase
   - Server returns the Meena ID and recovery phrase to the client
   - Client displays the recovery phrase to the user and prompts for a recovery PIN
   - Client sends the recovery PIN to the server
   - Server stores the hashed recovery phrase and PIN

2. **Login**:
   - User logs in using their Meena ID and password
   - Server validates the credentials and returns an authentication token

3. **Account Recovery**:
   - User initiates account recovery by providing their Meena ID
   - User enters their recovery phrase and recovery PIN
   - Server validates the recovery phrase and PIN
   - If valid, server allows the user to set a new password

## API Endpoints

### Registration

```
POST /api/v1/auth/register
```

Request:
```json
{
  "password": "required_password"
}
```

Response:
```json
{
  "user": {
    "id": "user_uuid",
    "user_handle": "abc123xyz", // 9-character Meena ID
    "display_name": "abc123xyz",
    "bio": "",
    "avatar_url": "https://via.placeholder.com/150",
    "is_verified": false,
    "is_gold_member": false,
    "last_active": "2023-07-15T12:34:56Z",
    "created_at": "2023-07-15T12:34:56Z",
    "follower_count": 0,
    "following_count": 0
  },
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "expires_in": 86400,
  "recovery_phrase": "apple banana orange grape lemon peach cherry melon kiwi"
}
```

### Account Recovery Request

```
POST /api/v1/auth/recovery/request
```

Request:
```json
{
  "user_handle": "abc123xyz",
  "recovery_phrase_words": ["apple", "banana", "orange", "grape", "lemon", "peach", "cherry", "melon", "kiwi"],
  "recovery_pin": "123456"
}
```

Response:
```json
{
  "recovery_token": "recovery_token_uuid"
}
```

### Account Recovery Confirmation

```
POST /api/v1/auth/recovery/confirm
```

Request:
```json
{
  "recovery_token": "recovery_token_uuid",
  "new_password": "new_password"
}
```

Response:
```json
{
  "message": "Account recovery successful"
}
```

## Security Considerations

1. **Recovery Phrase Storage**:
   - Recovery phrases should never be stored in plain text
   - Use a secure one-way hash function (e.g., bcrypt, Argon2) to store recovery phrases
   - Include a unique salt for each user to prevent rainbow table attacks

2. **Recovery PIN Storage**:
   - Recovery PINs should never be stored in plain text
   - Use a secure one-way hash function to store recovery PINs
   - Include a unique salt for each user to prevent rainbow table attacks

3. **Rate Limiting**:
   - Implement strict rate limiting on recovery endpoints to prevent brute force attacks
   - Consider implementing progressive delays after failed attempts

4. **Recovery Token Expiration**:
   - Recovery tokens should expire after a short period (e.g., 15 minutes)
   - Recovery tokens should be single-use

5. **Client-Side Security**:
   - Encourage users to store their recovery phrase securely offline
   - Warn users about the importance of remembering their recovery PIN
   - Implement secure input methods for recovery phrases and PINs

## Implementation Guidelines

1. **Meena ID Generation**:
   - Use a cryptographically secure random number generator
   - Verify uniqueness before assigning to a user
   - Consider excluding easily confused characters (e.g., 0 vs O, 1 vs l)

2. **Recovery Phrase Generation**:
   - Use a predefined dictionary of common words
   - Ensure sufficient entropy in the selection process
   - Consider using BIP-39 or a similar standard for mnemonic generation

3. **Recovery PIN Validation**:
   - Implement minimum length requirements (e.g., 6 digits)
   - Discourage common patterns (e.g., 123456, 111111)
   - Consider implementing a maximum number of failed attempts before temporary lockout

4. **Error Messages**:
   - Use generic error messages to prevent information leakage
   - Do not indicate whether a user handle exists or not
   - Do not indicate whether a recovery phrase or PIN is partially correct
