Okay, let's generate the Mermaid code for each diagram type, one by one. Remember that these diagrams represent the architecture and flows based on our discussion. Sequence diagrams especially are simplified representations of complex interactions.

---

**1. System Context Diagram (C4 Level 1)**

*   **Purpose:** Shows `MEENA` as a single system interacting with users and key external systems.
*   **Mermaid Code:**

```mermaid
%% Diagram: System Context Diagram (C4 Level 1)
graph TD
    actor User [User (Mobile/Web)]
    actor Admin [Admin User]
    subgraph ExternalSystems [External Systems]
        PaymentGateway[(Payment Gateway<br/>e.g., Stripe)]
        PushService[(Push Notification Service<br/>APNS/FCM)]
        ModerationAI[(AI Moderation Service<br/>e.g., Google Cloud AI)]
        ObjectStorage[(Object Storage<br/>e.g., Cloudflare R2)]
        TURNServer[(STUN/TURN Server<br/>e.g., Coturn)]
    end

    subgraph MyAppSystem [MEENA System]
        style MyAppSystem fill:#lightblue,stroke:#333,stroke-width:2px
        MyAppBoundary(MEENA Social Network<br/><br/>[Backend, Database, Cache, etc.])
    end

    User -- "Uses (HTTPS/WSS)" --> MyAppBoundary
    Admin -- "Manages/Moderates (HTTPS)" --> MyAppBoundary

    MyAppBoundary -- "Processes Payments (API)" --> PaymentGateway
    MyAppBoundary -- "Sends Notifications (API)" --> PushService
    MyAppBoundary -- "Scans Content (API)" --> ModerationAI
    MyAppBoundary -- "Stores/Retrieves Media (S3 API)" --> ObjectStorage
    MyAppBoundary -- "Provides TURN Credentials (API)" --> TURNServer

    %% Styling for clarity
    classDef actor fill:#orange,stroke:#333,stroke-width:2px;
    class User,Admin actor;
```

*   **Explanation:** This diagram places the `MEENA` system at the center, showing the primary actors (Users, Admins) who interact with it and the external dependencies it relies upon for specific functionalities like payments, push notifications, AI moderation, file storage, and call relaying.

---

**2. Container Diagram (C4 Level 2)**

*   **Purpose:** Shows the major deployable units/containers within the `MEENA` system boundary and their high-level interactions.
*   **Mermaid Code:**

```mermaid
%% Diagram: Container Diagram (C4 Level 2)
graph TD
    actor User [User (Mobile/Web)]
    actor Admin [Admin User]

    subgraph MyAppSystemBoundary [MEENA System Boundary]
        style MyAppSystemBoundary fill:none,stroke:#cccccc,stroke-dasharray: 5 5

        ClientApp[(Client Applications<br/>[Mobile: Swift/Kotlin]<br/>[Web: React/Vue/etc])]
        APIService[API & WebSocket Service<br/>[Go, Gin/Echo]<br/>Handles API requests & manages persistent WebSocket connections]
        JobWorker[Background Job Worker<br/>[Go, Asynq]<br/>Processes asynchronous tasks]
        Database[(PostgreSQL Database<br/>[Relational Storage])]
        Cache[(Redis<br/>[Cache, Pub/Sub, Queue Backend])]

        %% Internal Interactions
        ClientApp -- "Makes API Calls (HTTPS)" --> APIService
        ClientApp -- "Connects (WSS)" --> APIService
        APIService -- "Reads/Writes Data (SQL)" --> Database
        APIService -- "Reads/Writes Cache, Pub/Sub (Redis Protocol)" --> Cache
        APIService -- "Enqueues Jobs" --> Cache
        JobWorker -- "Dequeues Jobs" --> Cache
        JobWorker -- "Reads/Writes Data (SQL)" --> Database
        JobWorker -- "Uses Cache" --> Cache
        JobWorker -- "Calls External APIs" --> Internet[(Internet)]
        APIService -- "Calls External APIs" --> Internet

        %% Admin Interface (could be separate or part of main API)
        Admin -- "Accesses Admin Functions (HTTPS)" --> APIService
    end

    subgraph ExternalServices [External Dependencies]
        PaymentGateway[(Payment Gateway)]
        PushService[(Push Notification Service)]
        ModerationAI[(AI Moderation)]
        ObjectStorage[(Object Storage)]
        TURNServer[(STUN/TURN Server)]
    end

    %% External Interactions
    Internet --> PaymentGateway
    Internet --> PushService
    Internet --> ModerationAI
    Internet --> ObjectStorage
    Internet --> TURNServer

    %% Styling
    classDef actor fill:#orange,stroke:#333,stroke-width:2px;
    classDef container fill:#lightblue,stroke:#333,stroke-width:2px;
    classDef database fill:#lightgrey,stroke:#333,stroke-width:2px;
    classDef cache fill:#lightpink,stroke:#333,stroke-width:2px;
    class ClientApp,APIService,JobWorker container;
    class Database database;
    class Cache cache;
    class User,Admin actor;

```

*   **Explanation:** This zooms into the system, showing the client applications, the main Go backend (handling both API and WebSockets), the separate background worker process, the PostgreSQL database, and the Redis instance. It illustrates the primary communication paths between these deployable units and their connection to external dependencies via the internet.

---

**3. Backend Component Diagram (C4 Level 3 for Go Service)**

*   **Purpose:** Details the internal logical modules within the primary Go Backend Service and their dependencies.
*   **Mermaid Code:**

```mermaid
%% Diagram: Backend Component Diagram (C4 Level 3)
graph TD
    subgraph BackendGoService [Backend API/WebSocket Service (Go Application)]
        style BackendGoService fill:none,stroke:#cccccc,stroke-dasharray: 5 5

        %% Core Service Modules
        AuthS(Auth & User Service)
        ProfileS(Profile & Settings Service)
        ContactS(Contact & Relationship Service)
        MsgS(Messaging Service)
        GroupS(Group Service)
        ChannelS(Channel Service)
        StoryS(Story Service)
        CallS(Call Service)
        PaymentS(Payment & Subscription Service)
        ModS(Moderation Service)
        MediaS(Media Service)
        ReportS(Reporting Service) %% Added for handling report submissions
        NotifS(Notification Service)
        WsMgr(WebSocket Manager Component)

        %% Infrastructure Clients / Adapters
        JobQueueClient[Job Queue Client<br/>(Asynq Interface)]
        DBClient[Database Client<br/>(pgx Interface/Wrapper)]
        RedisClient[Redis Client<br/>(go-redis Interface)]
        ExtAPIClient[External API Client<br/>(HTTP Client Wrapper)]

        %% High-Level Interactions (Examples - add more as needed)
        ProfileS --> AuthS; ContactS --> AuthS; MsgS --> AuthS; GroupS --> AuthS; ChannelS --> AuthS; StoryS --> AuthS; CallS --> AuthS; PaymentS --> AuthS; ModS --> AuthS; MediaS --> AuthS; ReportS --> AuthS; WsMgr --> AuthS;

        MsgS --> WsMgr; MsgS --> NotifS; MsgS --> JobQueueClient;
        GroupS --> PaymentS; GroupS --> ModS; GroupS --> WsMgr; GroupS --> NotifS; GroupS --> JobQueueClient;
        ChannelS --> PaymentS; ChannelS --> ModS; ChannelS --> GroupS; ChannelS --> WsMgr; ChannelS --> NotifS; ChannelS --> JobQueueClient;
        StoryS --> MediaS;
        CallS --> WsMgr; CallS --> PaymentS;
        ReportS --> ModS;
        MediaS --> ModS;
        NotifS --> WsMgr;

        %% Infrastructure Client Usage (Examples)
        AuthS --> DBClient; AuthS --> RedisClient;
        ProfileS --> DBClient;
        ContactS --> DBClient;
        MsgS --> DBClient; MsgS --> RedisClient;
        GroupS --> DBClient;
        ChannelS --> DBClient;
        StoryS --> DBClient;
        CallS --> DBClient;
        PaymentS --> DBClient; PaymentS --> ExtAPIClient;
        ModS --> DBClient; ModS --> ExtAPIClient;
        MediaS --> DBClient; MediaS --> ExtAPIClient;
        ReportS --> DBClient;
        NotifS --> ExtAPIClient;
        WsMgr --> RedisClient; %% For PubSub/State

    end

    %% Styling
    classDef module fill:#lightblue,stroke:#333;
    classDef infra fill:#lightgrey,stroke:#666;
    class AuthS,ProfileS,ContactS,MsgS,GroupS,ChannelS,StoryS,CallS,PaymentS,ModS,MediaS,ReportS,NotifS,WsMgr module;
    class JobQueueClient,DBClient,RedisClient,ExtAPIClient infra;
```

*   **Explanation:** This focuses *inside* the main Go backend application container. It shows the logical modules responsible for different features and how they depend on each other (e.g., `GroupService` needs `AuthService` and `PaymentService`). It also shows how these modules interact with abstracted infrastructure clients (for DB, Redis, Jobs, External APIs).

---

**4. Database Schema Diagram (ERD - Simplified)**

*   **Purpose:** Visualize the core database tables and their primary relationships. *Note: Mermaid ERD is limited for complex schemas; a dedicated tool is better for the full details.*
*   **Mermaid Code:**

```mermaid
%% Diagram: Entity Relationship Diagram (ERD - Simplified)
erDiagram
    USERS ||--o{ CONTACTS : "has"
    USERS ||--o{ FOLLOWERS : "follows"
    USERS ||--o{ FOLLOWERS : "is_followed_by"
    USERS ||--o{ PAYMENTS : "makes"
    USERS ||--o{ SUBSCRIPTIONS : "has_active"
    USERS ||--o{ GROUPS : "creates"
    USERS ||--o{ CHANNELS : "creates"
    USERS ||--o{ MESSAGES : "sends"
    USERS ||--o{ STORIES : "creates"
    USERS ||--o{ STORY_VIEWS : "views"
    USERS ||--o{ CALL_LOGS : "initiates"
    USERS ||--o{ CALL_LOGS : "receives"
    USERS ||--o{ MONTHLY_CALL_USAGE : "tracks"
    USERS ||--o{ REPORTS : "submits"
    USERS ||--o{ MEDIA : "uploads"
    USERS ||--o{ USER_TOTP_SECRETS : "has"
    USERS ||--o{ BANNED_ENTITIES : "reviews/bans"
    USERS ||--o{ USER_CONVERSATIONS : "has_entry_for"

    CONTACTS }|--|| USERS : "is_contact_of"
    FOLLOWERS }|--|| USERS : "follows_user"
    FOLLOWERS }|--|| USERS : "followed_by_user"

    GROUPS ||--|{ GROUP_MEMBERS : "has"
    GROUP_MEMBERS }|--|| USERS : "member"
    GROUPS ||--o{ MESSAGES : "contains"
    GROUPS ||--o{ BANNED_ENTITIES : "can_be"
    GROUPS ||--o{ REPORTS : "can_be_reported"
    GROUPS }o--o| CHANNELS : "linked_to"

    CHANNELS ||--|{ CHANNEL_SUBSCRIBERS : "has"
    CHANNEL_SUBSCRIBERS }|--|| USERS : "subscriber"
    CHANNELS ||--o{ MESSAGES : "contains"
    CHANNELS ||--o{ BANNED_ENTITIES : "can_be"
    CHANNELS ||--o{ REPORTS : "can_be_reported"

    CHATS ||--o{ MESSAGES : "contains"
    CHATS ||--|{ CHAT_PARTICIPANTS : "has"
    CHAT_PARTICIPANTS }|--|| USERS : "participant"

    MESSAGES ||--|{ MESSAGE_MEDIA : "attaches"
    MESSAGES ||--o{ MESSAGE_STATUS : "has_status_for"
    MESSAGES }o--o| MESSAGES : "replies_to"
    MESSAGES }o--o| MESSAGES : "forwards"
    MESSAGES ||--o{ REPORTS : "can_be_reported"
    MESSAGES ||--o{ BANNED_ENTITIES : "can_be"

    MESSAGE_STATUS }|--|| USERS : "recipient"

    MEDIA ||--|{ MESSAGE_MEDIA : "attached_to"
    MEDIA ||--o{ REPORTS : "can_be_reported"
    MEDIA ||--o{ BANNED_ENTITIES : "can_be"

    STORIES ||--|{ STORY_ELEMENTS : "has"
    STORIES ||--|{ STORY_VIEWS : "has"
    STORIES }|--|| MEDIA : "uses_base"
    STORIES ||--o{ REPORTS : "can_be_reported"

    STORY_ELEMENTS }o--o| MEDIA : "uses_overlay"

    SUBSCRIPTIONS }|--o| PAYMENTS : "last_payment"

    %% Basic Attributes (Examples - Add more key ones)
    USERS {
        UUID user_id PK
        VARCHAR user_handle UK
        VARCHAR display_name
        VARCHAR password_hash
        VARCHAR recovery_phrase_hash
        subscription_tier_enum subscription_tier
        JSONB settings
    }
    GROUPS {
        UUID group_id PK
        VARCHAR name
        privacy_type_enum privacy_type
        UUID creator_id FK
        moderation_status_enum moderation_status
    }
    MESSAGES {
        UUID message_id PK
        UUID chat_id FK NULL
        UUID group_id FK NULL
        UUID channel_id FK NULL
        UUID sender_id FK
        TEXT text_content NULL
        TIMESTAMP sent_at
        moderation_status_enum moderation_status
    }
    MEDIA {
        UUID media_id PK
        UUID uploader_user_id FK
        VARCHAR file_url
        VARCHAR media_type
        moderation_status_enum moderation_status
    }
    USER_CONVERSATIONS {
        UUID user_id PK, FK
        UUID conversation_id PK
        VARCHAR conversation_type
        TIMESTAMP last_activity_timestamp
        INT unread_count
    }
```

*   **Explanation:** This provides a visual overview of the main tables and how they relate via foreign keys (represented by the connecting lines and relationship markers like `||--|{`, `}o--o|`, etc.). It helps understand the data structure but omits many columns and details for brevity.

---

**V. Dynamic Behavior / Sequence Diagrams (Examples)**

*   **Purpose:** Illustrate step-by-step interactions for key flows.
*   **Note:** These are simplified. Real flows involve more detailed checks and potential branches.

**5. User Registration Sequence Diagram**

```mermaid
%% Diagram: Sequence - User Registration
sequenceDiagram
    participant Client
    participant API_GW as API Gateway
    participant AuthSvc as Auth Service (Go)
    participant DB as PostgreSQL DB

    Client->>+API_GW: POST /auth/register (email?, phone?, pass)
    API_GW->>+AuthSvc: Handle Registration
    AuthSvc->>AuthSvc: Generate unique user_handle (9 chars)
    AuthSvc->>AuthSvc: Generate Secret Recovery Phrase (9 words)
    AuthSvc->>AuthSvc: Hash password
    AuthSvc->>AuthSvc: Hash default/temp Recovery PIN? (Or require later setup)
    AuthSvc->>+DB: INSERT INTO users (handle, hash, phrase_hash, pin_hash...)
    DB-->>-AuthSvc: User Record Created (user_id)
    AuthSvc->>AuthSvc: Generate JWT Tokens (Access, Refresh)
    AuthSvc-->>-API_GW: 201 Created (user_id, handle, tokens, recovery_phrase)
    API_GW-->>-Client: 201 Created (user_id, handle, tokens, recovery_phrase)
    Note right of Client: Client MUST securely store<br/>Recovery Phrase & prompt<br/>for Recovery PIN setup.
```

**6. Send Group Message Sequence Diagram**

```mermaid
%% Diagram: Sequence - Send Group Message
sequenceDiagram
    participant Client
    participant API_GW as API Gateway
    participant MsgSvc as Messaging Service (Go)
    participant AuthSvc as Auth Service (Go)
    participant GroupSvc as Group Service (Go)
    participant WsMgr as WebSocket Manager (Go)
    participant Redis as Redis (PubSub)
    participant JobQueue as Job Queue (Asynq/Redis)
    participant DB as PostgreSQL DB

    Client->>+API_GW: POST /conversations/{group_id}/messages (text)
    API_GW->>+MsgSvc: Handle Send Group Message
    MsgSvc->>AuthSvc: Verify Auth & Get User ID
    AuthSvc-->>MsgSvc: User OK (user_id)
    MsgSvc->>GroupSvc: Check Group Exists & User is Member/Can Post
    GroupSvc->>DB: Query groups, group_members
    DB-->>GroupSvc: Group/Membership OK
    GroupSvc-->>MsgSvc: Permission OK
    MsgSvc->>+DB: INSERT INTO messages (group_id, sender_id, text)
    DB-->>-MsgSvc: Message Saved (message_id)
    MsgSvc->>WsMgr: Notify via PubSub (Group Message, group_id, message_data)
    WsMgr->>Redis: PUBLISH group:{group_id} message_data
    Note over WsMgr,Redis: Other WsMgr instances subscribe,<br/>find connected members via Redis/memory,<br/>push message via WebSocket.
    MsgSvc->>JobQueue: Enqueue Job (UpdateUserConversationsForGroup, group_id, msg_data)
    MsgSvc-->>-API_GW: 201 Created (message_object)
    API_GW-->>-Client: 201 Created (message_object)
```

**7. Create Secret Group Sequence Diagram**

```mermaid
%% Diagram: Sequence - Create Secret Group
sequenceDiagram
    participant Client
    participant API_GW as API Gateway
    participant GroupSvc as Group Service (Go)
    participant AuthSvc as Auth Service (Go)
    participant PaySvc as Payment Service (Go)
    participant PayGW as Payment Gateway
    participant DB as PostgreSQL DB
    participant JobQueue as Job Queue (Asynq/Redis)

    Client->>+API_GW: POST /groups (name, privacy_type='secret')
    API_GW->>+GroupSvc: Handle Create Group
    GroupSvc->>AuthSvc: Verify Auth & Get User ID
    AuthSvc-->>GroupSvc: User OK (user_id)
    GroupSvc->>PaySvc: Initiate Payment for 'create_secret_group' (€12)
    PaySvc->>+PayGW: Create Payment Intent/Checkout Session
    PayGW-->>-PaySvc: Payment Intent ID / Checkout URL
    PaySvc->>+DB: INSERT INTO payments (user_id, amount, status='pending', purpose, ...)
    DB-->>-PaySvc: Payment Record Created (payment_id)
    PaySvc-->>GroupSvc: Requires Payment (payment_id, checkout_url/client_secret)
    GroupSvc-->>-API_GW: 202 Accepted / 402 Payment Required (checkout_details)
    API_GW-->>-Client: 202 Accepted / 402 Payment Required (checkout_details)

    %% ... Later: Client completes payment ...

    PayGW-->>PaySvc: Webhook: Payment Successful (payment_intent_id)
    PaySvc->>+DB: Find payment record by processor_transaction_id
    DB-->>-PaySvc: Payment record found
    PaySvc->>+DB: UPDATE payments SET status='completed' WHERE payment_id = ?
    DB-->>-PaySvc: Payment Updated
    PaySvc->>JobQueue: Enqueue Job (FinalizeSecretGroupCreation, user_id, group_details, payment_id)

    %% ... Background Job Execution ...
    JobQueue-->>BackgroundWorker: Process FinalizeSecretGroupCreation
    BackgroundWorker->>+DB: INSERT INTO groups (creator_id, name, privacy='secret', ...)
    DB-->>-BackgroundWorker: Group Created (group_id)
    BackgroundWorker->>+DB: INSERT INTO group_members (group_id, user_id, role='owner')
    DB-->>-BackgroundWorker: Owner Added
    BackgroundWorker->>+DB: INSERT INTO user_conversations (user_id, group_id, ...)
    DB-->>-BackgroundWorker: User Conversation Entry Added
    Note over BackgroundWorker: Notify user via WebSocket/Push?

```
*(This flow is complex and involves asynchronous steps after payment)*

---

**VI. Deployment Diagram (Conceptual Example)**

*   **Purpose:** Shows a potential deployment topology for scalability and availability.
*   **Mermaid Code:**

```mermaid
%% Diagram: Deployment Diagram (Conceptual)
graph TD
    subgraph Internet
        User[(User)]
        DNS[(DNS)]
    end

    subgraph CloudProviderRegion [Cloud Provider Region (e.g., us-east-1)]
        subgraph AvailabilityZone1 [Availability Zone 1]
            LB1[Load Balancer<br/>(Nginx/Caddy/ALB)]
            subgraph API_Cluster_AZ1 [API/WebSocket Service Cluster]
                API1_1(Go Service Instance 1)
                API1_2(Go Service Instance 2)
            end
            subgraph Worker_Cluster_AZ1 [Job Worker Cluster]
                Worker1_1(Asynq Worker Instance 1)
            end
            DB_Master[(PostgreSQL Master)]
            Redis_AZ1[(Redis Instance/Shard)]
        end

        subgraph AvailabilityZone2 [Availability Zone 2]
            LB2[Load Balancer<br/>(Nginx/Caddy/ALB)]
             subgraph API_Cluster_AZ2 [API/WebSocket Service Cluster]
                API2_1(Go Service Instance 3)
                API2_2(Go Service Instance 4)
            end
             subgraph Worker_Cluster_AZ2 [Job Worker Cluster]
                 Worker2_1(Asynq Worker Instance 2)
            end
            DB_Replica[(PostgreSQL Replica)]
            Redis_AZ2[(Redis Instance/Shard)]
        end

        %% Connections
        DNS --> LB1; DNS --> LB2;
        LB1 --> API1_1; LB1 --> API1_2;
        LB2 --> API2_1; LB2 --> API2_2;

        API1_1 --> DB_Master; API1_2 --> DB_Master;
        API2_1 --> DB_Master; API2_2 --> DB_Master; %% Writes go to Master
        API1_1 --> DB_Replica; API1_2 --> DB_Replica; %% Reads can go to Replica
        API2_1 --> DB_Replica; API2_2 --> DB_Replica;

        API1_1 --> Redis_AZ1; API1_2 --> Redis_AZ1;
        API2_1 --> Redis_AZ2; API2_2 --> Redis_AZ2; %% May connect cross-AZ or use clustered Redis

        Worker1_1 --> DB_Master; Worker2_1 --> DB_Master;
        Worker1_1 --> Redis_AZ1; Worker2_1 --> Redis_AZ2;

        DB_Master -- "Replication" --> DB_Replica
        Redis_AZ1 -- "Clustering/Replication?" --> Redis_AZ2 %% Depending on Redis setup

    end

    User --> DNS

    %% External Services
    subgraph External [External Services]
        PaymentGW[(Payment Gateway)]
        PushSvc[(Push Service)]
        AI_Mod[(AI Moderation)]
        ObjStore[(Object Storage)]
    end

    %% Connections to External (Simplified)
    API_Cluster_AZ1 --> External
    API_Cluster_AZ2 --> External
    Worker_Cluster_AZ1 --> External
    Worker_Cluster_AZ2 --> External


```

*   **Explanation:** This conceptual diagram shows deploying the application across two Availability Zones for high availability. Load balancers distribute traffic to multiple instances of the Go API/WebSocket service. Background workers also run in multiple instances. PostgreSQL is set up with a Master/Replica for failover and read scaling. Redis might be clustered or replicated across zones. This illustrates a scalable and resilient setup.

---

This set of diagrams provides a much clearer, layered view of your application's architecture, data structure, and key workflows than a single monolithic diagram. Remember to use dedicated tools for the ERD for best results and refine sequence diagrams as you flesh out specific feature implementations.