# Meena App Architecture

This document describes the architecture of the Meena app, including the key components, their responsibilities, and how they interact with each other.

## Overview

The Meena app follows a clean architecture approach with the following layers:

- **Presentation Layer**: UI components, ViewModels, and UI state management
- **Domain Layer**: Use cases, domain models, and repository interfaces
- **Data Layer**: Repository implementations, data sources, and data models

## Key Components

### Presentation Layer

#### UI Components

UI components are implemented using Jetpack Compose. They observe UI state from ViewModels and dispatch user actions to ViewModels.

Key principles for UI components:
- UI components should be stateless and derive their state from the ViewModel
- UI components should not contain business logic
- UI components should use LaunchedEffect to observe state changes and perform side effects
- UI components should handle UI-related logic only (e.g., animations, transitions)

Example of a UI component observing state from a ViewModel:

```kotlin
@Composable
fun LoginScreen(
    onNavigateToRegister: () -> Unit,
    onNavigateToRecovery: () -> Unit,
    onLoginSuccess: () -> Unit,
    onNavigateTo2FA: () -> Unit,
    viewModel: AuthViewModel = hiltViewModel()
) {
    val authState by viewModel.authState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Show error message
    LaunchedEffect(authState.error) {
        authState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }
    
    // Show operation success/error messages
    LaunchedEffect(authState.loginOperation.isSuccessful) {
        if (authState.loginOperation.isSuccessful) {
            viewModel.resetOperationStates()
        }
    }
...
```

#### ViewModels

ViewModels are responsible for managing UI state and handling user actions. They use use cases to perform business logic and update UI state accordingly.

All ViewModels extend the `BaseViewModel` class, which provides common functionality for error handling and state management.

Key principles for ViewModels:
- ViewModels should expose UI state as immutable StateFlow
- ViewModels should handle user actions and update UI state accordingly
- ViewModels should use use cases to perform business logic
- ViewModels should handle errors and update UI state accordingly
- ViewModels should not contain UI-related logic

Example of a ViewModel:

```kotlin
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    private val registerUseCase: RegisterUseCase,
    private val twoFactorAuthUseCase: TwoFactorAuthUseCase,
    private val recoverAccountUseCase: RecoverAccountUseCase,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    fun login(identifier: String, password: String) {
        launchWithErrorHandling {
            _authState.update { it.copy(
                loginOperation = it.loginOperation.start(),
                properties = it.properties.copy(isLoading = true, error = null)
            ) }
...
```

#### BaseViewModel

The `BaseViewModel` class provides common functionality for all ViewModels, including error handling, loading state management, and coroutine utilities.

Key features of the `BaseViewModel` class:
- Error handling with a centralized `ErrorHandler`
- Loading state management
- Coroutine utilities for launching coroutines with error handling
- Helper methods for executing use cases and handling results

```kotlin
abstract class BaseViewModel(
    protected val errorHandler: ErrorHandler
) : ViewModel() {

    // Application context from the ErrorHandler
    protected val appContext = errorHandler.getApplicationContext()

    // Error state
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    /**
     * Clear the error message.
     */
    open fun clearError() {
        _error.value = null
    }

    /**
     * Set an error message.
     *
     * @param message The error message.
     */
    protected fun setError(message: String) {
        _error.value = message
    }
...
```

#### UI State Management

UI state is managed using immutable data classes that implement the `UiState` interface. Each ViewModel has its own UI state class that represents the state of the UI it manages.

UI state is exposed as a `StateFlow` that UI components can observe.

Operation states (e.g., loading, success, error) are managed using the `OperationState` class, which represents the state of a specific operation (e.g., sending a message, creating a group).

##### UiState Interface

The `UiState` interface provides a common structure for all UI state classes:

```kotlin
interface UiState {
    /**
     * Whether the UI is in a loading state.
     */
    val isLoading: Boolean

    /**
     * Error message to display, if any.
     */
    val error: String?
}
```

##### UiStateProperties

The `UiStateProperties` class provides common properties for all UI state classes:

```kotlin
data class UiStateProperties(
    val isLoading: Boolean = false,
    val error: String? = null
)
```

##### OperationState

The `OperationState` class represents the state of a specific operation:

```kotlin
data class OperationState(
    val isInProgress: Boolean = false,
    val isSuccessful: Boolean = false,
    val error: String? = null
)
```

Extension functions for `OperationState`:

```kotlin
/**
 * Extension function to start an operation.
 *
 * @return A new OperationState with isInProgress set to true.
 */
fun OperationState.start(): OperationState {
    return copy(
        isInProgress = true,
        isSuccessful = false,
        error = null
    )
}

/**
 * Extension function to mark an operation as successful.
 *
 * @return A new OperationState with isInProgress set to false and isSuccessful set to true.
 */
fun OperationState.success(): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = true,
        error = null
    )
}

/**
 * Extension function to mark an operation as failed.
 *
 * @param error The error message.
 * @return A new OperationState with isInProgress set to false and the error message set.
 */
fun OperationState.failure(error: String): OperationState {
    return copy(
        isInProgress = false,
        isSuccessful = false,
        error = error
    )
}

/**
 * Extension function to reset an operation.
 *
 * @return A new OperationState with all fields reset to their default values.
 */
fun OperationState.reset(): OperationState {
    return OperationState()
}
```

Example of a UI state class:

```kotlin
data class AuthState(
    val isLoggedIn: Boolean = false,
    val requires2fa: Boolean = false,
    val userId: String? = null,
    val userHandle: String? = null,
    val registerOperation: OperationState = OperationState(),
    val loginOperation: OperationState = OperationState(),
    val twoFactorAuthOperation: OperationState = OperationState(),
    val recoverAccountOperation: OperationState = OperationState(),
    val properties: UiStateProperties = UiStateProperties()
) : UiState {
    override val isLoading: Boolean
        get() = properties.isLoading
    override val error: String?
        get() = properties.error
}
```

### Domain Layer

#### Use Cases

Use cases represent the business logic of the app. They are implemented as classes that perform a specific action, such as sending a message or creating a group.

Use cases use repository interfaces to interact with data sources.

Key principles for use cases:
- Use cases should be focused on a single responsibility
- Use cases should be independent of the UI
- Use cases should return `Result<T>` to handle success and failure cases
- Use cases should not depend on other use cases (composition over inheritance)

Example of a use case:
```kotlin
class LoginUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(identifier: String, password: String): Result<LoginResponse> {
        return authRepository.login(identifier, password)
    }
}
```

#### Repository Interfaces

Repository interfaces define the contract for data operations. They are implemented by repository classes in the data layer.

Key principles for repository interfaces:
- Repository interfaces should be defined in the domain layer
- Repository interfaces should be independent of the data layer
- Repository interfaces should return `Result<T>` to handle success and failure cases
- Repository interfaces should be focused on a specific domain (e.g., auth, contacts, messages)

Example of a repository interface:

```kotlin
interface AuthRepository {
    suspend fun login(identifier: String, password: String): Result<LoginResponse>
    suspend fun register(userHandle: String?, password: String, email: String?, phoneNumber: String?): Result<RegisterResponse>
    suspend fun twoFactorAuth(code: String): Result<TwoFactorAuthResponse>
    suspend fun recoverAccount(userHandle: String, recoveryPhrase: String, recoveryPin: String, newPassword: String): Result<RecoverAccountResponse>
    suspend fun isLoggedIn(): Result<Boolean>
}
```

### Data Layer

#### Repository Implementations

Repository implementations are responsible for fetching data from data sources and mapping it to domain models.

All repositories implement the `BaseRepository` interface, which provides common functionality for error handling and data operations.

Key principles for repository implementations:
- Repository implementations should implement repository interfaces from the domain layer
- Repository implementations should be responsible for data operations
- Repository implementations should handle errors and return `Result<T>`
- Repository implementations should map data models to domain models

Example of a repository implementation:

```kotlin
class AuthRepositoryImpl @Inject constructor(
    private val authApi: AuthApi,
    private val authDao: AuthDao,
    private val tokenManager: TokenManager
) : AuthRepository, BaseRepository {

    override suspend fun login(identifier: String, password: String): Result<LoginResponse> {
        return executeNetworkOperation {
            val request = LoginRequest(identifier, password)
            val response = authApi.login(request)
            
            // Save tokens
            tokenManager.saveAccessToken(response.accessToken)
            tokenManager.saveRefreshToken(response.refreshToken)
            
            response
        }
    }
    
    override suspend fun register(userHandle: String?, password: String, email: String?, phoneNumber: String?): Result<RegisterResponse> {
        return executeNetworkOperation {
            val request = RegisterRequest(userHandle, password, email, phoneNumber)
            authApi.register(request)
        }
    }
    
    override suspend fun twoFactorAuth(code: String): Result<TwoFactorAuthResponse> {
        return executeNetworkOperation {
            val request = TwoFactorAuthRequest(code)
            authApi.twoFactorAuth(request)
        }
    }
    
    override suspend fun recoverAccount(userHandle: String, recoveryPhrase: String, recoveryPin: String, newPassword: String): Result<RecoverAccountResponse> {
        return executeNetworkOperation {
            val request = RecoverAccountRequest(userHandle, recoveryPhrase, recoveryPin, newPassword)
            authApi.recoverAccount(request)
        }
    }
    
    override suspend fun isLoggedIn(): Result<Boolean> {
        return executeDatabaseOperation {
            tokenManager.getAccessToken() != null
        }
    }
}
```

#### BaseRepository

The `BaseRepository` interface provides common functionality for error handling and data operations:

```kotlin
interface BaseRepository {
    /**
     * Execute a network operation and return a Result.
     *
     * @param block The network operation to execute.
     * @return A Result containing the result of the operation.
     */
    suspend fun <T> executeNetworkOperation(block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Execute a database operation and return a Result.
     *
     * @param block The database operation to execute.
     * @return A Result containing the result of the operation.
     */
    suspend fun <T> executeDatabaseOperation(block: suspend () -> T): Result<T> {
        return try {
            Result.success(block())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

#### Data Sources

Data sources are responsible for fetching data from a specific source, such as a local database or a remote API.

Key principles for data sources:
- Data sources should be focused on a specific data source (e.g., API, database, file system)
- Data sources should be independent of the domain layer
- Data sources should return data models, not domain models
- Data sources should handle errors specific to the data source

Example of a data source:

```kotlin
interface AuthApi {
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): LoginResponse
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): RegisterResponse
    
    @POST("auth/2fa")
    suspend fun twoFactorAuth(@Body request: TwoFactorAuthRequest): TwoFactorAuthResponse
    
    @POST("auth/recover")
    suspend fun recoverAccount(@Body request: RecoverAccountRequest): RecoverAccountResponse
}
```

#### Data Models

Data models represent the data as it is stored in data sources. They are mapped to domain models by repositories.

Key principles for data models:
- Data models should be focused on a specific data source
- Data models should be independent of the domain layer
- Data models should be serializable (e.g., for API requests/responses)
- Data models should be mapped to domain models by repositories

Example of a data model:

```kotlin
data class LoginRequest(
    val identifier: String,
    val password: String
)

data class LoginResponse(
    val userId: String,
    val userHandle: String,
    val accessToken: String,
    val refreshToken: String,
    val requires2fa: Boolean
)
```

### Error Handling

Error handling is centralized in the `ErrorHandler` class, which provides methods for handling errors consistently across the app.

Key principles for error handling:
- Errors should be handled at the appropriate level
- Errors should be propagated up the call stack using `Result<T>`
- Errors should be displayed to the user in a user-friendly way
- Errors should be logged for debugging purposes

Example of the `ErrorHandler` class:

```kotlin
class ErrorHandler @Inject constructor(
    private val context: Context
) {
    /**
     * Get the application context.
     *
     * @return The application context.
     */
    fun getApplicationContext(): Context {
        return context
    }

    /**
     * Get a user-friendly error message from a throwable.
     *
     * @param throwable The throwable.
     * @param fallbackMessage Optional fallback message if the throwable doesn't have a message.
     * @return A user-friendly error message.
     */
    fun getErrorMessage(throwable: Throwable, fallbackMessage: String? = null): String {
        return when (throwable) {
            is IOException -> context.getString(R.string.error_network)
            is HttpException -> {
                when (throwable.code()) {
                    401 -> context.getString(R.string.error_unauthorized)
                    403 -> context.getString(R.string.error_forbidden)
                    404 -> context.getString(R.string.error_not_found)
                    else -> throwable.message() ?: fallbackMessage ?: context.getString(R.string.error_unknown)
                }
            }
            else -> throwable.message ?: fallbackMessage ?: context.getString(R.string.error_unknown)
        }
    }
}
```

## Guidelines for Future Development

### Adding a New Feature

1. Define the UI state for the feature
   - Create a data class that implements the `UiState` interface
   - Include `UiStateProperties` for common properties
   - Include `OperationState` for each operation
   - Include any feature-specific properties

2. Create a ViewModel for the feature that extends `BaseViewModel`
   - Inject the necessary use cases and the `ErrorHandler`
   - Define methods for handling user actions
   - Update the UI state using the `OperationState` extension functions
   - Handle errors using the `ErrorHandler`

3. Create use cases for the feature
   - Define a class for each use case
   - Inject the necessary repositories
   - Implement the `invoke` operator function
   - Return `Result<T>` to handle success and failure cases

4. Update repository interfaces and implementations as needed
   - Define methods in the repository interface
   - Implement the methods in the repository implementation
   - Use the `executeNetworkOperation`, `executeDatabaseOperation`, and `executeFileOperation` methods to handle errors

5. Implement the UI components using Jetpack Compose
   - Observe the UI state using `collectAsState`
   - Use `LaunchedEffect` to handle side effects
   - Dispatch user actions to the ViewModel
   - Handle loading and error states

### Error Handling

1. Use the `Result<T>` class to represent success and failure cases
2. Use the `ErrorHandler` class to handle errors consistently
3. Use the `executeNetworkOperation`, `executeDatabaseOperation`, and `executeFileOperation` methods to handle errors in repositories
4. Use the `executeUseCase` method to handle errors in ViewModels
5. Use the `OperationState` class to represent the state of specific operations

### State Management

1. Use the `UiState` interface for UI state classes
2. Use the `UiStateProperties` class for common UI state properties
3. Use the `OperationState` class for representing the state of specific operations
4. Use the `start()`, `success()`, `failure()`, and `reset()` extension functions to update operation states

### Testing

1. Use unit tests to test use cases, repositories, and ViewModels
2. Use integration tests to test the interaction between components
3. Use UI tests to test the UI components
4. Use mock objects to isolate the component being tested
5. Use the `TestCoroutineDispatcher` to test coroutines

## Conclusion

This architecture provides a solid foundation for building a maintainable, testable, and scalable app. By following the guidelines outlined in this document, you can ensure that your code is consistent, easy to understand, and easy to modify.
