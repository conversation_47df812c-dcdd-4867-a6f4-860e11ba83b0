# Meena Database Guide

This document provides a comprehensive guide to the Meena database, including its structure, management, and usage.

## Database Schema

The single source of truth for the database schema is the `master_schema.sql` file in this directory. This file contains the complete SQL definition of all tables, constraints, indexes, and other database objects.

## Database Initialization

To initialize or reset the database, use the `scripts/initialize_database_fixed.sql` script. This script:

1. Drops all existing tables and enum types
2. Creates all tables with the correct structure and relationships
3. Adds all necessary indexes
4. Creates functions for the contacts API

## Key Design Decisions

### Boolean `is_verified` Field

We use a simple boolean `is_verified` field in the users table instead of an enum for verification status. This decision was made to:

1. Simplify the database schema
2. Match the backend code's expectations
3. Improve maintainability

If more complex verification statuses are needed in the future, this can be revisited.

### Table Dependencies

Tables are created in a specific order to respect dependencies. For example:

1. Users table is created first
2. Groups table is created before Chats
3. Messages table is created before Message Media and Message Reactions
4. Media table is created before Story Elements and Message Media

### Database Functions

The database includes functions for various operations. These functions provide a single source of truth for database operations and ensure consistent behavior across the application.

#### Contact Query Functions

The following functions are used for querying contacts:

1. `get_contacts(user_id, limit, offset)` - Gets all contacts for a user
2. `get_favorite_contacts(user_id, limit, offset)` - Gets favorite contacts for a user
3. `get_contact_by_id(user_id, contact_id)` - Gets a specific contact by ID

These functions are used by the contacts API and are included in the initialization script.

#### Future Functions

We plan to implement more database functions in the future. See the [TODO list](./TODO.md) for details.

## Schema Management

For detailed information on how to manage the database schema, see the `SCHEMA_MANAGEMENT.md` file in this directory.

## Related Documentation

- `data_dictionary.md` - Detailed information about tables and columns
- `erd.md` - Entity-Relationship Diagram
- `api_database_mapping.md` - Mapping between API endpoints and database tables
- `migration_strategy.md` - Strategy for database migrations

## Troubleshooting

If you encounter issues with the database:

1. Check the PostgreSQL logs for error messages
2. Verify that all tables were created successfully
3. Run the initialization script to reset the database if needed
4. Test the affected functionality in the application

## Making Changes

When making changes to the database schema:

1. Update the master schema file (`master_schema.sql`)
2. Update the initialization script (`scripts/initialize_database_fixed.sql`)
3. Create a migration script if needed for existing databases
4. Update the documentation (data dictionary, ERD, etc.)
5. Test the changes thoroughly
