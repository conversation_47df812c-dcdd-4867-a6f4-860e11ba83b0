# Database TODO List

This document outlines planned improvements and tasks related to the database schema and functions.

## Database Functions

### Existing Functions

The following database functions have been implemented:

1. `get_contacts(user_id, limit, offset)` - Gets all contacts for a user
2. `get_favorite_contacts(user_id, limit, offset)` - Gets favorite contacts for a user
3. `get_contact_by_id(user_id, contact_id)` - Gets a specific contact by ID

### Planned Functions

The following database functions should be implemented in the future:

1. **Contact Management**
   - `add_contact(user_id, contact_id, display_name, relationship)` - Adds a new contact
   - `update_contact(user_id, contact_id, display_name, relationship, is_favorite)` - Updates a contact
   - `remove_contact(user_id, contact_id)` - Removes a contact
   - `block_contact(user_id, contact_id)` - Blocks a contact
   - `unblock_contact(user_id, contact_id)` - Unblocks a contact
   - `get_blocked_contacts(user_id, limit, offset)` - Gets blocked contacts

2. **Contact Groups**
   - `create_contact_group(user_id, name, description)` - Creates a new contact group
   - `update_contact_group(group_id, name, description)` - Updates a contact group
   - `delete_contact_group(group_id)` - Deletes a contact group
   - `add_contact_to_group(group_id, contact_id)` - Adds a contact to a group
   - `remove_contact_from_group(group_id, contact_id)` - Removes a contact from a group
   - `get_contact_groups(user_id, limit, offset)` - Gets all contact groups for a user
   - `get_contacts_in_group(group_id, limit, offset)` - Gets all contacts in a group

3. **User Management**
   - `get_user_by_id(user_id)` - Gets a user by ID
   - `get_user_by_handle(user_handle)` - Gets a user by handle
   - `update_user_profile(user_id, display_name, bio, avatar_url)` - Updates a user's profile
   - `update_user_settings(user_id, settings_json)` - Updates a user's settings

4. **Messaging**
   - `get_conversations(user_id, limit, offset)` - Gets all conversations for a user
   - `get_messages(conversation_id, limit, offset)` - Gets messages in a conversation
   - `send_message(conversation_id, sender_id, content, reply_to_message_id)` - Sends a message
   - `delete_message(message_id)` - Deletes a message
   - `edit_message(message_id, content)` - Edits a message

## Benefits of Database Functions

Implementing these database functions will provide several benefits:

1. **Improved Maintainability**: All database operations will be defined in one place, making them easier to maintain and update.

2. **Consistent Behavior**: All operations will use the same logic, ensuring consistent behavior across the application.

3. **Better Performance**: Database functions can be optimized at the database level, potentially improving performance.

4. **Simplified Backend Code**: Backend code will be simpler and more focused on business logic rather than database operations.

5. **Easier Testing**: Database functions can be tested independently of the backend code.

## Implementation Plan

1. **Phase 1**: Implement contact management functions (Q3 2025)
2. **Phase 2**: Implement contact group functions (Q4 2025)
3. **Phase 3**: Implement user management functions (Q1 2026)
4. **Phase 4**: Implement messaging functions (Q2 2026)

## Related Documentation

- [Master Schema](./master_schema.sql) - The single source of truth for the database schema
- [Schema Management](./SCHEMA_MANAGEMENT.md) - Guidelines for managing the database schema
- [API-Database Mapping](./api_database_mapping.md) - How APIs map to database operations
