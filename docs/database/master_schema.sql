-- Meena Database Master Schema
-- THIS IS THE SINGLE SOURCE OF TRUTH FOR THE DATABASE SCHEMA
-- Last updated: 2025-05-07

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate enum types
CREATE TYPE subscription_tier_enum AS ENUM ('free', 'premium', 'gold');
CREATE TYPE verification_status_enum AS ENUM ('none', 'pending', 'verified', 'rejected');
CREATE TYPE privacy_type_enum AS ENUM ('public', 'private', 'secret');
CREATE TYPE moderation_status_enum AS ENUM ('ok', 'pending_review', 'flagged', 'banned');

-- Users table
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_handle VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone_number <PERSON><PERSON><PERSON><PERSON>(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    recovery_phrase_hash VARCHAR(255) NOT NULL,
    recovery_pin_hash VARCHAR(255), -- Changed from recovery_pin and increased length for hash
    display_name VARCHAR(100),
    profile_picture_url VARCHAR(255),
    bio TEXT,
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    verification_status verification_status_enum NOT NULL DEFAULT 'none',
    requires_2fa BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE,
    scheduled_deletion_at TIMESTAMP WITH TIME ZONE
);

-- User TOTP secrets table (for 2FA)
CREATE TABLE user_totp_secrets (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    secret_key VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    display_name VARCHAR(255) NOT NULL,
    relationship VARCHAR(50) NOT NULL DEFAULT 'friend',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_favorite BOOLEAN NOT NULL DEFAULT FALSE,
    is_blocked BOOLEAN NOT NULL DEFAULT FALSE,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, contact_id)
);

-- Contact groups table
CREATE TABLE contact_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Contact group members table
CREATE TABLE contact_group_members (
    id SERIAL PRIMARY KEY,
    group_id UUID NOT NULL REFERENCES contact_groups(id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, contact_id)
);

-- Followers table
CREATE TABLE followers (
    follower_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    followed_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    followed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (follower_id, followed_id),
    CHECK (follower_id != followed_id)
);

-- Chats table
CREATE TABLE chats (
    chat_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL, -- 'one_to_one', 'group'
    group_id UUID REFERENCES groups(group_id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_message_id UUID,
    last_message_time TIMESTAMP WITH TIME ZONE
);

-- Chat participants table
CREATE TABLE chat_participants (
    chat_id UUID NOT NULL REFERENCES chats(chat_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP WITH TIME ZONE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (chat_id, user_id)
);

-- Groups table
CREATE TABLE groups (
    group_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    profile_picture_url VARCHAR(255),
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    privacy_type privacy_type_enum NOT NULL DEFAULT 'private',
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Group participants table
CREATE TABLE group_participants (
    group_id UUID NOT NULL REFERENCES groups(group_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP WITH TIME ZONE,
    role VARCHAR(20) NOT NULL DEFAULT 'member', -- 'owner', 'admin', 'member'
    PRIMARY KEY (group_id, user_id)
);

-- Messages table
CREATE TABLE messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chat_id UUID NOT NULL REFERENCES chats(chat_id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_edited BOOLEAN NOT NULL DEFAULT FALSE,
    reply_to_message_id UUID REFERENCES messages(message_id) ON DELETE SET NULL,
    moderation_status moderation_status_enum NOT NULL DEFAULT 'ok'
);

-- Media table
CREATE TABLE media (
    media_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    media_type VARCHAR(20) NOT NULL, -- 'image', 'video', 'audio', 'document'
    url VARCHAR(255) NOT NULL,
    thumbnail_url VARCHAR(255),
    file_name VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(100),
    duration INTEGER, -- For audio/video, in seconds
    width INTEGER, -- For images/videos
    height INTEGER, -- For images/videos
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Message media attachments junction table
CREATE TABLE message_media (
    message_id UUID NOT NULL REFERENCES messages(message_id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(media_id) ON DELETE CASCADE,
    PRIMARY KEY (message_id, media_id)
);

-- Message reactions table
CREATE TABLE message_reactions (
    message_id UUID NOT NULL REFERENCES messages(message_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    reaction VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (message_id, user_id, reaction)
);

-- Stories table
CREATE TABLE stories (
    story_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    privacy_type privacy_type_enum NOT NULL DEFAULT 'public'
);

-- Story elements table
CREATE TABLE story_elements (
    element_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES stories(story_id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(media_id) ON DELETE CASCADE,
    caption TEXT,
    position INTEGER NOT NULL, -- Order in the story
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Story views table
CREATE TABLE story_views (
    story_id UUID NOT NULL REFERENCES stories(story_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (story_id, user_id)
);

-- Call logs table
CREATE TABLE call_logs (
    call_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    caller_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    callee_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- In seconds
    call_type VARCHAR(20) NOT NULL, -- 'audio', 'video'
    status VARCHAR(20) NOT NULL, -- 'answered', 'missed', 'declined', 'failed'
    quality_score INTEGER, -- 1-5
    notes TEXT
);

-- User settings table
CREATE TABLE user_settings (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    notification_preferences JSONB NOT NULL DEFAULT '{}',
    privacy_settings JSONB NOT NULL DEFAULT '{}',
    theme_preferences JSONB NOT NULL DEFAULT '{}',
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Subscriptions table
CREATE TABLE subscriptions (
    subscription_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    tier subscription_tier_enum NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    auto_renew BOOLEAN NOT NULL DEFAULT TRUE,
    payment_method VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Payments table
CREATE TABLE payments (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES subscriptions(subscription_id) ON DELETE SET NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- 'pending', 'completed', 'failed', 'refunded'
    transaction_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Support tickets table
CREATE TABLE support_tickets (
    ticket_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    subject VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
    priority VARCHAR(20) NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    category VARCHAR(50) NOT NULL,
    assigned_to UUID REFERENCES users(user_id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Support ticket messages table
CREATE TABLE support_ticket_messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID NOT NULL REFERENCES support_tickets(ticket_id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL, -- 'user', 'support_agent', 'system'
    sender_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    message_body TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Support ticket attachments junction table
CREATE TABLE support_ticket_attachments (
    message_id UUID NOT NULL REFERENCES support_ticket_messages(message_id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(media_id) ON DELETE CASCADE,
    PRIMARY KEY (message_id, media_id)
);

-- Audit logs table
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID,
    old_value JSONB,
    new_value JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    additional_info JSONB
);

-- Recovery tokens table
CREATE TABLE recovery_tokens (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- Renamed from token
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- Refresh tokens table
CREATE TABLE refresh_tokens (
    token_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    issued_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    used_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster token lookups
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token_hash ON refresh_tokens(token_hash);

-- Create indexes for performance
CREATE INDEX idx_users_user_handle ON users(user_handle);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_contacts_user_id ON contacts(user_id);
CREATE INDEX idx_contacts_contact_id ON contacts(contact_id);
CREATE INDEX idx_contacts_is_favorite ON contacts(is_favorite);
CREATE INDEX idx_contacts_last_interaction ON contacts(last_interaction_at);
CREATE INDEX idx_contact_groups_user_id ON contact_groups(user_id);
CREATE INDEX idx_contact_group_members_group_id ON contact_group_members(group_id);
CREATE INDEX idx_contact_group_members_contact_id ON contact_group_members(contact_id);
CREATE INDEX idx_followers_follower_id ON followers(follower_id);
CREATE INDEX idx_followers_followed_id ON followers(followed_id);
CREATE INDEX idx_chat_participants_chat_id ON chat_participants(chat_id);
CREATE INDEX idx_chat_participants_user_id ON chat_participants(user_id);
CREATE INDEX idx_group_participants_group_id ON group_participants(group_id);
CREATE INDEX idx_group_participants_user_id ON group_participants(user_id);
CREATE INDEX idx_messages_chat_id ON messages(chat_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_media_user_id ON media(user_id);
CREATE INDEX idx_stories_user_id ON stories(user_id);
CREATE INDEX idx_stories_expires_at ON stories(expires_at);
CREATE INDEX idx_call_logs_user_id ON call_logs(user_id);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_recovery_tokens_token_hash ON recovery_tokens(token_hash); -- Renamed from idx_recovery_tokens_token and updated column
CREATE INDEX idx_recovery_tokens_user_id ON recovery_tokens(user_id);

-- Create indexes for audit logs
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_entity_type_entity_id ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_action_type ON audit_logs(action_type);

-- Database functions

-- Create a function to get all contacts for a user
CREATE OR REPLACE FUNCTION get_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_blocked = false
    ORDER BY c.is_favorite DESC, c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get favorite contacts for a user
CREATE OR REPLACE FUNCTION get_favorite_contacts(p_user_id UUID, p_limit INTEGER, p_offset INTEGER)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.is_favorite = true AND c.is_blocked = false
    ORDER BY c.last_interaction_at DESC NULLS LAST, c.updated_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get a contact by ID
CREATE OR REPLACE FUNCTION get_contact_by_id(p_user_id UUID, p_contact_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    contact_id UUID,
    display_name VARCHAR,
    relationship VARCHAR,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    contact_user_id UUID,
    contact_user_handle VARCHAR,
    contact_display_name VARCHAR,
    contact_bio TEXT,
    contact_profile_picture_url VARCHAR,
    contact_is_verified BOOLEAN,
    contact_is_gold_member BOOLEAN,
    contact_last_seen_at TIMESTAMP WITH TIME ZONE,
    contact_created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id, c.user_id, c.contact_id, c.display_name, c.relationship, c.notes,
        c.created_at, c.updated_at, c.is_favorite, c.last_interaction_at,
        u.user_id, u.user_handle, u.display_name, u.bio, u.profile_picture_url,
        u.verification_status != 'none' as is_verified, u.subscription_tier = 'gold' as is_gold_member,
        u.last_seen_at, u.created_at
    FROM contacts c
    JOIN users u ON c.contact_id = u.user_id
    WHERE c.user_id = p_user_id AND c.id = p_contact_id;
END;
$$ LANGUAGE plpgsql;
