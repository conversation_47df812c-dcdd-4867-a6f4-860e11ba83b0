# Meena Database Data Dictionary

This document provides detailed descriptions of all tables and columns in the Meena database schema. It serves as a reference for both frontend and backend developers.

## Table of Contents

1. [Users and Authentication](#users-and-authentication)
2. [Messaging](#messaging)
3. [Groups and Channels](#groups-and-channels)
4. [Media and Stories](#media-and-stories)
5. [Calls](#calls)
6. [Payments and Subscriptions](#payments-and-subscriptions)
7. [Support](#support)
8. [Enumerations](#enumerations)

## Users and Authentication

### `users` Table

Stores user account information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `user_id` | UUID | Unique identifier for the user | Primary Key |
| `user_handle` | VARCHAR(50) | Unique username/handle | Unique, Not Null |
| `email` | VARCHAR(255) | User's email address | Unique, Nullable |
| `phone_number` | VARCHAR(20) | User's phone number | Unique, Nullable |
| `password_hash` | VARCHAR(255) | Hashed password | Not Null |
| `recovery_phrase_hash` | VARCHAR(255) | Hashed recovery phrase | Not Null |
| `recovery_pin` | VARCHAR(10) | PIN for account recovery | Nullable |
| `display_name` | VARCHAR(100) | User's display name | Nullable |
| `profile_picture_url` | VARCHAR(255) | URL to profile picture | Nullable |
| `bio` | TEXT | User's biography/description | Nullable |
| `subscription_tier` | subscription_tier_enum | User's subscription level | Not Null, Default: 'free' |
| `verification_status` | verification_status_enum | Account verification status | Not Null, Default: 'none' |
| `requires_2fa` | BOOLEAN | Whether 2FA is required | Not Null, Default: FALSE |
| `is_active` | BOOLEAN | Whether the account is active | Not Null, Default: TRUE |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the account was created | Not Null, Default: CURRENT_TIMESTAMP |
| `last_seen_at` | TIMESTAMP WITH TIME ZONE | When the user was last active | Nullable |
| `scheduled_deletion_at` | TIMESTAMP WITH TIME ZONE | When the account is scheduled for deletion | Nullable |

### `user_settings` Table

Stores user preferences and settings.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `user_id` | UUID | Reference to the user | Primary Key, Foreign Key |
| `theme` | VARCHAR(20) | UI theme preference | Not Null, Default: 'system' |
| `language` | VARCHAR(10) | Preferred language | Not Null, Default: 'en' |
| `notifications` | BOOLEAN | Whether notifications are enabled | Not Null, Default: TRUE |
| `message_preview` | BOOLEAN | Whether to show message previews | Not Null, Default: TRUE |
| `read_receipts` | BOOLEAN | Whether to send read receipts | Not Null, Default: TRUE |
| `typing_indicators` | BOOLEAN | Whether to show typing indicators | Not Null, Default: TRUE |
| `call_permission` | VARCHAR(20) | Who can call the user | Not Null, Default: 'contacts' |
| `message_permission` | VARCHAR(20) | Who can message the user | Not Null, Default: 'contacts' |
| `group_permission` | VARCHAR(20) | Who can add user to groups | Not Null, Default: 'contacts' |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When settings were last updated | Not Null, Default: CURRENT_TIMESTAMP |

### `user_totp_secrets` Table

Stores TOTP secrets for two-factor authentication.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `user_id` | UUID | Reference to the user | Primary Key, Foreign Key |
| `secret_key` | VARCHAR(255) | TOTP secret key | Not Null |
| `is_active` | BOOLEAN | Whether the TOTP is active | Not Null, Default: TRUE |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the TOTP was created | Not Null, Default: CURRENT_TIMESTAMP |

### `contacts` Table

Stores user contacts.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `contact_id` | UUID | Unique identifier for the contact | Primary Key |
| `user_id` | UUID | Reference to the user who owns the contact | Not Null, Foreign Key |
| `contact_user_id` | UUID | Reference to the user who is the contact | Not Null, Foreign Key |
| `display_name` | VARCHAR(100) | Custom display name for the contact | Nullable |
| `notes` | TEXT | Notes about the contact | Nullable |
| `is_blocked` | BOOLEAN | Whether the contact is blocked | Not Null, Default: FALSE |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the contact was added | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the contact was last updated | Nullable |

## Messaging

### `chats` Table

Stores chat conversations.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `chat_id` | UUID | Unique identifier for the chat | Primary Key |
| `type` | VARCHAR(20) | Type of chat ('one_to_one', 'group') | Not Null |
| `group_id` | UUID | Reference to the group (if group chat) | Foreign Key, Nullable |
| `created_by` | UUID | Reference to the user who created the chat | Not Null, Foreign Key |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the chat was created | Not Null, Default: CURRENT_TIMESTAMP |
| `last_message_id` | UUID | Reference to the last message | Nullable |
| `last_message_time` | TIMESTAMP WITH TIME ZONE | When the last message was sent | Nullable |

### `chat_participants` Table

Stores participants in a chat.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `chat_id` | UUID | Reference to the chat | Primary Key (part), Foreign Key |
| `user_id` | UUID | Reference to the user | Primary Key (part), Foreign Key |
| `joined_at` | TIMESTAMP WITH TIME ZONE | When the user joined the chat | Not Null, Default: CURRENT_TIMESTAMP |
| `last_read_message_id` | UUID | Reference to the last message read by the user | Nullable |
| `is_archived` | BOOLEAN | Whether the chat is archived for the user | Not Null, Default: FALSE |
| `is_muted` | BOOLEAN | Whether the chat is muted for the user | Not Null, Default: FALSE |
| `is_pinned` | BOOLEAN | Whether the chat is pinned for the user | Not Null, Default: FALSE |

### `messages` Table

Stores messages in chats.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `message_id` | UUID | Unique identifier for the message | Primary Key |
| `chat_id` | UUID | Reference to the chat | Not Null, Foreign Key |
| `sender_id` | UUID | Reference to the user who sent the message | Not Null, Foreign Key |
| `type` | message_type_enum | Type of message | Not Null |
| `content` | TEXT | Message content | Not Null |
| `reply_to_id` | UUID | Reference to the message being replied to | Foreign Key, Nullable |
| `forwarded_from` | UUID | Reference to the original sender if forwarded | Foreign Key, Nullable |
| `is_edited` | BOOLEAN | Whether the message has been edited | Not Null, Default: FALSE |
| `edited_at` | TIMESTAMP WITH TIME ZONE | When the message was edited | Nullable |
| `is_deleted` | BOOLEAN | Whether the message has been deleted | Not Null, Default: FALSE |
| `deleted_at` | TIMESTAMP WITH TIME ZONE | When the message was deleted | Nullable |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the message was created | Not Null, Default: CURRENT_TIMESTAMP |
| `moderation_status` | moderation_status_enum | Moderation status of the message | Not Null, Default: 'ok' |

### `message_media` Table

Junction table linking messages to media attachments.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `message_id` | UUID | Reference to the message | Primary Key (part), Foreign Key |
| `media_id` | UUID | Reference to the media | Primary Key (part), Foreign Key |

### `message_reactions` Table

Stores reactions to messages.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `message_id` | UUID | Reference to the message | Primary Key (part), Foreign Key |
| `user_id` | UUID | Reference to the user who reacted | Primary Key (part), Foreign Key |
| `reaction` | VARCHAR(20) | The reaction (emoji) | Primary Key (part), Not Null |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the reaction was added | Not Null, Default: CURRENT_TIMESTAMP |

## Groups and Channels

### `groups` Table

Stores group information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `group_id` | UUID | Unique identifier for the group | Primary Key |
| `name` | VARCHAR(100) | Group name | Not Null |
| `description` | TEXT | Group description | Nullable |
| `avatar_url` | VARCHAR(255) | URL to group avatar | Nullable |
| `privacy_type` | privacy_type_enum | Privacy level of the group | Not Null, Default: 'private' |
| `created_by` | UUID | Reference to the user who created the group | Not Null, Foreign Key |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the group was created | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the group was last updated | Nullable |
| `chat_id` | UUID | Reference to the associated chat | Not Null |
| `moderation_status` | moderation_status_enum | Moderation status of the group | Not Null, Default: 'ok' |

### `group_participants` Table

Stores participants in a group.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `group_id` | UUID | Reference to the group | Primary Key (part), Foreign Key |
| `user_id` | UUID | Reference to the user | Primary Key (part), Foreign Key |
| `role` | VARCHAR(20) | Role in the group ('admin', 'member', 'moderator') | Not Null, Default: 'member' |
| `added_by` | UUID | Reference to the user who added this participant | Not Null, Foreign Key |
| `joined_at` | TIMESTAMP WITH TIME ZONE | When the user joined the group | Not Null, Default: CURRENT_TIMESTAMP |

### `channels` Table

Stores channel information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `channel_id` | UUID | Unique identifier for the channel | Primary Key |
| `name` | VARCHAR(100) | Channel name | Not Null |
| `topic` | TEXT | Channel topic/description | Nullable |
| `avatar_url` | VARCHAR(255) | URL to channel avatar | Nullable |
| `type` | VARCHAR(20) | Type of channel ('public', 'private', 'announcement') | Not Null |
| `created_by` | UUID | Reference to the user who created the channel | Not Null, Foreign Key |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the channel was created | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the channel was last updated | Nullable |
| `moderation_status` | moderation_status_enum | Moderation status of the channel | Not Null, Default: 'ok' |

### `channel_subscribers` Table

Stores subscribers to a channel.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `channel_id` | UUID | Reference to the channel | Primary Key (part), Foreign Key |
| `user_id` | UUID | Reference to the user | Primary Key (part), Foreign Key |
| `subscribed_at` | TIMESTAMP WITH TIME ZONE | When the user subscribed | Not Null, Default: CURRENT_TIMESTAMP |
| `is_muted` | BOOLEAN | Whether the channel is muted for the user | Not Null, Default: FALSE |

## Media and Stories

### `media` Table

Stores media files.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `media_id` | UUID | Unique identifier for the media | Primary Key |
| `user_id` | UUID | Reference to the user who uploaded the media | Not Null, Foreign Key |
| `type` | media_type_enum | Type of media | Not Null |
| `url` | VARCHAR(255) | URL to the media file | Not Null |
| `thumbnail_url` | VARCHAR(255) | URL to the thumbnail | Nullable |
| `name` | VARCHAR(255) | Original filename | Not Null |
| `size` | BIGINT | File size in bytes | Not Null |
| `mime_type` | VARCHAR(100) | MIME type of the file | Not Null |
| `width` | INTEGER | Width in pixels (for images/videos) | Nullable |
| `height` | INTEGER | Height in pixels (for images/videos) | Nullable |
| `duration` | INTEGER | Duration in seconds (for audio/video) | Nullable |
| `is_encrypted` | BOOLEAN | Whether the file is encrypted | Not Null, Default: FALSE |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the media was uploaded | Not Null, Default: CURRENT_TIMESTAMP |
| `moderation_status` | moderation_status_enum | Moderation status of the media | Not Null, Default: 'ok' |

### `media_upload_sessions` Table

Stores information about chunked upload sessions.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `upload_id` | UUID | Unique identifier for the upload session | Primary Key |
| `user_id` | UUID | Reference to the user who initiated the upload | Not Null, Foreign Key |
| `file_name` | VARCHAR(255) | Original filename | Not Null |
| `content_type` | VARCHAR(100) | MIME type of the file | Not Null |
| `total_size` | BIGINT | Total size of the file in bytes | Not Null |
| `chunk_size` | INTEGER | Size of each chunk in bytes | Not Null |
| `is_encrypted` | BOOLEAN | Whether the file is encrypted | Not Null, Default: FALSE |
| `status` | VARCHAR(20) | Status of the upload ('in_progress', 'completed', 'failed', 'expired') | Not Null, Default: 'in_progress' |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the upload session was created | Not Null, Default: CURRENT_TIMESTAMP |
| `expires_at` | TIMESTAMP WITH TIME ZONE | When the upload session expires | Not Null |
| `completed_at` | TIMESTAMP WITH TIME ZONE | When the upload was completed | Nullable |
| `media_id` | UUID | Reference to the created media entry | Foreign Key, Nullable |

### `media_upload_chunks` Table

Stores information about uploaded chunks in a chunked upload session.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `upload_id` | UUID | Reference to the upload session | Primary Key (part), Foreign Key |
| `chunk_index` | INTEGER | Index of the chunk (0-based) | Primary Key (part), Not Null |
| `size` | BIGINT | Size of the chunk in bytes | Not Null |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the chunk was uploaded | Not Null, Default: CURRENT_TIMESTAMP |

### `stories` Table

Stores user stories.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `story_id` | UUID | Unique identifier for the story | Primary Key |
| `user_id` | UUID | Reference to the user who created the story | Not Null, Foreign Key |
| `base_media_id` | UUID | Reference to the base media for the story | Not Null, Foreign Key |
| `caption` | TEXT | Story caption | Nullable |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the story was created | Not Null, Default: CURRENT_TIMESTAMP |
| `expires_at` | TIMESTAMP WITH TIME ZONE | When the story expires | Not Null |
| `moderation_status` | moderation_status_enum | Moderation status of the story | Not Null, Default: 'ok' |

### `story_elements` Table

Stores elements added to stories (text, stickers, etc.).

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `element_id` | UUID | Unique identifier for the element | Primary Key |
| `story_id` | UUID | Reference to the story | Not Null, Foreign Key |
| `element_type` | VARCHAR(20) | Type of element | Not Null |
| `position_x` | FLOAT | X position (percentage of width) | Not Null |
| `position_y` | FLOAT | Y position (percentage of height) | Not Null |
| `element_data` | JSONB | Element-specific data | Not Null |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the element was added | Not Null, Default: CURRENT_TIMESTAMP |

### `story_views` Table

Stores views of stories.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `story_id` | UUID | Reference to the story | Primary Key (part), Foreign Key |
| `user_id` | UUID | Reference to the user who viewed the story | Primary Key (part), Foreign Key |
| `viewed_at` | TIMESTAMP WITH TIME ZONE | When the story was viewed | Not Null, Default: CURRENT_TIMESTAMP |

## Calls

### `calls` Table

Stores call information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `call_id` | UUID | Unique identifier for the call | Primary Key |
| `caller_id` | UUID | Reference to the user who initiated the call | Not Null, Foreign Key |
| `callee_id` | UUID | Reference to the user who received the call | Not Null, Foreign Key |
| `type` | call_type_enum | Type of call ('audio', 'video') | Not Null |
| `status` | call_status_enum | Status of the call | Not Null |
| `start_time` | TIMESTAMP WITH TIME ZONE | When the call started | Not Null, Default: CURRENT_TIMESTAMP |
| `end_time` | TIMESTAMP WITH TIME ZONE | When the call ended | Nullable |
| `duration_seconds` | INTEGER | Duration of the call in seconds | Nullable |

### `call_logs` Table

Stores call history for users.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `call_log_id` | UUID | Unique identifier for the call log | Primary Key |
| `user_id` | UUID | Reference to the user whose log this is | Not Null, Foreign Key |
| `other_party_id` | UUID | Reference to the other user in the call | Not Null, Foreign Key |
| `type` | call_type_enum | Type of call ('audio', 'video') | Not Null |
| `status` | call_status_enum | Status of the call | Not Null |
| `start_time` | TIMESTAMP WITH TIME ZONE | When the call started | Not Null, Default: CURRENT_TIMESTAMP |
| `duration_seconds` | INTEGER | Duration of the call in seconds | Nullable |

### `monthly_call_usage` Table

Tracks monthly call usage for non-Gold users.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `user_id` | UUID | Reference to the user | Primary Key (part), Foreign Key |
| `month` | INTEGER | Month (1-12) | Primary Key (part), Not Null |
| `year` | INTEGER | Year | Primary Key (part), Not Null |
| `total_duration_seconds` | INTEGER | Total call duration for the month | Not Null, Default: 0 |

## Payments and Subscriptions

### `subscriptions` Table

Stores user subscriptions.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `subscription_id` | UUID | Unique identifier for the subscription | Primary Key |
| `user_id` | UUID | Reference to the user | Not Null, Foreign Key |
| `tier` | subscription_tier_enum | Subscription tier | Not Null |
| `status` | VARCHAR(20) | Status of the subscription | Not Null |
| `start_date` | TIMESTAMP WITH TIME ZONE | When the subscription started | Not Null, Default: CURRENT_TIMESTAMP |
| `end_date` | TIMESTAMP WITH TIME ZONE | When the subscription ends | Nullable |
| `auto_renew` | BOOLEAN | Whether the subscription auto-renews | Not Null, Default: TRUE |
| `payment_method` | VARCHAR(50) | Payment method used | Nullable |
| `external_subscription_id` | VARCHAR(100) | ID from external payment processor | Nullable |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the subscription was created | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the subscription was last updated | Nullable |

### `payments` Table

Stores payment information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `payment_id` | UUID | Unique identifier for the payment | Primary Key |
| `user_id` | UUID | Reference to the user who made the payment | Not Null, Foreign Key |
| `subscription_id` | UUID | Reference to the subscription (if applicable) | Foreign Key, Nullable |
| `amount` | DECIMAL(10, 2) | Payment amount | Not Null |
| `currency` | VARCHAR(3) | Currency code | Not Null |
| `status` | VARCHAR(20) | Status of the payment | Not Null |
| `purpose` | VARCHAR(50) | Purpose of the payment | Not Null |
| `external_payment_id` | VARCHAR(100) | ID from external payment processor | Nullable |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the payment was created | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the payment was last updated | Nullable |

## Support

### `support_tickets` Table

Stores support tickets.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `ticket_id` | UUID | Unique identifier for the ticket | Primary Key |
| `user_id` | UUID | Reference to the user who created the ticket | Not Null, Foreign Key |
| `subject` | VARCHAR(255) | Ticket subject | Not Null |
| `status` | ticket_status_enum | Status of the ticket | Not Null, Default: 'open' |
| `priority` | ticket_priority_enum | Priority of the ticket | Not Null, Default: 'medium' |
| `category` | VARCHAR(50) | Category of the ticket | Not Null |
| `created_at` | TIMESTAMP WITH TIME ZONE | When the ticket was created | Not Null, Default: CURRENT_TIMESTAMP |
| `updated_at` | TIMESTAMP WITH TIME ZONE | When the ticket was last updated | Not Null, Default: CURRENT_TIMESTAMP |
| `resolved_at` | TIMESTAMP WITH TIME ZONE | When the ticket was resolved | Nullable |

### `support_ticket_messages` Table

Stores messages in support tickets.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `message_id` | UUID | Unique identifier for the message | Primary Key |
| `ticket_id` | UUID | Reference to the ticket | Not Null, Foreign Key |
| `sender_type` | VARCHAR(20) | Type of sender ('user', 'support_agent', 'system') | Not Null |
| `sender_id` | UUID | Reference to the user who sent the message | Foreign Key, Nullable |
| `message_body` | TEXT | Message content | Not Null |
| `sent_at` | TIMESTAMP WITH TIME ZONE | When the message was sent | Not Null, Default: CURRENT_TIMESTAMP |

### `support_ticket_attachments` Table

Junction table linking support ticket messages to media attachments.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| `message_id` | UUID | Reference to the message | Primary Key (part), Foreign Key |
| `media_id` | UUID | Reference to the media | Primary Key (part), Foreign Key |

## Enumerations

### `subscription_tier_enum`

Defines subscription tiers.

- `free`: Free tier
- `gold`: Gold (premium) tier

### `verification_status_enum`

Defines account verification statuses.

- `none`: Not verified
- `email_verified`: Email verified
- `phone_verified`: Phone verified
- `fully_verified`: Both email and phone verified

### `privacy_type_enum`

Defines privacy levels for groups.

- `public`: Visible to everyone
- `private`: Visible only to members
- `secret`: Hidden from search, invitation only

### `moderation_status_enum`

Defines moderation statuses for content.

- `ok`: Content is acceptable
- `flagged`: Content has been flagged for review
- `under_review`: Content is being reviewed
- `rejected`: Content has been rejected

### `message_type_enum`

Defines types of messages.

- `text`: Text message
- `image`: Image message
- `video`: Video message
- `audio`: Audio message
- `file`: File attachment
- `location`: Location share
- `contact`: Contact share
- `system`: System message

### `media_type_enum`

Defines types of media.

- `image`: Image file
- `video`: Video file
- `audio`: Audio file
- `file`: Other file type
- `story_base`: Base media for a story
- `story_overlay`: Overlay media for a story

### `call_type_enum`

Defines types of calls.

- `audio`: Audio call
- `video`: Video call

### `call_status_enum`

Defines statuses for calls.

- `initiated`: Call has been initiated
- `ringing`: Call is ringing
- `in_progress`: Call is in progress
- `completed`: Call completed successfully
- `missed`: Call was missed
- `rejected`: Call was rejected

### `ticket_status_enum`

Defines statuses for support tickets.

- `open`: Ticket is open
- `in_progress`: Ticket is being worked on
- `resolved`: Ticket has been resolved
- `closed`: Ticket is closed

### `ticket_priority_enum`

Defines priorities for support tickets.

- `low`: Low priority
- `medium`: Medium priority
- `high`: High priority
- `urgent`: Urgent priority
