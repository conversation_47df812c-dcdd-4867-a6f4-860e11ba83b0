# WebSocket and Database Interaction

This document explains how real-time features implemented via WebSockets interact with the database in the Meena application.

## Overview

Meena uses WebSockets for real-time communication features alongside traditional REST APIs. While REST APIs handle most CRUD operations, WebSockets enable:

- Instant message delivery
- Presence management (online status)
- Typing indicators
- Read receipts
- Call signaling
- Real-time notifications

This document outlines how these real-time features interact with the database and the architectural patterns used to ensure consistency, performance, and scalability.

## Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Client App │     │  API Server │     │  Database   │
│  (WebSocket)│◄────┤  (Go/Gin)   │◄────┤ (PostgreSQL)│
└─────────────┘     └─────────────┘     └─────────────┘
                          ▲                    ▲
                          │                    │
                          ▼                    │
                    ┌─────────────┐           │
                    │ Redis PubSub│◄───────────┘
                    │ (Real-time) │
                    └─────────────┘
```

- **PostgreSQL**: Persistent storage for all data
- **Redis**: In-memory storage for ephemeral data and pub/sub messaging
- **WebSocket Server**: Handles real-time communication with clients
- **API Server**: Handles REST API requests and database operations

## Presence Management

### Storage Strategy

User presence information (online, away, offline) is stored in Redis rather than PostgreSQL for performance reasons:

```
// Redis key structure
user:{user_id}:presence = { status, last_active, device_info }
```

### Database Impact

- **Write Operations**:
  - Update `users.last_seen_at` when status changes from online to offline
  - No database writes for temporary status changes (online→away)
  - Periodic batch updates (every 5 minutes) to reduce database load

- **Read Operations**:
  - Initial presence data loaded from Redis
  - Fall back to `users.last_seen_at` for users not in Redis cache

### Implementation Details

```go
// When user connects via WebSocket
func handleUserConnect(userId string, deviceInfo string) {
    // Update Redis (immediate)
    redis.HSet("user:"+userId+":presence", "status", "online", "last_active", time.Now().Unix(), "device_info", deviceInfo)

    // Publish to subscribers
    redis.Publish("presence_updates", userId+":online")

    // No immediate database update
}

// When user disconnects
func handleUserDisconnect(userId string) {
    // Update Redis (immediate)
    redis.HSet("user:"+userId+":presence", "status", "offline", "last_active", time.Now().Unix())

    // Publish to subscribers
    redis.Publish("presence_updates", userId+":offline")

    // Update database
    db.Exec("UPDATE users SET last_seen_at = NOW() WHERE user_id = $1", userId)
}

// Periodic batch update (runs every 5 minutes)
func updateLastSeenBatch() {
    // Get all online users from Redis
    onlineUsers := redis.Keys("user:*:presence")

    // Batch update database
    for _, userKey := range onlineUsers {
        userId := extractUserId(userKey)
        lastActive := redis.HGet(userKey, "last_active")

        db.Exec("UPDATE users SET last_seen_at = TO_TIMESTAMP($1) WHERE user_id = $2",
                lastActive, userId)
    }
}
```

## Typing Indicators

### Storage Strategy

Typing status is ephemeral and stored only in Redis with automatic expiration:

```
// Redis key structure
chat:{chat_id}:typing:{user_id} = timestamp
// Key automatically expires after 5 seconds
```

### Database Impact

- **No database operations** - typing indicators never persist to the database
- Completely handled through WebSocket and Redis

### Implementation Details

```go
// When user starts typing
func handleTypingStart(userId string, chatId string) {
    // Set in Redis with 5-second expiration
    redis.Set("chat:"+chatId+":typing:"+userId, time.Now().Unix(), 5*time.Second)

    // Publish to chat participants
    redis.Publish("chat:"+chatId+":updates", "typing:"+userId+":start")
}

// When user stops typing
func handleTypingStop(userId string, chatId string) {
    // Remove from Redis
    redis.Del("chat:"+chatId+":typing:"+userId)

    // Publish to chat participants
    redis.Publish("chat:"+chatId+":updates", "typing:"+userId+":stop")
}

// Get who's typing in a chat
func getTypingUsers(chatId string) []string {
    // Get all typing users from Redis
    return redis.Keys("chat:"+chatId+":typing:*")
}
```

## Message Delivery and Read Receipts

### Flow and Database Interaction

1. **Message Sending**:
   - Client sends message via WebSocket
   - Server inserts into `messages` table with status "sent"
   - Server publishes message to Redis channel for the conversation
   - WebSocket server delivers to online recipients

2. **Delivery Confirmation**:
   - When recipient's client receives message, it sends delivery confirmation
   - Server updates `messages` status to "delivered"
   - No separate database table for delivery status

3. **Read Receipts**:
   - When recipient reads message, client sends read receipt
   - Server updates `chat_participants.last_read_message_id`
   - Server publishes read status to Redis channel
   - WebSocket server notifies sender

### Implementation Details

```go
// When sending a message
func handleNewMessage(userId string, chatId string, content string) (string, error) {
    // Insert into database
    messageId := uuid.New().String()
    _, err := db.Exec(`
        INSERT INTO messages (message_id, chat_id, sender_id, type, content, created_at)
        VALUES ($1, $2, $3, 'text', $4, NOW())
    `, messageId, chatId, userId, content)

    if err != nil {
        return "", err
    }

    // Get message with sender info for broadcasting
    var message Message
    err = db.QueryRow(`
        SELECT m.message_id, m.chat_id, m.sender_id, u.user_handle,
               m.type, m.content, m.created_at
        FROM messages m
        JOIN users u ON m.sender_id = u.user_id
        WHERE m.message_id = $1
    `, messageId).Scan(&message.ID, &message.ChatID, &message.SenderID,
                       &message.SenderHandle, &message.Type,
                       &message.Content, &message.CreatedAt)

    // Publish to Redis for WebSocket delivery
    messageJson, _ := json.Marshal(message)
    redis.Publish("chat:"+chatId+":messages", messageJson)

    return messageId, nil
}

// When marking messages as delivered
func handleMessageDelivered(userId string, messageId string) error {
    // Update in database - we track last delivered message per user/chat
    _, err := db.Exec(`
        INSERT INTO message_delivery_status (message_id, user_id, status, updated_at)
        VALUES ($1, $2, 'delivered', NOW())
        ON CONFLICT (message_id, user_id)
        DO UPDATE SET status = 'delivered', updated_at = NOW()
    `, messageId, userId)

    if err != nil {
        return err
    }

    // Get chat ID for the message
    var chatId string
    err = db.QueryRow("SELECT chat_id FROM messages WHERE message_id = $1", messageId).Scan(&chatId)

    if err != nil {
        return err
    }

    // Publish delivery status
    redis.Publish("chat:"+chatId+":delivery", userId+":"+messageId+":delivered")

    return nil
}

// When marking messages as read
func handleMessageRead(userId string, chatId string, messageId string) error {
    // Update last read message ID in database
    _, err := db.Exec(`
        UPDATE chat_participants
        SET last_read_message_id = $1
        WHERE chat_id = $2 AND user_id = $3
    `, messageId, chatId, userId)

    if err != nil {
        return err
    }

    // Publish read status
    redis.Publish("chat:"+chatId+":read", userId+":"+messageId)

    return nil
}
```

## Real-time Notifications

### Storage and Delivery Strategy

- **Online Users**: Notifications delivered immediately via WebSocket
- **Offline Users**: Notifications stored in database and delivered when user comes online

### Database Interaction

- **Database Triggers**: Create triggers on `messages`, `story_views`, etc. that insert into `notifications` table
- **Notification Delivery**: When user connects, undelivered notifications are fetched and sent

### Implementation Details

```go
// Database trigger example (PostgreSQL)
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert notification for each chat participant except sender
    INSERT INTO notifications (user_id, type, entity_type, entity_id, data, created_at)
    SELECT
        cp.user_id,
        'new_message',
        'message',
        NEW.message_id,
        json_build_object(
            'chat_id', NEW.chat_id,
            'sender_id', NEW.sender_id,
            'content_preview', substring(NEW.content, 1, 100)
        ),
        NOW()
    FROM chat_participants cp
    WHERE cp.chat_id = NEW.chat_id AND cp.user_id != NEW.sender_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER message_notification_trigger
AFTER INSERT ON messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();

// When user connects, send pending notifications
func sendPendingNotifications(userId string, conn *websocket.Conn) {
    // Get undelivered notifications
    rows, err := db.Query(`
        SELECT notification_id, type, entity_type, entity_id, data, created_at
        FROM notifications
        WHERE user_id = $1 AND delivered_at IS NULL
        ORDER BY created_at ASC
    `, userId)

    if err != nil {
        log.Printf("Error fetching notifications: %v", err)
        return
    }

    defer rows.Close()

    // Send each notification
    for rows.Next() {
        var notification Notification
        err := rows.Scan(&notification.ID, &notification.Type,
                         &notification.EntityType, &notification.EntityID,
                         &notification.Data, &notification.CreatedAt)

        if err != nil {
            continue
        }

        // Send via WebSocket
        notificationJson, _ := json.Marshal(notification)
        conn.WriteMessage(websocket.TextMessage, notificationJson)

        // Mark as delivered
        db.Exec(`
            UPDATE notifications
            SET delivered_at = NOW()
            WHERE notification_id = $1
        `, notification.ID)
    }
}
```

## Call Signaling

### Flow and Database Interaction

1. **Call Initiation**:
   - Client sends call request via WebSocket
   - Server creates record in `calls` table with status "initiated"
   - Server sends WebSocket notification to callee
   - WebRTC signaling (offer, answer, ICE candidates) happens via WebSocket

2. **Call Status Updates**:
   - Status changes (ringing, in_progress, completed, missed, rejected) update the `calls` record
   - Status updates are published to Redis and delivered via WebSocket

3. **Call Completion**:
   - Call completion updates `calls` record with end time and duration
   - Creates entries in `call_logs` for both participants
   - Updates `monthly_call_usage` for non-Gold users

### Implementation Details

```go
// When initiating a call
func initiateCall(callerId string, calleeId string, callType string) (string, error) {
    // Insert into database
    callId := uuid.New().String()
    _, err := db.Exec(`
        INSERT INTO calls (call_id, caller_id, callee_id, type, status, start_time)
        VALUES ($1, $2, $3, $4, 'initiated', NOW())
    `, callId, callerId, calleeId, callType)

    if err != nil {
        return "", err
    }

    // Publish to Redis for WebSocket delivery
    callData := map[string]string{
        "call_id": callId,
        "caller_id": callerId,
        "callee_id": calleeId,
        "type": callType,
        "status": "initiated"
    }
    callJson, _ := json.Marshal(callData)

    // Publish to caller and callee channels
    redis.Publish("user:"+callerId+":calls", callJson)
    redis.Publish("user:"+calleeId+":calls", callJson)

    return callId, nil
}

// When updating call status
func updateCallStatus(callId string, status string) error {
    // Update in database
    _, err := db.Exec(`
        UPDATE calls
        SET status = $1,
            end_time = CASE WHEN $1 IN ('completed', 'missed', 'rejected') THEN NOW() ELSE end_time END,
            duration_seconds = CASE WHEN $1 = 'completed' THEN
                                   EXTRACT(EPOCH FROM (NOW() - start_time))::INTEGER
                               ELSE duration_seconds END
        WHERE call_id = $2
    `, status, callId)

    if err != nil {
        return err
    }

    // Get call details
    var call Call
    err = db.QueryRow(`
        SELECT call_id, caller_id, callee_id, type, status,
               start_time, end_time, duration_seconds
        FROM calls
        WHERE call_id = $1
    `, callId).Scan(&call.ID, &call.CallerID, &call.CalleeID,
                    &call.Type, &call.Status, &call.StartTime,
                    &call.EndTime, &call.DurationSeconds)

    if err != nil {
        return err
    }

    // If call completed, create call logs and update usage
    if status == "completed" {
        // Create call logs for both participants
        db.Exec(`
            INSERT INTO call_logs (user_id, other_party_id, type, status, start_time, duration_seconds)
            VALUES ($1, $2, $3, $4, $5, $6)
        `, call.CallerID, call.CalleeID, call.Type, "completed", call.StartTime, call.DurationSeconds)

        db.Exec(`
            INSERT INTO call_logs (user_id, other_party_id, type, status, start_time, duration_seconds)
            VALUES ($1, $2, $3, $4, $5, $6)
        `, call.CalleeID, call.CallerID, call.Type, "completed", call.StartTime, call.DurationSeconds)

        // Update monthly call usage for non-Gold users
        updateCallUsage(call.CallerID, call.DurationSeconds)
        updateCallUsage(call.CalleeID, call.DurationSeconds)
    }

    // Publish status update
    callJson, _ := json.Marshal(call)
    redis.Publish("user:"+call.CallerID+":calls", callJson)
    redis.Publish("user:"+call.CalleeID+":calls", callJson)

    return nil
}

// Update call usage for non-Gold users
func updateCallUsage(userId string, durationSeconds int) {
    // Check if user is Gold
    var isGold bool
    db.QueryRow("SELECT subscription_tier = 'gold' FROM users WHERE user_id = $1", userId).Scan(&isGold)

    if isGold {
        return // Gold users have unlimited calls
    }

    // Get current month and year
    now := time.Now()
    month := now.Month()
    year := now.Year()

    // Update monthly usage
    _, err := db.Exec(`
        INSERT INTO monthly_call_usage (user_id, month, year, total_duration_seconds)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (user_id, month, year)
        DO UPDATE SET total_duration_seconds = monthly_call_usage.total_duration_seconds + $4
    `, userId, month, year, durationSeconds)

    if err != nil {
        log.Printf("Error updating call usage: %v", err)
    }
}
```

## Scaling Considerations

### Multi-Instance WebSocket Servers

When running multiple WebSocket server instances:

1. **Redis Pub/Sub**: All instances subscribe to the same Redis channels
2. **Session Affinity**: Clients maintain connection to the same server instance
3. **Connection State**: Connection state stored in Redis for failover

### Database Load Management

To minimize database load from real-time features:

1. **Batching**: Batch database writes for non-critical updates
2. **Caching**: Cache frequently accessed data in Redis
3. **Write-Behind**: Use write-behind caching for high-frequency updates
4. **Read Replicas**: Direct read-heavy operations to database replicas

## Monitoring and Debugging

For effective monitoring of WebSocket and database interactions:

1. **Connection Metrics**:
   - Active WebSocket connections (per server and total)
   - Connection duration (average, p95, p99)
   - Message throughput (messages per second)
   - Connection error rate
   - Reconnection frequency

2. **Database Metrics**:
   - Query performance for real-time operations (execution time)
   - Write frequency from WebSocket handlers (writes per second)
   - Transaction latency (average, p95, p99)
   - Connection pool utilization
   - Lock contention on high-traffic tables

3. **Correlation IDs**:
   - Each WebSocket message assigned a correlation ID
   - ID propagated through Redis to database operations
   - Enables tracing of real-time operations end-to-end

4. **Error Recovery Metrics**:
   - Database connection failure rate
   - Redis connection failure rate
   - Message delivery retry rate
   - Recovery success rate
   - Failover time

5. **Specific Metrics to Track**:
   - Presence update latency
   - Typing indicator propagation time
   - Message delivery time (end-to-end)
   - Read receipt propagation time
   - Call setup time

## Error Handling and Recovery

To ensure resilience in the WebSocket and database interaction:

1. **Database Connection Failures**:
   ```go
   func executeWithRetry(operation func() error) error {
       maxRetries := 3
       retryDelay := 100 * time.Millisecond

       var err error
       for i := 0; i < maxRetries; i++ {
           err = operation()
           if err == nil {
               return nil
           }

           // Check if error is retryable
           if isRetryableError(err) {
               log.Printf("Retryable error, attempt %d: %v", i+1, err)
               time.Sleep(retryDelay)
               retryDelay *= 2 // Exponential backoff
               continue
           }

           // Non-retryable error
           return err
       }

       return fmt.Errorf("operation failed after %d attempts: %w", maxRetries, err)
   }

   func isRetryableError(err error) bool {
       // Check for connection errors, deadlocks, etc.
       if errors.Is(err, sql.ErrConnDone) || errors.Is(err, sql.ErrTxDone) {
           return true
       }

       // Check for specific PostgreSQL error codes
       if pgErr, ok := err.(*pgconn.PgError); ok {
           switch pgErr.Code {
           case "40001": // serialization_failure
               return true
           case "40P01": // deadlock_detected
               return true
           case "08006": // connection_failure
               return true
           }
       }

       return false
   }
   ```

2. **Redis Connection Failures**:
   ```go
   func publishWithFailover(channel string, message []byte) error {
       primaryErr := redisPrimary.Publish(channel, message).Err()
       if primaryErr == nil {
           return nil
       }

       // Try secondary Redis instance
       log.Printf("Primary Redis publish failed: %v, trying secondary", primaryErr)
       secondaryErr := redisSecondary.Publish(channel, message).Err()
       if secondaryErr == nil {
           // Trigger reconnection to primary in background
           go reconnectToPrimary()
           return nil
       }

       // Both failed, use in-memory queue as last resort
       log.Printf("Secondary Redis publish failed: %v, using in-memory queue", secondaryErr)
       inMemoryQueue.Enqueue(channel, message)

       return fmt.Errorf("redis publish failed on both primary and secondary: %v, %v",
                         primaryErr, secondaryErr)
   }
   ```

3. **Message Delivery Guarantees**:
   ```go
   func sendMessageWithAck(userId string, messageId string, content []byte) {
       // Store message in pending delivery table
       db.Exec(`
           INSERT INTO pending_message_deliveries (message_id, user_id, content, attempts, created_at)
           VALUES ($1, $2, $3, 1, NOW())
       `, messageId, userId, content)

       // Try to deliver via WebSocket
       delivered := tryDeliverMessage(userId, messageId, content)

       if delivered {
           // Mark as delivered
           db.Exec(`
               DELETE FROM pending_message_deliveries
               WHERE message_id = $1 AND user_id = $2
           `, messageId, userId)
       }
   }

   // Background worker that retries pending deliveries
   func retryPendingDeliveries() {
       for {
           rows, err := db.Query(`
               SELECT message_id, user_id, content, attempts
               FROM pending_message_deliveries
               WHERE next_attempt_at <= NOW()
               LIMIT 100
           `)

           if err != nil {
               log.Printf("Error querying pending deliveries: %v", err)
               time.Sleep(5 * time.Second)
               continue
           }

           for rows.Next() {
               var messageId, userId string
               var content []byte
               var attempts int

               rows.Scan(&messageId, &userId, &content, &attempts)

               delivered := tryDeliverMessage(userId, messageId, content)

               if delivered {
                   db.Exec(`
                       DELETE FROM pending_message_deliveries
                       WHERE message_id = $1 AND user_id = $2
                   `, messageId, userId)
               } else if attempts < 5 {
                   // Schedule next retry with exponential backoff
                   backoff := time.Duration(math.Pow(2, float64(attempts))) * time.Second

                   db.Exec(`
                       UPDATE pending_message_deliveries
                       SET attempts = attempts + 1,
                           next_attempt_at = NOW() + $1
                       WHERE message_id = $2 AND user_id = $3
                   `, backoff, messageId, userId)
               } else {
                   // Max attempts reached, move to dead letter queue
                   db.Exec(`
                       INSERT INTO failed_message_deliveries
                       SELECT *, NOW() as failed_at FROM pending_message_deliveries
                       WHERE message_id = $1 AND user_id = $2
                   `, messageId, userId)

                   db.Exec(`
                       DELETE FROM pending_message_deliveries
                       WHERE message_id = $1 AND user_id = $2
                   `, messageId, userId)
               }
           }

           rows.Close()
           time.Sleep(1 * time.Second)
       }
   }
   ```

## Conclusion

This architecture balances real-time responsiveness with database consistency and performance. By using Redis as an intermediary layer for ephemeral data and pub/sub messaging, we minimize database load while maintaining data integrity.

The patterns described here should be followed when implementing any real-time feature to ensure consistency across the application and prevent performance issues as the system scales.
