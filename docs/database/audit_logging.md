# Audit Logging Strategy

This document outlines how audit logging is implemented in the Meena application to track user actions, system events, and security-related operations.

## Purpose

Audit logs serve several critical purposes:

- **Security Monitoring**: Detect and investigate suspicious activities
- **Compliance**: Meet regulatory requirements (GDPR, CCPA, etc.)
- **Debugging**: Troubleshoot issues by tracking state changes
- **User Activity Tracking**: Understand user behavior and patterns
- **Moderation Oversight**: Review content moderation decisions
- **Accountability**: Track administrative actions

## Implementation Architecture

Meena implements a tiered audit logging strategy with three separate tables:

1. **Standard Audit Logs** (`audit_logs`): General user actions
2. **Sensitive Audit Logs** (`sensitive_audit_logs`): Security-critical operations
3. **Payment Audit Logs** (`payment_audit_logs`): Financial transactions

This separation allows for:
- Different retention policies
- Stricter access controls for sensitive logs
- Optimized queries for specific use cases
- Compliance with different regulatory requirements

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  API Server │     │ Audit Logger│     │  Database   │
│  (Go/Gin)   │────►│  Service    │────►│ (PostgreSQL)│
└─────────────┘     └─────────────┘     └─────────────┘
                          ▲
                          │
                          ▼
                    ┌─────────────┐
                    │ Async Queue │
                    │  (Redis)    │
                    └─────────────┘
```

## Database Schema

### Standard Audit Logs

```sql
CREATE TABLE audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'create', 'update', 'delete', 'moderate', etc.
    entity_type VARCHAR(50) NOT NULL, -- 'user', 'message', 'group', etc.
    entity_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Sensitive Audit Logs

```sql
CREATE TABLE sensitive_audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL, -- 'password_change', 'admin_action', 'security_setting', etc.
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    additional_context JSONB, -- Additional security context (2FA used, etc.)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Payment Audit Logs

```sql
CREATE TABLE payment_audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL, -- 'payment', 'refund', 'subscription_change', etc.
    entity_type VARCHAR(50) NOT NULL, -- 'payment', 'subscription', etc.
    entity_id UUID NOT NULL,
    amount DECIMAL(10, 2),
    currency VARCHAR(3),
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(45),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## Audited Actions

The following actions are audited in the Meena application:

### Standard Audit Logs

#### User Management
- User registration
- Login/logout
- Profile updates
- Account settings changes
- Contact management (add, update, delete)
- Blocking/unblocking users

#### Content Management
- Message sending
- Message editing/deletion
- Media uploads
- Story creation/deletion
- Group/channel creation and updates
- Group membership changes

#### Moderation
- Content reporting
- Moderation decisions
- Content removal

### Sensitive Audit Logs

#### Security Operations
- Password changes
- Recovery phrase changes
- Two-factor authentication setup/removal
- Remote wipe actions
- Account deletion requests
- Email/phone verification
- Login from new device/location

#### Administrative Actions
- User bans/suspensions
- Administrative privilege changes
- System setting changes
- Manual account interventions

### Payment Audit Logs

#### Financial Operations
- Subscription purchases
- Subscription cancellations
- One-time payments
- Refunds
- Payment method changes
- Gold membership changes

## Implementation Guidelines

### Logging Mechanism

1. **Database Triggers**:
   - Automatic logging for critical tables
   - Example: Message deletion, group membership changes
   - Advantages: Consistent, cannot be bypassed by application code
   - Disadvantages: Limited context, potential performance impact

2. **Application-Level Logging**:
   - For complex actions spanning multiple tables
   - For actions requiring context not available at database level
   - Example: User registration, login attempts
   - Advantages: Rich context, can include business logic details
   - Disadvantages: Must be implemented consistently across all code paths

3. **Asynchronous Processing**:
   - Non-critical audit logs are queued and processed asynchronously
   - Critical security logs are written synchronously
   - Advantages: Minimal impact on application performance
   - Disadvantages: Potential for data loss if queue processing fails

### Performance Considerations

1. **Database Impact Mitigation**:
   - Use separate database for audit logs
   - Implement table partitioning for audit tables (by date)
   - Create optimized indexes for common query patterns
   - Consider using columnar storage for analytical queries

2. **Batch Processing**:
   - Buffer audit logs in memory and write in batches
   - Use bulk insert operations for better performance
   - Implement backpressure mechanisms to prevent memory exhaustion

3. **Resource Allocation**:
   - Dedicate specific CPU cores/threads to audit log processing
   - Monitor and adjust queue processing rate based on system load
   - Implement circuit breakers to protect core functionality

### Example Performance Optimization

```go
// Efficient batch insertion of audit logs
type AuditLogBatch struct {
    logs        []AuditLog
    maxSize     int
    flushPeriod time.Duration
    mutex       sync.Mutex
    db          *sql.DB
}

func NewAuditLogBatch(db *sql.DB, maxSize int, flushPeriod time.Duration) *AuditLogBatch {
    batch := &AuditLogBatch{
        logs:        make([]AuditLog, 0, maxSize),
        maxSize:     maxSize,
        flushPeriod: flushPeriod,
        db:          db,
    }

    // Start periodic flush
    go func() {
        ticker := time.NewTicker(flushPeriod)
        defer ticker.Stop()

        for range ticker.C {
            batch.Flush()
        }
    }()

    return batch
}

func (b *AuditLogBatch) Add(log AuditLog) {
    b.mutex.Lock()
    defer b.mutex.Unlock()

    b.logs = append(b.logs, log)

    // Flush if batch is full
    if len(b.logs) >= b.maxSize {
        go b.Flush()
    }
}

func (b *AuditLogBatch) Flush() {
    b.mutex.Lock()
    if len(b.logs) == 0 {
        b.mutex.Unlock()
        return
    }

    // Take current batch and reset
    logs := b.logs
    b.logs = make([]AuditLog, 0, b.maxSize)
    b.mutex.Unlock()

    // Prepare batch insert
    tx, err := b.db.Begin()
    if err != nil {
        log.Printf("Error beginning transaction: %v", err)
        return
    }

    stmt, err := tx.Prepare(`
        INSERT INTO audit_logs (
            log_id, user_id, action_type, entity_type, entity_id,
            old_values, new_values, ip_address, user_agent, created_at
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        )
    `)
    if err != nil {
        log.Printf("Error preparing statement: %v", err)
        tx.Rollback()
        return
    }
    defer stmt.Close()

    // Execute batch insert
    for _, log := range logs {
        _, err := stmt.Exec(
            log.ID, log.UserID, log.ActionType, log.EntityType, log.EntityID,
            log.OldValues, log.NewValues, log.IPAddress, log.UserAgent, log.CreatedAt,
        )
        if err != nil {
            log.Printf("Error inserting audit log: %v", err)
            tx.Rollback()
            return
        }
    }

    // Commit transaction
    if err := tx.Commit(); err != nil {
        log.Printf("Error committing transaction: %v", err)
        tx.Rollback()
        return
    }

    log.Printf("Successfully flushed %d audit logs", len(logs))
}
```

### Code Examples

#### Database Trigger Example

```sql
CREATE OR REPLACE FUNCTION log_message_deletion()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        user_id,
        action_type,
        entity_type,
        entity_id,
        old_values,
        new_values
    ) VALUES (
        current_setting('app.current_user_id')::uuid,
        'delete',
        'message',
        OLD.message_id,
        row_to_json(OLD),
        NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER message_deletion_audit
BEFORE DELETE ON messages
FOR EACH ROW
EXECUTE FUNCTION log_message_deletion();
```

#### Application-Level Logging Example

```go
func LogUserLogin(userId string, successful bool, r *http.Request) error {
    // Get IP and user agent
    ipAddress := getClientIP(r)
    userAgent := r.UserAgent()

    // Create audit log entry
    logEntry := map[string]interface{}{
        "user_id":     userId,
        "action_type": "login",
        "entity_type": "user",
        "entity_id":   userId,
        "new_values": map[string]interface{}{
            "successful": successful,
            "login_time": time.Now(),
        },
        "ip_address": ipAddress,
        "user_agent": userAgent,
    }

    // For failed logins, add to sensitive logs
    if !successful {
        return db.Exec(`
            INSERT INTO sensitive_audit_logs (
                user_id, action_type, entity_type, entity_id,
                new_values, ip_address, user_agent
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, userId, "failed_login", "user", userId,
           json.Marshal(logEntry["new_values"]), ipAddress, userAgent)
    }

    // For successful logins, add to standard logs
    return db.Exec(`
        INSERT INTO audit_logs (
            user_id, action_type, entity_type, entity_id,
            new_values, ip_address, user_agent
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, userId, "login", "user", userId,
       json.Marshal(logEntry["new_values"]), ipAddress, userAgent)
}
```

#### Asynchronous Logging Example

```go
func QueueAuditLog(logEntry map[string]interface{}) error {
    // Convert to JSON
    logJSON, err := json.Marshal(logEntry)
    if err != nil {
        return err
    }

    // Push to Redis queue
    return redisClient.LPush("audit_log_queue", logJSON).Err()
}

// Worker process that consumes from the queue
func ProcessAuditLogQueue() {
    for {
        // Pop from Redis queue with timeout
        result, err := redisClient.BRPop(5*time.Second, "audit_log_queue").Result()
        if err != nil {
            if err == redis.Nil {
                // No items in queue, continue
                continue
            }
            log.Printf("Error reading from audit log queue: %v", err)
            time.Sleep(1 * time.Second)
            continue
        }

        // Process the log entry
        var logEntry map[string]interface{}
        if err := json.Unmarshal([]byte(result[1]), &logEntry); err != nil {
            log.Printf("Error unmarshaling audit log: %v", err)
            continue
        }

        // Write to database
        if err := writeAuditLogToDatabase(logEntry); err != nil {
            log.Printf("Error writing audit log to database: %v", err)
            // Requeue the failed log
            QueueAuditLog(logEntry)
        }
    }
}
```

## Data Privacy and Sanitization

To protect user privacy and comply with regulations:

1. **PII Sanitization**:
   - Personally Identifiable Information (PII) is redacted or hashed in logs
   - Example: Email addresses, phone numbers, IP addresses (partial)

2. **Sensitive Data Exclusion**:
   - Passwords, authentication tokens, and other credentials are never logged
   - Message content is truncated or excluded based on privacy settings

3. **Data Minimization**:
   - Only necessary information is logged
   - Full objects are not stored when partial information is sufficient

Example sanitization function:

```go
func sanitizeForAuditLog(data map[string]interface{}) map[string]interface{} {
    result := make(map[string]interface{})

    // Copy data with sanitization
    for key, value := range data {
        switch key {
        case "password", "password_hash", "token", "secret":
            // Skip sensitive fields
            continue
        case "email":
            // Hash or partially redact emails
            if email, ok := value.(string); ok {
                parts := strings.Split(email, "@")
                if len(parts) == 2 {
                    result[key] = parts[0][:2] + "***@" + parts[1]
                }
            }
        case "phone_number":
            // Redact middle digits
            if phone, ok := value.(string); ok && len(phone) > 6 {
                result[key] = phone[:3] + "****" + phone[len(phone)-3:]
            }
        case "ip_address":
            // Redact last octet for IPv4 or equivalent for IPv6
            if ip, ok := value.(string); ok {
                if strings.Contains(ip, ".") {
                    parts := strings.Split(ip, ".")
                    if len(parts) == 4 {
                        result[key] = strings.Join(parts[:3], ".") + ".0"
                    }
                } else {
                    // IPv6 handling
                    result[key] = strings.Split(ip, ":")[0] + ":****"
                }
            }
        default:
            // Copy other fields as-is
            result[key] = value
        }
    }

    return result
}
```

## Retention Policy

Different retention periods are applied based on log type and regulatory requirements:

| Log Type | Retention Period | Justification |
|----------|------------------|---------------|
| Standard Audit Logs | 90 days | Balance between operational needs and storage costs |
| Sensitive Audit Logs | 1 year | Security investigation requirements |
| Payment Audit Logs | 7 years | Financial compliance requirements |

Implementation:

```sql
-- Create a function to purge old audit logs
CREATE OR REPLACE FUNCTION purge_old_audit_logs()
RETURNS void AS $$
BEGIN
    -- Delete standard logs older than 90 days
    DELETE FROM audit_logs
    WHERE created_at < NOW() - INTERVAL '90 days';

    -- Delete sensitive logs older than 1 year
    DELETE FROM sensitive_audit_logs
    WHERE created_at < NOW() - INTERVAL '1 year';

    -- Payment logs are retained for 7 years
    DELETE FROM payment_audit_logs
    WHERE created_at < NOW() - INTERVAL '7 years';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run this function daily
SELECT cron.schedule('0 2 * * *', 'SELECT purge_old_audit_logs()');
```

## Access Control

Access to audit logs is strictly controlled:

| Role | Standard Logs | Sensitive Logs | Payment Logs |
|------|---------------|----------------|--------------|
| Regular User | Own logs only | None | Own logs only |
| Support Staff | Limited access | None | None |
| Moderators | Content-related logs | None | None |
| Security Team | Full access | Full access | None |
| Finance Team | None | None | Full access |
| System Administrators | Full access | Full access | Full access |

Implementation through database roles and row-level security:

```sql
-- Create database roles
CREATE ROLE meena_user;
CREATE ROLE meena_support;
CREATE ROLE meena_moderator;
CREATE ROLE meena_security;
CREATE ROLE meena_finance;
CREATE ROLE meena_admin;

-- Enable row-level security
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE sensitive_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_audit_logs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY user_audit_logs ON audit_logs
    TO meena_user
    USING (user_id = current_setting('app.current_user_id')::uuid);

CREATE POLICY support_audit_logs ON audit_logs
    TO meena_support
    USING (entity_type IN ('message', 'group', 'channel', 'media'));

CREATE POLICY moderator_audit_logs ON audit_logs
    TO meena_moderator
    USING (action_type IN ('moderate', 'report', 'delete', 'flag'));

-- Security team has full access to standard and sensitive logs
CREATE POLICY security_audit_logs ON audit_logs
    TO meena_security
    USING (true);

CREATE POLICY security_sensitive_logs ON sensitive_audit_logs
    TO meena_security
    USING (true);

-- Finance team has access to payment logs
CREATE POLICY finance_payment_logs ON payment_audit_logs
    TO meena_finance
    USING (true);

-- Admin has full access to all logs
CREATE POLICY admin_all_logs ON audit_logs
    TO meena_admin
    USING (true);

CREATE POLICY admin_sensitive_logs ON sensitive_audit_logs
    TO meena_admin
    USING (true);

CREATE POLICY admin_payment_logs ON payment_audit_logs
    TO meena_admin
    USING (true);
```

## Monitoring and Alerting

Audit logs are monitored for security and operational purposes:

1. **Security Alerts**:
   - Multiple failed login attempts
   - Password changes from new locations
   - Administrative actions outside business hours
   - Unusual deletion patterns

2. **Operational Monitoring**:
   - Audit log volume trends
   - Error rates in audit logging
   - Database performance impact

Example alert configuration:

```sql
-- Create a view for failed login attempts
CREATE VIEW recent_failed_logins AS
SELECT user_id, COUNT(*) as attempt_count, MIN(created_at) as first_attempt, MAX(created_at) as last_attempt
FROM sensitive_audit_logs
WHERE action_type = 'failed_login'
  AND created_at > NOW() - INTERVAL '30 minutes'
GROUP BY user_id
HAVING COUNT(*) >= 5;

-- Create a function to check for suspicious activity
CREATE OR REPLACE FUNCTION check_suspicious_activity()
RETURNS TABLE(alert_type text, user_id uuid, details jsonb) AS $$
BEGIN
    -- Check for brute force attempts
    RETURN QUERY
    SELECT 'brute_force_attempt'::text, rfl.user_id,
           jsonb_build_object(
               'attempt_count', rfl.attempt_count,
               'time_span_minutes', EXTRACT(EPOCH FROM (rfl.last_attempt - rfl.first_attempt))/60
           ) as details
    FROM recent_failed_logins rfl;

    -- Check for admin actions outside business hours
    RETURN QUERY
    SELECT 'after_hours_admin_action'::text, sal.user_id,
           jsonb_build_object(
               'action_type', sal.action_type,
               'entity_type', sal.entity_type,
               'entity_id', sal.entity_id,
               'time', sal.created_at
           ) as details
    FROM sensitive_audit_logs sal
    WHERE sal.action_type LIKE 'admin_%'
      AND EXTRACT(HOUR FROM sal.created_at) NOT BETWEEN 9 AND 17
      AND EXTRACT(DOW FROM sal.created_at) BETWEEN 1 AND 5
      AND sal.created_at > NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;
```

## Conclusion

This comprehensive audit logging strategy ensures that Meena can:

1. **Track all significant actions** in the system
2. **Investigate security incidents** effectively
3. **Comply with regulatory requirements** for data retention
4. **Protect user privacy** while maintaining accountability
5. **Support operational troubleshooting** and debugging

The tiered approach balances performance, storage costs, and security needs while providing the flexibility to adapt to changing requirements.
