# API-Database Mapping

This document maps API endpoints to database operations, helping both frontend and backend developers understand how the API interacts with the database.

## Authentication APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/auth/register` | POST | `users`, `user_settings` | INSERT into `users`, INSERT into `user_settings` |
| `/api/v1/auth/login` | POST | `users` | SELECT from `users` |
| `/api/v1/auth/refresh` | POST | N/A | Token validation (no direct DB operation) |
| `/api/v1/auth/logout` | POST | N/A | Token invalidation (no direct DB operation) |
| `/api/v1/auth/request-password-reset` | POST | `users` | SELECT from `users` |
| `/api/v1/auth/reset-password` | POST | `users` | UPDATE `users` (password_hash) |
| `/api/v1/auth/verify-account` | POST | `users` | UPDATE `users` (verification_status) |

## User APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/users/me` | GET | `users` | SELECT from `users` |
| `/api/v1/users/{user_handle}` | GET | `users` | SELECT from `users` |
| `/api/v1/users/me/settings` | GET | `user_settings` | SELECT from `user_settings` |
| `/api/v1/users/me/settings` | PUT | `user_settings` | UPDATE `user_settings` |
| `/api/v1/users/me/delete/request` | POST | `users` | UPDATE `users` (scheduled_deletion_at) |
| `/api/v1/users/me/remote-wipe/setup` | POST | `users` | UPDATE `users` (remote_wipe_pin_hash) |
| `/api/v1/users/me/call-usage` | GET | `monthly_call_usage` | SELECT from `monthly_call_usage` |

## Contact APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/contacts` | GET | `contacts`, `users` | SELECT from `contacts` JOIN `users` |
| `/api/v1/contacts` | POST | `contacts` | INSERT into `contacts` |
| `/api/v1/contacts/{contact_user_handle}` | GET | `contacts`, `users` | SELECT from `contacts` JOIN `users` |
| `/api/v1/contacts/{contact_user_handle}` | PUT | `contacts` | UPDATE `contacts` |
| `/api/v1/contacts/{contact_user_handle}` | DELETE | `contacts` | DELETE from `contacts` |
| `/api/v1/blocks` | GET | `contacts` | SELECT from `contacts` WHERE is_blocked = TRUE |
| `/api/v1/blocks/{blocked_user_handle}` | POST | `contacts` | INSERT/UPDATE `contacts` (is_blocked = TRUE) |
| `/api/v1/blocks/{blocked_user_handle}` | DELETE | `contacts` | UPDATE `contacts` (is_blocked = FALSE) |

## Contact Group APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/contact-groups` | GET | `contact_groups` | SELECT from `contact_groups` |
| `/api/v1/contact-groups` | POST | `contact_groups` | INSERT into `contact_groups` |
| `/api/v1/contact-groups/{group_id}` | GET | `contact_groups` | SELECT from `contact_groups` |
| `/api/v1/contact-groups/{group_id}` | PUT | `contact_groups` | UPDATE `contact_groups` |
| `/api/v1/contact-groups/{group_id}` | DELETE | `contact_groups` | DELETE from `contact_groups` |
| `/api/v1/contact-groups/{group_id}/members/{contact_id}` | POST | `contact_groups` | UPDATE `contact_groups` (add to member_ids) |
| `/api/v1/contact-groups/{group_id}/members/{contact_id}` | DELETE | `contact_groups` | UPDATE `contact_groups` (remove from member_ids) |

## Messaging APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/conversations` | GET | `chats`, `chat_participants`, `messages` | SELECT from `chats` JOIN `chat_participants` LEFT JOIN `messages` |
| `/api/v1/conversations` | POST | `chats`, `chat_participants` | INSERT into `chats`, INSERT into `chat_participants` |
| `/api/v1/conversations/{conversation_id}/messages` | GET | `messages`, `users` | SELECT from `messages` JOIN `users` |
| `/api/v1/conversations/{conversation_id}/messages` | POST | `messages`, `message_media` | INSERT into `messages`, INSERT into `message_media` |
| `/api/v1/messages/{message_id}` | PATCH | `messages` | UPDATE `messages` |
| `/api/v1/messages/{message_id}` | DELETE | `messages` | UPDATE `messages` (is_deleted, deleted_at) |
| `/api/v1/conversations/{conversation_id}/read` | POST | `chat_participants` | UPDATE `chat_participants` (last_read_message_id) |
| `/api/v1/conversations/{conversation_id}/settings` | PUT | `chat_participants` | UPDATE `chat_participants` (is_muted, is_pinned, is_archived) |

## Group APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/groups` | GET | `groups`, `group_participants` | SELECT from `groups` JOIN `group_participants` |
| `/api/v1/groups` | POST | `groups`, `group_participants`, `chats`, `chat_participants` | INSERT into `groups`, INSERT into `group_participants`, INSERT into `chats`, INSERT into `chat_participants` |
| `/api/v1/groups/{group_id}` | GET | `groups`, `group_participants`, `users` | SELECT from `groups` JOIN `group_participants` JOIN `users` |
| `/api/v1/groups/{group_id}` | PUT | `groups` | UPDATE `groups` |
| `/api/v1/groups/{group_id}` | DELETE | `groups` | DELETE from `groups` (cascades to `group_participants`) |
| `/api/v1/groups/{group_id}/members` | GET | `group_participants`, `users` | SELECT from `group_participants` JOIN `users` |
| `/api/v1/groups/{group_id}/members` | POST | `group_participants`, `chat_participants` | INSERT into `group_participants`, INSERT into `chat_participants` |
| `/api/v1/groups/{group_id}/members/{user_id}` | PUT | `group_participants` | UPDATE `group_participants` (role) |
| `/api/v1/groups/{group_id}/members/{user_id}` | DELETE | `group_participants`, `chat_participants` | DELETE from `group_participants`, DELETE from `chat_participants` |

## Channel APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/channels` | GET | `channels`, `channel_subscribers` | SELECT from `channels` JOIN `channel_subscribers` |
| `/api/v1/channels` | POST | `channels`, `channel_subscribers` | INSERT into `channels`, INSERT into `channel_subscribers` |
| `/api/v1/channels/{channel_id}` | GET | `channels`, `channel_subscribers`, `users` | SELECT from `channels` JOIN `channel_subscribers` JOIN `users` |
| `/api/v1/channels/{channel_id}` | PUT | `channels` | UPDATE `channels` |
| `/api/v1/channels/{channel_id}` | DELETE | `channels` | DELETE from `channels` (cascades to `channel_subscribers`) |
| `/api/v1/channels/{channel_id}/subscribe` | POST | `channel_subscribers` | INSERT into `channel_subscribers` |
| `/api/v1/channels/{channel_id}/unsubscribe` | POST | `channel_subscribers` | DELETE from `channel_subscribers` |
| `/api/v1/channels/{channel_id}/messages` | GET | `messages`, `users` | SELECT from `messages` JOIN `users` |
| `/api/v1/channels/{channel_id}/messages` | POST | `messages`, `message_media` | INSERT into `messages`, INSERT into `message_media` |

## Media APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/media/upload` | POST | `media` | INSERT into `media` |
| `/api/v1/media/upload/encrypted` | POST | `media` | INSERT into `media` with `is_encrypted = true` |
| `/api/v1/media/upload/chunked/init` | POST | `media_upload_sessions` | INSERT into `media_upload_sessions` |
| `/api/v1/media/upload/chunked/{upload_id}/{chunk_index}` | PUT | `media_upload_chunks` | INSERT into `media_upload_chunks` |
| `/api/v1/media/upload/chunked/{upload_id}/status` | GET | `media_upload_sessions`, `media_upload_chunks` | SELECT from `media_upload_sessions` JOIN `media_upload_chunks` |
| `/api/v1/media/upload/chunked/{upload_id}/complete` | POST | `media`, `media_upload_sessions`, `media_upload_chunks` | INSERT into `media`, UPDATE `media_upload_sessions` |
| `/api/v1/media/{media_id}` | GET | `media` | SELECT from `media` |
| `/api/v1/media/{media_id}` | DELETE | `media` | DELETE from `media` |

## Story APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/stories` | GET | `stories`, `story_elements`, `media` | SELECT from `stories` JOIN `story_elements` JOIN `media` |
| `/api/v1/stories` | POST | `stories`, `story_elements` | INSERT into `stories`, INSERT into `story_elements` |
| `/api/v1/stories/feed` | GET | `stories`, `story_views`, `users`, `contacts`, `media` | SELECT from `stories` JOIN `users` JOIN `contacts` LEFT JOIN `story_views` JOIN `media` |
| `/api/v1/users/{user_handle}/stories` | GET | `stories`, `story_elements`, `users`, `media` | SELECT from `stories` JOIN `users` JOIN `story_elements` JOIN `media` |
| `/api/v1/stories/{story_id}` | GET | `stories`, `story_elements`, `media` | SELECT from `stories` JOIN `story_elements` JOIN `media` |
| `/api/v1/stories/{story_id}` | DELETE | `stories` | DELETE from `stories` (cascades to `story_elements`, `story_views`) |
| `/api/v1/stories/{story_id}/view` | POST | `story_views` | INSERT into `story_views` |
| `/api/v1/stories/{story_id}/viewers` | GET | `story_views`, `users` | SELECT from `story_views` JOIN `users` |

## Call APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/calls/logs` | GET | `call_logs`, `users` | SELECT from `call_logs` JOIN `users` |
| `/api/v1/calls/initiate` | POST | `calls`, `monthly_call_usage` | INSERT into `calls`, UPDATE `monthly_call_usage` |
| `/api/v1/calls/{call_id}/end` | POST | `calls`, `call_logs`, `monthly_call_usage` | UPDATE `calls`, INSERT into `call_logs`, UPDATE `monthly_call_usage` |

## Payment and Subscription APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/subscriptions/me` | GET | `subscriptions` | SELECT from `subscriptions` |
| `/api/v1/subscriptions/me` | DELETE | `subscriptions` | UPDATE `subscriptions` (status, auto_renew) |
| `/api/v1/subscriptions/gold/checkout` | POST | N/A | External payment processor integration |
| `/api/v1/payments/checkout` | POST | N/A | External payment processor integration |
| `/api/v1/payments/history` | GET | `payments` | SELECT from `payments` |
| `/api/v1/payments/webhook` | POST | `payments`, `subscriptions`, `users` | INSERT/UPDATE `payments`, INSERT/UPDATE `subscriptions`, UPDATE `users` |

## Support APIs

| API Endpoint | HTTP Method | Database Tables | Operations |
|--------------|-------------|-----------------|------------|
| `/api/v1/support/tickets` | GET | `support_tickets` | SELECT from `support_tickets` |
| `/api/v1/support/tickets` | POST | `support_tickets`, `support_ticket_messages` | INSERT into `support_tickets`, INSERT into `support_ticket_messages` |
| `/api/v1/support/tickets/{ticket_id}` | GET | `support_tickets` | SELECT from `support_tickets` |
| `/api/v1/support/tickets/{ticket_id}` | PUT | `support_tickets` | UPDATE `support_tickets` |
| `/api/v1/support/tickets/{ticket_id}/messages` | GET | `support_ticket_messages`, `support_ticket_attachments`, `media` | SELECT from `support_ticket_messages` LEFT JOIN `support_ticket_attachments` LEFT JOIN `media` |
| `/api/v1/support/tickets/{ticket_id}/messages` | POST | `support_ticket_messages`, `support_ticket_attachments` | INSERT into `support_ticket_messages`, INSERT into `support_ticket_attachments` |
