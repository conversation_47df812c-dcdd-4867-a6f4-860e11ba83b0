# Database Migration Strategy

This document outlines the approach for managing database schema changes throughout the development and production lifecycle of the Meena application.

## Principles

1. **Backward Compatibility**: Migrations must not break existing functionality
2. **Versioning**: All schema changes are versioned and tracked
3. **Automation**: Migrations are automated and repeatable
4. **Testing**: All migrations are tested before deployment
5. **Rollback**: Every migration has a corresponding rollback plan
6. **Documentation**: Schema changes are documented with their purpose and impact

## Migration Workflow

### Development Environment

```mermaid
flowchart TD
    A[Identify Schema Change] --> B[Create Migration File]
    B --> C[Test Migration Locally]
    C --> D{Tests Pass?}
    D -->|No| B
    D -->|Yes| E[Commit to Version Control]
    E --> F[CI/CD Pipeline]
```

1. **Identify Schema Change**:
   - Determine what database changes are needed
   - Document the purpose and impact of the change
   - Consider backward compatibility

2. **Create Migration File**:
   - Create a versioned migration file with:
     - Up migration (changes to apply)
     - Down migration (how to revert)
     - Migration metadata (version, description, dependencies)
   - Follow naming convention: `YYYYMMDDHHMMSS_descriptive_name.sql`

3. **Test Migration Locally**:
   - Apply to development database
   - Run application tests
   - Verify rollback functionality
   - Check performance impact

4. **Commit to Version Control**:
   - Include migration files in the same commit as related code changes
   - Update documentation if necessary

### CI/CD Pipeline

```mermaid
flowchart TD
    A[Pull Request] --> B[Run Migrations in Test DB]
    B --> C[Run Integration Tests]
    C --> D{Tests Pass?}
    D -->|No| E[Fix Issues]
    E --> B
    D -->|Yes| F[Approve for Staging]
    F --> G[Deploy to Staging]
    G --> H[Verify in Staging]
    H --> I{Verification OK?}
    I -->|No| E
    I -->|Yes| J[Approve for Production]
```

1. **Automated Testing**:
   - Apply migrations to test database
   - Run integration tests against migrated schema
   - Verify application functionality

2. **Staging Deployment**:
   - Deploy migrations to staging environment
   - Verify application functionality in staging
   - Monitor performance and errors

3. **Production Approval**:
   - Review migration impact in staging
   - Schedule production deployment
   - Prepare rollback plan

### Production Deployment

```mermaid
flowchart TD
    A[Schedule Maintenance Window] --> B[Take Database Backup]
    B --> C[Apply Migrations]
    C --> D[Verify Application Functionality]
    D --> E{Verification OK?}
    E -->|No| F[Execute Rollback]
    F --> G[Restore from Backup if Needed]
    E -->|Yes| H[Monitor Performance]
    H --> I[Update Documentation]
```

1. **Pre-Deployment**:
   - Schedule maintenance window if needed
   - Notify users of potential downtime
   - Take database backup

2. **Deployment**:
   - Apply migrations during maintenance window
   - Deploy corresponding application changes
   - Verify application functionality

3. **Post-Deployment**:
   - Monitor application performance
   - Monitor database performance
   - Update documentation

## Migration Tools

We will use [golang-migrate](https://github.com/golang-migrate/migrate) for managing database migrations. This tool provides:

- Version tracking
- Up and down migrations
- Command-line interface
- Programmatic API for CI/CD integration
- Support for multiple database types

### Migration File Structure

Each migration consists of two files:
- `YYYYMMDDHHMMSS_descriptive_name.up.sql` - For applying the migration
- `YYYYMMDDHHMMSS_descriptive_name.down.sql` - For reverting the migration

Example:

```sql
-- 20230415123000_add_user_last_active.up.sql
ALTER TABLE users
ADD COLUMN last_active_at TIMESTAMP WITH TIME ZONE;

-- 20230415123000_add_user_last_active.down.sql
ALTER TABLE users
DROP COLUMN last_active_at;
```

### Migration Commands

```bash
# Create a new migration
migrate create -ext sql -dir db/migrations -seq add_user_last_active

# Apply all pending migrations
migrate -path db/migrations -database "postgres://user:password@localhost:5432/meena?sslmode=disable" up

# Revert the last migration
migrate -path db/migrations -database "postgres://user:password@localhost:5432/meena?sslmode=disable" down 1

# Apply migrations up to a specific version
migrate -path db/migrations -database "postgres://user:password@localhost:5432/meena?sslmode=disable" goto 20230415123000
```

## Handling Schema Changes

Different types of schema changes require different approaches to maintain backward compatibility:

### Safe Changes (No Special Handling Required)

- Adding new tables
- Adding new columns (with default values or nullable)
- Adding indexes
- Adding constraints that all existing data satisfies

### Moderate Changes (Require Careful Planning)

- Renaming tables or columns
- Changing column types that are compatible (e.g., VARCHAR(50) to VARCHAR(100))
- Adding non-nullable columns with default values
- Adding foreign key constraints

### High-Risk Changes (Require Multi-Step Approach)

- Removing tables or columns
- Changing column types that are incompatible
- Splitting or merging tables
- Adding constraints that existing data might violate

### Multi-Step Migration Pattern

For high-risk changes, follow this pattern:

1. **Add**: Add new structures without removing old ones
2. **Migrate**: Copy/transform data from old to new structures
3. **Switch**: Update application to use new structures
4. **Remove**: Remove old structures after confirming no usage

Example: Changing a column type from `INTEGER` to `VARCHAR`

```sql
-- Step 1: Add new column (Migration 1)
ALTER TABLE users
ADD COLUMN username_str VARCHAR(50);

-- Step 2: Migrate data (Migration 1 or application code)
UPDATE users
SET username_str = username::VARCHAR(50);

-- Step 3: Switch application code to use new column
-- (Deploy application changes)

-- Step 4: Remove old column (Migration 2, after application deployment)
ALTER TABLE users
DROP COLUMN username;

-- Step 5: Rename new column to original name (Migration 2)
ALTER TABLE users
RENAME COLUMN username_str TO username;
```

## Data Migration Strategies

For large tables or complex data transformations:

### Batch Processing

```sql
-- Process in batches of 10,000 rows
DO $$
DECLARE
    batch_size INT := 10000;
    max_id INT;
    current_id INT := 0;
BEGIN
    SELECT MAX(id) INTO max_id FROM large_table;

    WHILE current_id <= max_id LOOP
        -- Process one batch
        UPDATE large_table
        SET new_column = transform(old_column)
        WHERE id > current_id AND id <= current_id + batch_size;

        current_id := current_id + batch_size;
        COMMIT;
    END LOOP;
END $$;
```

### Background Jobs

For very large tables, implement a background job:

```go
func MigrateUserData(batchSize int) {
    var lastProcessedID int64 = 0

    for {
        // Get next batch
        rows, err := db.Query(`
            SELECT id, old_column
            FROM large_table
            WHERE id > $1
            ORDER BY id
            LIMIT $2
        `, lastProcessedID, batchSize)

        if err != nil {
            log.Printf("Error querying batch: %v", err)
            return
        }

        // Process rows
        rowsProcessed := 0
        for rows.Next() {
            rowsProcessed++
            var id int64
            var oldValue string

            if err := rows.Scan(&id, &oldValue); err != nil {
                log.Printf("Error scanning row: %v", err)
                continue
            }

            // Transform data
            newValue := transformData(oldValue)

            // Update row
            if _, err := db.Exec(`
                UPDATE large_table
                SET new_column = $1
                WHERE id = $2
            `, newValue, id); err != nil {
                log.Printf("Error updating row %d: %v", id, err)
            }

            lastProcessedID = id
        }

        rows.Close()

        // If no rows processed, we're done
        if rowsProcessed == 0 {
            break
        }

        // Sleep to reduce database load
        time.Sleep(100 * time.Millisecond)
    }
}
```

## Testing Migrations

All migrations must be tested before deployment:

### Unit Tests

Test individual migrations to ensure they:
- Apply successfully
- Can be rolled back successfully
- Maintain data integrity

```go
func TestAddUserLastActiveMigration(t *testing.T) {
    // Set up test database
    db := setupTestDB()
    defer cleanupTestDB(db)

    // Apply migration
    err := applyMigration(db, "20230415123000")
    assert.NoError(t, err)

    // Verify column exists
    var columnExists bool
    err = db.QueryRow(`
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'last_active_at'
        )
    `).Scan(&columnExists)

    assert.NoError(t, err)
    assert.True(t, columnExists)

    // Test rollback
    err = rollbackMigration(db, "20230415123000")
    assert.NoError(t, err)

    // Verify column no longer exists
    err = db.QueryRow(`
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'last_active_at'
        )
    `).Scan(&columnExists)

    assert.NoError(t, err)
    assert.False(t, columnExists)
}
```

### Integration Tests

Test the application with the migrated schema to ensure:
- All features work correctly
- Performance is acceptable
- No regressions are introduced

## Monitoring and Validation

### Pre-Migration Validation

Before applying migrations in production:

1. **Schema Validation**:
   - Verify migration syntax
   - Check for potential conflicts with existing schema
   - Estimate migration duration
   - Run migration in a staging environment with production-like data volume
   - Verify that all database constraints are still satisfied

2. **Data Validation**:
   - Check if existing data meets new constraints
   - Identify potential data issues
   - Prepare data fixes if needed
   - Create data validation scripts to run before migration
   - Estimate data migration time for large tables

3. **Application Compatibility**:
   - Verify that application code works with both old and new schema
   - Test with feature flags to enable/disable new schema features
   - Ensure API versioning handles schema changes gracefully

### Post-Migration Validation

After applying migrations:

1. **Schema Verification**:
   - Verify all expected changes are applied
   - Check for unexpected changes
   - Verify indexes and constraints are correctly created
   - Check for any unexpected dependencies

2. **Data Integrity**:
   - Verify data was not corrupted
   - Run data consistency checks
   - Compare row counts before and after migration
   - Validate referential integrity
   - Run business logic validation rules

3. **Performance Monitoring**:
   - Monitor query performance
   - Check for new slow queries
   - Monitor database load
   - Compare query execution plans before and after
   - Check index usage statistics

4. **Automated Validation Suite**:
   - Run comprehensive test suite post-migration
   - Verify all critical business flows
   - Check for regressions in API response times
   - Validate data access patterns

```sql
-- Example post-migration validation query
SELECT
    table_name,
    column_name,
    data_type,
    character_maximum_length
FROM
    information_schema.columns
WHERE
    table_schema = 'public' AND
    table_name = 'users'
ORDER BY
    ordinal_position;
```

### Automated Rollback Procedure

Implement an automated rollback procedure that can be triggered if validation fails:

```go
func performMigrationWithValidation(db *sql.DB, migrationVersion string) error {
    // 1. Take pre-migration measurements
    preMigrationStats := collectDatabaseStats(db)

    // 2. Apply migration
    if err := applyMigration(db, migrationVersion); err != nil {
        return fmt.Errorf("migration failed: %w", err)
    }

    // 3. Run validation
    validationResults, err := validateMigration(db, preMigrationStats)
    if err != nil {
        log.Printf("Validation error: %v", err)
        return err
    }

    // 4. Check validation results
    if !validationResults.Passed() {
        log.Printf("Migration validation failed: %v", validationResults.Failures)

        // 5. Automatic rollback if validation fails
        log.Printf("Rolling back migration %s", migrationVersion)
        if err := rollbackMigration(db, migrationVersion); err != nil {
            // Critical failure: migration failed and rollback failed
            return fmt.Errorf("CRITICAL: migration validation failed and rollback failed: %w", err)
        }

        return fmt.Errorf("migration rolled back due to validation failure")
    }

    log.Printf("Migration %s successfully applied and validated", migrationVersion)
    return nil
}

func validateMigration(db *sql.DB, preMigrationStats DatabaseStats) (ValidationResults, error) {
    results := ValidationResults{}

    // 1. Schema validation
    if err := validateSchema(db, &results); err != nil {
        return results, err
    }

    // 2. Data integrity validation
    if err := validateDataIntegrity(db, &results); err != nil {
        return results, err
    }

    // 3. Performance validation
    if err := validatePerformance(db, preMigrationStats, &results); err != nil {
        return results, err
    }

    // 4. Application compatibility validation
    if err := validateApplicationCompatibility(&results); err != nil {
        return results, err
    }

    return results, nil
}
```

## Emergency Rollback Procedure

In case of critical issues after migration:

1. **Assess Impact**:
   - Determine severity of the issue
   - Decide whether to fix forward or roll back

2. **Rollback Decision**:
   - For severe issues affecting core functionality, roll back
   - For minor issues, consider fixing forward

3. **Rollback Execution**:
   - Stop application servers
   - Run migration down command
   - Verify database state
   - Deploy previous application version
   - Start application servers
   - Verify functionality

4. **Post-Rollback**:
   - Notify users
   - Investigate root cause
   - Fix migration issues
   - Reschedule migration

## Documentation Requirements

For each migration, document:

1. **Purpose**: Why the change is needed
2. **Impact**: What functionality is affected
3. **Dependencies**: Related code changes
4. **Rollback Plan**: How to revert if needed
5. **Performance Impact**: Expected effect on database performance

Example documentation template:

```markdown
# Migration: Add Last Active Timestamp to Users

## Purpose
Add a timestamp to track when users were last active in the application to support presence features and inactive user cleanup.

## Impact
- Enables tracking of user activity
- Supports new presence indicator feature
- Allows for identification of inactive accounts

## Dependencies
- Requires deployment of updated user service (PR #123)
- Requires update to presence management code (PR #124)

## Rollback Plan
- Run down migration to remove the column
- Deploy previous version of user service

## Performance Impact
- Minimal impact: one additional timestamp column
- Additional write on each user activity
- New index on last_active_at for inactive user queries
```

## Conclusion

Following this migration strategy ensures that database schema changes are:

1. **Safe**: Thoroughly tested and validated
2. **Reliable**: Automated and repeatable
3. **Reversible**: With clear rollback procedures
4. **Documented**: With clear purpose and impact
5. **Coordinated**: With application code changes

This approach minimizes risk and downtime while allowing the database schema to evolve with application requirements.
