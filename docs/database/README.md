# Meena Database Documentation

This directory contains comprehensive documentation for the Meena database schema. It serves as a single source of truth for both frontend and backend developers to understand the database structure and how it relates to the API.

## Table of Contents

1. [Master Schema](#master-schema)
2. [Schema Management](#schema-management)
3. [Data Dictionary](#data-dictionary)
4. [Entity-Relationship Diagram](#entity-relationship-diagram)
5. [API-Database Mapping](#api-database-mapping)
6. [How to Use This Documentation](#how-to-use-this-documentation)
7. [Contributing to This Documentation](#contributing-to-this-documentation)

## Master Schema

The [master_schema.sql](./master_schema.sql) file is the **SINGLE SOURCE OF TRUTH** for the database schema. It contains the complete SQL definition of the database schema, including:

- Table definitions
- Column types and constraints
- Foreign key relationships
- Indexes
- Enumerated types

This file can be used to create a new database instance or to understand the structure of the existing database. All other schema definitions in the codebase should be derived from or consistent with this file.

## Schema Management

The [SCHEMA_MANAGEMENT.md](./SCHEMA_MANAGEMENT.md) file outlines the process for managing the database schema. It includes:

- Guidelines for making changes to the schema
- The migration process
- Tools and scripts for schema validation
- Responsibilities for schema management

This document is essential for anyone making changes to the database schema.

## Data Dictionary

The [data_dictionary.md](./data_dictionary.md) file provides detailed descriptions of all tables and columns in the database. It includes:

- Table purposes
- Column descriptions
- Data types
- Constraints
- Default values
- Relationships between tables

This document is particularly useful for understanding the semantic meaning of the data stored in the database.

## Entity-Relationship Diagram

The [erd.md](./erd.md) file contains a visual representation of the database schema using Mermaid syntax. It shows:

- Entities (tables)
- Relationships between entities
- Cardinality of relationships
- Key attributes of each entity

For a more detailed and interactive ERD, we recommend using a dedicated tool like dbdiagram.io, Lucidchart, or Draw.io.

## API-Database Mapping

The [api_database_mapping.md](./api_database_mapping.md) file maps API endpoints to database operations. It helps developers understand:

- Which database tables are affected by each API endpoint
- What operations (SELECT, INSERT, UPDATE, DELETE) are performed
- How API requests and responses relate to database records

This document is particularly useful for frontend developers who need to understand how their API calls affect the database.

## How to Use This Documentation

### For Frontend Developers

1. Start with the [API-Database Mapping](./api_database_mapping.md) to understand how your API calls interact with the database.
2. Refer to the [Data Dictionary](./data_dictionary.md) to understand the meaning of the data you're working with.
3. Use the [ERD](./erd.md) to visualize the relationships between different entities.

### For Backend Developers

1. Use the [Master Schema](./master_schema.sql) as a reference for the database structure.
2. Follow the guidelines in [Schema Management](./SCHEMA_MANAGEMENT.md) when making changes to the schema.
3. Refer to the [Data Dictionary](./data_dictionary.md) for detailed information about tables and columns.
4. Use the [ERD](./erd.md) to understand the relationships between entities.
5. Consult the [API-Database Mapping](./api_database_mapping.md) to ensure your API implementations correctly interact with the database.

### For Database Administrators

1. Use the [Master Schema](./master_schema.sql) as the single source of truth for the database schema.
2. Follow the guidelines in [Schema Management](./SCHEMA_MANAGEMENT.md) for schema changes and migrations.
3. Use the schema validation script (`scripts/validate_schema.sh`) to ensure consistency.
4. Refer to the [ERD](./erd.md) to understand the overall structure of the database.
5. Use the [Data Dictionary](./data_dictionary.md) to understand the purpose and constraints of each table and column.

## Contributing to This Documentation

When making changes to the database schema, please follow the process outlined in [Schema Management](./SCHEMA_MANAGEMENT.md) and update all relevant documentation files:

1. Update the [Master Schema](./master_schema.sql) with any new or modified tables, columns, constraints, or indexes.
2. Create appropriate migration files in `backend/internal/database/migrations/`.
3. Run the schema generation script (`scripts/generate_schema_files.sh`) to update individual schema files.
4. Update the [Data Dictionary](./data_dictionary.md) with descriptions of new or modified tables and columns.
5. Update the [ERD](./erd.md) to reflect changes in the database structure.
6. Update the [API-Database Mapping](./api_database_mapping.md) if the changes affect how APIs interact with the database.

After making changes, run the schema validation script (`scripts/validate_schema.sh`) to ensure consistency between the master schema and the actual database schema.

Please ensure that all documentation files are consistent with each other and with the actual database schema.
