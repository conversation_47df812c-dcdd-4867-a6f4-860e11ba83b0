# Entity-Relationship Diagram (ERD)

This document provides a visual representation of the database schema using Mermaid syntax. For a more detailed and interactive ERD, we recommend using a dedicated tool like dbdiagram.io, Lucidchart, or Draw.io.

## Core Entities and Relationships

```mermaid
erDiagram
    USERS ||--o{ USER_SETTINGS : has
    USERS ||--o{ CONTACTS : has
    USERS ||--o{ CHAT_PARTICIPANTS : participates_in
    USERS ||--o{ GROUP_PARTICIPANTS : member_of
    USERS ||--o{ CHANNEL_SUBSCRIBERS : subscribes_to
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ MEDIA : uploads
    USERS ||--o{ STORIES : creates
    USERS ||--o{ STORY_VIEWS : views
    USERS ||--o{ CALLS : initiates
    USERS ||--o{ CALLS : receives
    USERS ||--o{ CALL_LOGS : has
    USERS ||--o{ MONTHLY_CALL_USAGE : tracks
    USERS ||--o{ SUBSCRIPTIONS : subscribes
    USERS ||--o{ PAYMENTS : makes
    USERS ||--o{ SUPPORT_TICKETS : creates

    CHATS ||--o{ CHAT_PARTICIPANTS : includes
    CHATS ||--o{ MESSAGES : contains
    CHATS ||--|| GROUPS : associated_with

    GROUPS ||--o{ GROUP_PARTICIPANTS : includes

    CHANNELS ||--o{ CHANNEL_SUBSCRIBERS : has

    MESSAGES ||--o{ MESSAGE_MEDIA : has
    MESSAGES ||--o{ MESSAGE_REACTIONS : has

    MEDIA ||--o{ MESSAGE_MEDIA : used_in
    MEDIA ||--o{ STORIES : base_for

    STORIES ||--o{ STORY_ELEMENTS : has
    STORIES ||--o{ STORY_VIEWS : viewed_by

    SUPPORT_TICKETS ||--o{ SUPPORT_TICKET_MESSAGES : has
    SUPPORT_TICKET_MESSAGES ||--o{ SUPPORT_TICKET_ATTACHMENTS : has
    MEDIA ||--o{ SUPPORT_TICKET_ATTACHMENTS : used_in
```

## User and Authentication

```mermaid
erDiagram
    USERS {
        UUID user_id PK
        VARCHAR user_handle UK
        VARCHAR email UK
        VARCHAR phone_number UK
        VARCHAR password_hash
        VARCHAR recovery_phrase_hash
        VARCHAR recovery_pin
        VARCHAR display_name
        VARCHAR profile_picture_url
        TEXT bio
        subscription_tier_enum subscription_tier
        verification_status_enum verification_status
        BOOLEAN requires_2fa
        BOOLEAN is_active
        TIMESTAMP created_at
        TIMESTAMP last_seen_at
        TIMESTAMP scheduled_deletion_at
    }

    USER_SETTINGS {
        UUID user_id PK,FK
        VARCHAR theme
        VARCHAR language
        BOOLEAN notifications
        BOOLEAN message_preview
        BOOLEAN read_receipts
        BOOLEAN typing_indicators
        VARCHAR call_permission
        VARCHAR message_permission
        VARCHAR group_permission
        TIMESTAMP updated_at
    }

    USER_TOTP_SECRETS {
        UUID user_id PK,FK
        VARCHAR secret_key
        BOOLEAN is_active
        TIMESTAMP created_at
    }

    CONTACTS {
        UUID contact_id PK
        UUID user_id FK
        UUID contact_user_id FK
        VARCHAR display_name
        TEXT notes
        BOOLEAN is_blocked
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    USERS ||--|| USER_SETTINGS : has
    USERS ||--o| USER_TOTP_SECRETS : has
    USERS ||--o{ CONTACTS : has
    CONTACTS }o--|| USERS : refers_to
```

## Messaging

```mermaid
erDiagram
    CHATS {
        UUID chat_id PK
        VARCHAR type
        UUID group_id FK
        UUID created_by FK
        TIMESTAMP created_at
        UUID last_message_id
        TIMESTAMP last_message_time
    }

    CHAT_PARTICIPANTS {
        UUID chat_id PK,FK
        UUID user_id PK,FK
        TIMESTAMP joined_at
        UUID last_read_message_id
        BOOLEAN is_archived
        BOOLEAN is_muted
        BOOLEAN is_pinned
    }

    MESSAGES {
        UUID message_id PK
        UUID chat_id FK
        UUID sender_id FK
        message_type_enum type
        TEXT content
        UUID reply_to_id FK
        UUID forwarded_from FK
        BOOLEAN is_edited
        TIMESTAMP edited_at
        BOOLEAN is_deleted
        TIMESTAMP deleted_at
        TIMESTAMP created_at
        moderation_status_enum moderation_status
    }

    MESSAGE_MEDIA {
        UUID message_id PK,FK
        UUID media_id PK,FK
    }

    MESSAGE_REACTIONS {
        UUID message_id PK,FK
        UUID user_id PK,FK
        VARCHAR reaction PK
        TIMESTAMP created_at
    }

    CHATS ||--o{ CHAT_PARTICIPANTS : has
    CHATS ||--o{ MESSAGES : contains
    MESSAGES ||--o{ MESSAGE_MEDIA : has
    MESSAGES ||--o{ MESSAGE_REACTIONS : has
    USERS ||--o{ CHAT_PARTICIPANTS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ MESSAGE_REACTIONS : reacts_to
```

## Groups and Channels

```mermaid
erDiagram
    GROUPS {
        UUID group_id PK
        VARCHAR name
        TEXT description
        VARCHAR avatar_url
        privacy_type_enum privacy_type
        UUID created_by FK
        TIMESTAMP created_at
        TIMESTAMP updated_at
        UUID chat_id
        moderation_status_enum moderation_status
    }

    GROUP_PARTICIPANTS {
        UUID group_id PK,FK
        UUID user_id PK,FK
        VARCHAR role
        UUID added_by FK
        TIMESTAMP joined_at
    }

    CHANNELS {
        UUID channel_id PK
        VARCHAR name
        TEXT topic
        VARCHAR avatar_url
        VARCHAR type
        UUID created_by FK
        TIMESTAMP created_at
        TIMESTAMP updated_at
        moderation_status_enum moderation_status
    }

    CHANNEL_SUBSCRIBERS {
        UUID channel_id PK,FK
        UUID user_id PK,FK
        TIMESTAMP subscribed_at
        BOOLEAN is_muted
    }

    GROUPS ||--o{ GROUP_PARTICIPANTS : has
    CHANNELS ||--o{ CHANNEL_SUBSCRIBERS : has
    USERS ||--o{ GROUP_PARTICIPANTS : member_of
    USERS ||--o{ CHANNEL_SUBSCRIBERS : subscribes_to
    USERS ||--o{ GROUPS : creates
    USERS ||--o{ CHANNELS : creates
    CHATS ||--|| GROUPS : associated_with
```

## Media and Stories

```mermaid
erDiagram
    MEDIA {
        UUID media_id PK
        UUID user_id FK
        media_type_enum type
        VARCHAR url
        VARCHAR thumbnail_url
        VARCHAR name
        BIGINT size
        VARCHAR mime_type
        INTEGER width
        INTEGER height
        INTEGER duration
        TIMESTAMP created_at
        moderation_status_enum moderation_status
    }

    STORIES {
        UUID story_id PK
        UUID user_id FK
        UUID base_media_id FK
        TEXT caption
        TIMESTAMP created_at
        TIMESTAMP expires_at
        moderation_status_enum moderation_status
    }

    STORY_ELEMENTS {
        UUID element_id PK
        UUID story_id FK
        VARCHAR element_type
        FLOAT position_x
        FLOAT position_y
        JSONB element_data
        TIMESTAMP created_at
    }

    STORY_VIEWS {
        UUID story_id PK,FK
        UUID user_id PK,FK
        TIMESTAMP viewed_at
    }

    USERS ||--o{ MEDIA : uploads
    USERS ||--o{ STORIES : creates
    USERS ||--o{ STORY_VIEWS : views
    MEDIA ||--o{ STORIES : base_for
    STORIES ||--o{ STORY_ELEMENTS : has
    STORIES ||--o{ STORY_VIEWS : viewed_by
```

## Calls

```mermaid
erDiagram
    CALLS {
        UUID call_id PK
        UUID caller_id FK
        UUID callee_id FK
        call_type_enum type
        call_status_enum status
        TIMESTAMP start_time
        TIMESTAMP end_time
        INTEGER duration_seconds
    }

    CALL_LOGS {
        UUID call_log_id PK
        UUID user_id FK
        UUID other_party_id FK
        call_type_enum type
        call_status_enum status
        TIMESTAMP start_time
        INTEGER duration_seconds
    }

    MONTHLY_CALL_USAGE {
        UUID user_id PK,FK
        INTEGER month PK
        INTEGER year PK
        INTEGER total_duration_seconds
    }

    USERS ||--o{ CALLS : initiates
    USERS ||--o{ CALLS : receives
    USERS ||--o{ CALL_LOGS : has
    USERS ||--o{ MONTHLY_CALL_USAGE : tracks
```

## Payments and Subscriptions

```mermaid
erDiagram
    SUBSCRIPTIONS {
        UUID subscription_id PK
        UUID user_id FK
        subscription_tier_enum tier
        VARCHAR status
        TIMESTAMP start_date
        TIMESTAMP end_date
        BOOLEAN auto_renew
        VARCHAR payment_method
        VARCHAR external_subscription_id
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    PAYMENTS {
        UUID payment_id PK
        UUID user_id FK
        UUID subscription_id FK
        DECIMAL amount
        VARCHAR currency
        VARCHAR status
        VARCHAR purpose
        VARCHAR external_payment_id
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    USERS ||--o{ SUBSCRIPTIONS : has
    USERS ||--o{ PAYMENTS : makes
    SUBSCRIPTIONS ||--o{ PAYMENTS : generates
```

## Support

```mermaid
erDiagram
    SUPPORT_TICKETS {
        UUID ticket_id PK
        UUID user_id FK
        VARCHAR subject
        ticket_status_enum status
        ticket_priority_enum priority
        VARCHAR category
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP resolved_at
    }

    SUPPORT_TICKET_MESSAGES {
        UUID message_id PK
        UUID ticket_id FK
        VARCHAR sender_type
        UUID sender_id FK
        TEXT message_body
        TIMESTAMP sent_at
    }

    SUPPORT_TICKET_ATTACHMENTS {
        UUID message_id PK,FK
        UUID media_id PK,FK
    }

    USERS ||--o{ SUPPORT_TICKETS : creates
    SUPPORT_TICKETS ||--o{ SUPPORT_TICKET_MESSAGES : has
    SUPPORT_TICKET_MESSAGES ||--o{ SUPPORT_TICKET_ATTACHMENTS : has
    MEDIA ||--o{ SUPPORT_TICKET_ATTACHMENTS : used_in
```

## Notes

This ERD provides a visual representation of the database schema. For a more detailed and interactive ERD, we recommend using a dedicated tool like:

1. **dbdiagram.io** - A free, web-based tool that allows you to create database diagrams using a simple DSL
2. **Lucidchart** - A professional diagramming tool with extensive ERD capabilities
3. **Draw.io** - A free, web-based diagramming tool that supports ERD creation
4. **MySQL Workbench** - A free tool that can connect to PostgreSQL and generate ERDs
5. **DBeaver** - A free, multi-platform database tool that can generate ERDs

These tools will provide a more interactive and detailed view of the database schema, allowing you to zoom in/out, rearrange entities, and export the diagram in various formats.
