# Database Schema Management Guide

This document outlines the process for managing the database schema in the Meena application. It establishes a single source of truth for the database schema and provides guidelines for making changes to the schema.

## Single Source of Truth

The **SINGLE SOURCE OF TRUTH** for the database schema is:

**`docs/database/master_schema.sql`**

This file contains the complete and up-to-date definition of all database tables, constraints, indexes, and other database objects. All other schema definitions in the codebase should be derived from or consistent with this file.

## Schema Change Process

When making changes to the database schema, follow these steps:

1. **Update the Master Schema File**:
   - Edit `docs/database/master_schema.sql` to reflect the changes
   - Add a comment with the date and a brief description of the change
   - Ensure the file remains valid SQL that can be executed to create a complete database

2. **Create a Migration File**:
   - Create a new migration file in `backend/internal/database/migrations/`
   - Follow the naming convention: `YYYYMMDDHHMMSS_descriptive_name.sql`
   - Include both "up" and "down" migrations
   - Test the migration locally

3. **Update Derived Schema Files**:
   - Update any individual table definition files in `backend/internal/database/schema/`
   - Update the Android Room schema if necessary
   - Update any other schema-related files

4. **Update Documentation**:
   - Update the ERD in `docs/database/erd.md`
   - Update the data dictionary in `docs/database/data_dictionary.md`
   - Update the API-database mapping in `docs/database/api_database_mapping.md`

5. **Create a Pull Request**:
   - Include all schema changes in a single pull request
   - Provide a clear description of the changes
   - Include any necessary application code changes

## Migration Guidelines

When creating migration files, follow these guidelines:

1. **Backward Compatibility**:
   - Ensure migrations don't break existing functionality
   - Use a multi-step approach for complex changes (add, migrate, switch, remove)

2. **Idempotency**:
   - Migrations should be idempotent (can be run multiple times without error)
   - Use `IF NOT EXISTS` and `IF EXISTS` clauses

3. **Atomicity**:
   - Each migration should be atomic (all or nothing)
   - Use transactions where appropriate

4. **Testing**:
   - Test migrations on a copy of production data
   - Verify both "up" and "down" migrations

## Schema Validation

To ensure the database schema matches the master schema file, run the schema validation script:

```bash
./scripts/validate_schema.sh
```

This script compares the actual database schema with the master schema file and reports any discrepancies.

## Handling Discrepancies

If you discover discrepancies between the actual database schema and the master schema file:

1. **Identify the Source**:
   - Determine which version is correct (usually the actual database schema)
   - Identify how the discrepancy occurred

2. **Create a Fix**:
   - Update the master schema file to match the actual schema
   - Create a migration to fix any issues in the actual schema
   - Update any derived schema files

3. **Document the Fix**:
   - Create a document in `docs/database/fixes/` describing the issue and fix
   - Include the date, affected tables, and resolution

## Tools

The following tools are used for database schema management:

1. **golang-migrate**: For applying migrations
2. **pg_dump**: For backing up the database
3. **psql**: For executing SQL commands
4. **schema-validator**: For validating the schema

## Responsibilities

- **Database Administrator**: Responsible for reviewing and approving schema changes
- **Backend Developers**: Responsible for creating migrations and updating schema files
- **Frontend Developers**: Responsible for ensuring client code is compatible with schema changes
- **DevOps**: Responsible for applying migrations in production

## Conclusion

Following this schema management process ensures that:

1. There is a single source of truth for the database schema
2. All schema changes are properly documented and tracked
3. The actual database schema remains consistent with the documentation
4. Schema changes are applied safely and consistently across environments
