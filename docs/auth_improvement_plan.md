# Authentication Improvement Plan

This plan outlines the steps to address two related issues in the Meena application's authentication system: a security vulnerability related to refresh token handling and a bug causing login failures after logout.

## Problem Identification

1.  **Refresh Token Vulnerability:** The current backend does not store or invalidate refresh tokens server-side. If a refresh token is compromised, it can be used repeatedly to obtain new access tokens until it expires, making the system vulnerable to replay attacks.
2.  **Login Failure Bug:** <PERSON><PERSON> attempts fail with an "invalid credentials: incorrect password" error immediately after a user logs out, even when correct credentials are provided. This suggests an issue in the login process itself, potentially related to how user data is handled on the frontend after logout.

## Proposed Solution

1.  Implement a secure refresh token mechanism by storing and invalidating refresh tokens server-side, adhering to industry best practices.
2.  Investigate and fix the login failure bug by debugging both the backend's user retrieval/password verification and the frontend's login flow, particularly its interaction with local user data after logout.

## Detailed Plan

### Step 1: Debug Login Failure

*   **Backend Debugging:**
    *   Add detailed logging within the `GetUserByIdentifier` function in [`backend/internal/database/user_repository.go`](backend/internal/database/user_repository.go) to log the `identifier` being used and the `user` object retrieved (including the `password_hash`) before it's returned.
    *   Add logging within the `Login` function in [`backend/internal/services/auth_service.go`](backend/internal/services/auth_service.go) to log the `user.PasswordHash` and the `req.Password` just before the `bcrypt.CompareHashAndPassword` call.
*   **Frontend Debugging:**
    *   Examine the login flow in the frontend code (likely in a ViewModel or UI component that uses `AuthRepository.login`) to understand how it calls the `AuthRepository.login` function and what data it uses or expects before and after the call.
    *   Add logging or debugging points in the frontend to see if the local user data being deleted on logout impacts the subsequent login attempt's parameters or process.
*   **Reproduce and Analyze:** Reproduce the login failure bug (create user, logout, immediate login attempt) and analyze both backend and frontend logs to pinpoint the exact cause of the "invalid credentials" error.

### Step 2: Implement Secure Refresh Tokens

*   **Database Schema Update:**
    *   Add a new table, `refresh_tokens`, to the database schema.
    *   This table will store the refresh token (or a hash), associated user ID, expiration timestamp, and a flag/timestamp for usage/revocation.
*   **Repository Layer Update:**
    *   Create a `RefreshTokenRepository` or add functions to `UserRepository` to handle database operations for refresh tokens (store, retrieve/validate, mark as used/revoke).
*   **Auth Service Update:**
    *   Modify `Login` and `Register` to store the refresh token in the database.
    *   Update `RefreshToken` to retrieve, validate against the database, mark as used, generate new tokens, and store the new refresh token.
    *   Implement a `Logout` function to revoke the refresh token in the database.
*   **Auth Handler Update:**
    *   Add a new API endpoint `/auth/logout` that calls the `AuthService.Logout` function.
*   **Middleware Update:**
    *   Ensure `AuthMiddleware` and `ValidateToken` continue to function correctly with the updated token handling in the service layer.
*   **Configuration Update:**
    *   Review and potentially update JWT expiration times in [`backend/internal/config/config.go`](backend/internal/config/config.go).

### Step 3: Address Login Bug based on Debugging

*   Based on the findings from Step 1, implement the necessary fix for the login failure bug.

### Step 4: Testing

*   Thoroughly test both the secure refresh token mechanism and the fixed login process, including the scenario where login fails after logout.

## Refresh Token Flow (Step 2)

```mermaid
sequenceDiagram
    Participant Client
    Participant BackendAPI
    Participant AuthService
    Participant RefreshTokenRepository
    Participant Database

    Client->>BackendAPI: POST /auth/refresh (refreshToken)
    BackendAPI->>AuthService: RefreshToken(refreshToken)
    AuthService->>RefreshTokenRepository: GetAndValidateToken(refreshToken)
    RefreshTokenRepository->>Database: Query refresh_tokens table
    Database-->>RefreshTokenRepository: Token Data (or not found)
    RefreshTokenRepository-->>AuthService: Token Data (or error)

    alt Token Valid and Not Used
        AuthService->>RefreshTokenRepository: MarkTokenAsUsed(refreshToken)
        RefreshTokenRepository->>Database: Update refresh_tokens table
        Database-->>RefreshTokenRepository: Success
        RefreshTokenRepository-->>AuthService: Success

        AuthService->>AuthService: Generate New Access & Refresh Tokens
        AuthService->>RefreshTokenRepository: StoreToken(newRefreshToken, userID, expiration)
        RefreshTokenRepository->>Database: Insert into refresh_tokens table
        Database-->>RefreshTokenRepository: Success
        RefreshTokenRepository-->>AuthService: Success

        AuthService-->>BackendAPI: New Access & Refresh Tokens
        BackendAPI-->>Client: 200 OK (New Tokens)
    else Token Invalid, Expired, or Used
        AuthService-->>BackendAPI: Error (e.g., Invalid Token)
        BackendAPI-->>Client: 401 Unauthorized (Error)
    end