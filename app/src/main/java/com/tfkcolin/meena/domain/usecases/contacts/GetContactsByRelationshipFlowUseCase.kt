package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.domain.repositories.IContactRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting contacts with a specific relationship as a flow.
 */
class GetContactsByRelationshipFlowUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Get contacts with a specific relationship as a flow.
     * 
     * @param relationship The relationship type.
     * @return A flow of contacts with the specified relationship.
     */
    operator fun invoke(relationship: String): Flow<List<Contact>> {
        return contactRepository.getContactsByRelationshipFlow(relationship)
    }
}
