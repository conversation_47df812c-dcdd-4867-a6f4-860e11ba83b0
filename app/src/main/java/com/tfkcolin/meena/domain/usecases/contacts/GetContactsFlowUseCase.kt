package com.tfkcolin.meena.domain.usecases.contacts

import com.tfkcolin.meena.data.models.Contact
import com.tfkcolin.meena.domain.repositories.IContactRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for getting the user's contact list as a flow.
 */
class GetContactsFlowUseCase @Inject constructor(
    private val contactRepository: IContactRepository
) {
    
    /**
     * Get the user's contact list as a flow.
     * 
     * @return A flow of the user's contacts.
     */
    operator fun invoke(): Flow<List<Contact>> {
        return contactRepository.getContactsFlow()
    }
}
