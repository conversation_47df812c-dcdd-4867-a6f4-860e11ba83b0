package com.tfkcolin.meena.domain.usecases.chat

import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.domain.repositories.IChatRepository
import javax.inject.Inject

/**
 * Use case for marking all messages in a chat as read.
 */
class MarkChatMessagesAsReadUseCase @Inject constructor(
    private val chatRepository: IChatRepository,
    private val tokenManager: TokenManager
) {
    
    /**
     * Mark all messages in a chat as read.
     * 
     * @param chatId The chat ID.
     */
    suspend operator fun invoke(chatId: String) {
        val currentUserId = tokenManager.getUserId() ?: return
        chatRepository.markChatMessagesAsRead(chatId, currentUserId)
    }
}
