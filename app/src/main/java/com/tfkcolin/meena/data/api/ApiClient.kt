package com.tfkcolin.meena.data.api

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.tfkcolin.meena.BuildConfig
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API client for creating Retrofit API interfaces.
 */
@Singleton
class ApiClient @Inject constructor(
    private val authInterceptor: AuthInterceptor,
    private val tokenAuthenticator: TokenAuthenticator
) {

    companion object {
        // Base URL for the API
        private const val BASE_URL = "https://api.meena.com/" // Replace with actual API URL

        // Timeouts
        private const val CONNECT_TIMEOUT = 15L
        private const val READ_TIMEOUT = 30L
        private const val WRITE_TIMEOUT = 30L
    }

    // Create Gson instance for JSON serialization/deserialization
    private val gson: Gson = GsonBuilder()
        .setLenient()
        .create()

    // Create OkHttpClient with interceptors and authenticator
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
        .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
        .addInterceptor(authInterceptor)
        .authenticator(tokenAuthenticator)
        .also {
            // Add logging interceptor in debug builds
            if (BuildConfig.DEBUG) {
                val loggingInterceptor = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                it.addInterceptor(loggingInterceptor)
            }
        }
        .build()

    // Create Retrofit instance
    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create(gson))
        .build()

    // Create API interfaces
    val authApi: AuthApi = retrofit.create(AuthApi::class.java)
    val contactApi: ContactApi = retrofit.create(ContactApi::class.java)
    val userApi: UserApi = retrofit.create(UserApi::class.java)
}
