package com.tfkcolin.meena.data.websocket

import android.util.Log
import com.google.gson.Gson
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.data.models.MessageDeletion
import com.tfkcolin.meena.data.models.MessageEdit
import com.tfkcolin.meena.data.models.MessageStatus
import com.tfkcolin.meena.utils.NetworkMonitor
import java.util.UUID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for managing WebSocket connections and message handling.
 */
@Singleton
class WebSocketService @Inject constructor(
    private val webSocketClient: WebSocketClient,
    private val messageSerializer: WebSocketMessageSerializer,
    private val tokenManager: TokenManager,
    private val gson: Gson,
    private val networkMonitor: NetworkMonitor
) {
    companion object {
        private const val TAG = "WebSocketService"
    }

    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // Flows for messages and typing indicators
    private val _messages = MutableSharedFlow<Message>(replay = 0)
    val messages: SharedFlow<Message> = _messages.asSharedFlow()

    private val _messageStatuses = MutableSharedFlow<MessageStatus>(replay = 0)
    val messageStatuses: SharedFlow<MessageStatus> = _messageStatuses.asSharedFlow()

    private val _typingIndicators = MutableSharedFlow<TypingPayload>(replay = 0)
    val typingIndicators: SharedFlow<TypingPayload> = _typingIndicators.asSharedFlow()

    private val _messageEdits = MutableSharedFlow<MessageEdit>(replay = 0)
    val messageEdits: SharedFlow<MessageEdit> = _messageEdits.asSharedFlow()

    private val _messageReactions = MutableSharedFlow<MessageReactionPayload>(replay = 0)
    val messageReactions: SharedFlow<MessageReactionPayload> = _messageReactions.asSharedFlow()

    private val _messageReplies = MutableSharedFlow<Message>(replay = 0)
    val messageReplies: SharedFlow<Message> = _messageReplies.asSharedFlow()

    private val _messageDeletions = MutableSharedFlow<MessageDeletion>(replay = 0)
    val messageDeletions: SharedFlow<MessageDeletion> = _messageDeletions.asSharedFlow()

    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()

    init {
        // Listen for incoming WebSocket messages
        coroutineScope.launch {
            webSocketClient.incomingMessages.collectLatest { message ->
                handleIncomingMessage(message)
            }
        }

        // Monitor network connectivity and reconnect when network becomes available
        coroutineScope.launch {
            networkMonitor.observeNetworkConnectivity().collectLatest { isConnected ->
                if (isConnected) {
                    Log.d(TAG, "Network is available, connecting to WebSocket")
                    if (_connectionState.value != ConnectionState.CONNECTED) {
                        connect()
                    }
                } else {
                    Log.d(TAG, "Network is unavailable, WebSocket will reconnect when network is available")
                    _connectionState.value = ConnectionState.DISCONNECTED
                }
            }
        }
    }

    /**
     * Connect to the WebSocket server.
     */
    fun connect() {
        if (tokenManager.isLoggedIn()) {
            _connectionState.value = ConnectionState.CONNECTING
            webSocketClient.connect()
            _connectionState.value = ConnectionState.CONNECTED
        }
    }

    /**
     * Disconnect from the WebSocket server.
     */
    fun disconnect() {
        webSocketClient.disconnect()
        _connectionState.value = ConnectionState.DISCONNECTED
    }

    /**
     * Send a chat message.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The message ID.
     */
    fun sendMessage(
        chatId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): String? {
        val userId = tokenManager.getUserId() ?: return null

        val message = messageSerializer.createChatMessage(
            chatId = chatId,
            senderId = userId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl
        )

        webSocketClient.sendMessage(message)

        return message.id
    }

    /**
     * Send a message status update.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param status The message status.
     */
    fun sendMessageStatus(
        messageId: String,
        chatId: String,
        status: String
    ) {
        val userId = tokenManager.getUserId() ?: return

        val message = messageSerializer.createMessageStatus(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            status = status
        )

        webSocketClient.sendMessage(message)
    }

    /**
     * Send a message status update and emit it to the messageStatuses flow.
     * This is used when the status update is initiated by this device.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param status The message status.
     */
    fun sendMessageStatusUpdate(
        messageId: String,
        chatId: String,
        status: String
    ) {
        // Send the status update via WebSocket
        sendMessageStatus(messageId, chatId, status)

        // Also emit the status update to the messageStatuses flow
        val userId = tokenManager.getUserId() ?: return

        val messageStatus = MessageStatus(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            status = status,
            timestamp = System.currentTimeMillis()
        )

        coroutineScope.launch {
            _messageStatuses.emit(messageStatus)
        }
    }

    /**
     * Send a typing indicator.
     *
     * @param chatId The chat ID.
     * @param isTyping Whether the user is typing.
     */
    fun sendTypingIndicator(
        chatId: String,
        isTyping: Boolean
    ) {
        val userId = tokenManager.getUserId() ?: return

        val message = messageSerializer.createTypingIndicator(
            chatId = chatId,
            userId = userId,
            isTyping = isTyping
        )

        webSocketClient.sendMessage(message)
    }

    /**
     * Send a message edit notification.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param newContent The new content.
     */
    fun sendMessageEdit(
        messageId: String,
        chatId: String,
        newContent: String
    ) {
        val userId = tokenManager.getUserId() ?: return

        val message = messageSerializer.createMessageEdit(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            newContent = newContent
        )

        webSocketClient.sendMessage(message)
    }

    /**
     * Send a message deletion notification.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param deleteType The delete type (self or everyone).
     */
    fun sendMessageDeletion(
        messageId: String,
        chatId: String,
        deleteType: String
    ) {
        val userId = tokenManager.getUserId() ?: return

        val message = messageSerializer.createMessageDeletion(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            deleteType = deleteType
        )

        webSocketClient.sendMessage(message)
    }

    /**
     * Send a message reaction.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param emoji The emoji reaction.
     * @param isAdd Whether to add (true) or remove (false) the reaction.
     */
    fun sendMessageReaction(
        messageId: String,
        chatId: String,
        emoji: String,
        isAdd: Boolean
    ) {
        val userId = tokenManager.getUserId() ?: return

        val message = messageSerializer.createMessageReaction(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            emoji = emoji,
            isAdd = isAdd
        )

        webSocketClient.sendMessage(message)
    }

    /**
     * Send a message reply.
     *
     * @param chatId The chat ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The message ID.
     */
    fun sendMessageReply(
        chatId: String,
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): String? {
        val userId = tokenManager.getUserId() ?: return null

        val messageId = UUID.randomUUID().toString()

        val message = messageSerializer.createMessageReply(
            messageId = messageId,
            chatId = chatId,
            senderId = userId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl,
            replyToMessageId = replyToMessageId
        )

        webSocketClient.sendMessage(message)

        return messageId
    }

    /**
     * Handle an incoming WebSocket message.
     *
     * @param message The message to handle.
     */
    private suspend fun handleIncomingMessage(message: WebSocketMessage) {
        when (message.type) {
            WebSocketMessageType.MESSAGE -> handleChatMessage(message)
            WebSocketMessageType.MESSAGE_DELIVERED -> handleMessageStatus(message, "delivered")
            WebSocketMessageType.MESSAGE_READ -> handleMessageStatus(message, "read")
            WebSocketMessageType.MESSAGE_TYPING -> handleTypingIndicator(message)
            WebSocketMessageType.MESSAGE_EDIT -> handleMessageEdit(message)
            WebSocketMessageType.MESSAGE_DELETE -> handleMessageDeletion(message)
            WebSocketMessageType.MESSAGE_REACTION_ADD -> handleMessageReaction(message, true)
            WebSocketMessageType.MESSAGE_REACTION_REMOVE -> handleMessageReaction(message, false)
            WebSocketMessageType.MESSAGE_REPLY -> handleMessageReply(message)
            WebSocketMessageType.CONNECT_ACK -> _connectionState.value = ConnectionState.CONNECTED
            WebSocketMessageType.DISCONNECT -> _connectionState.value = ConnectionState.DISCONNECTED
            WebSocketMessageType.ERROR -> handleError(message)
            else -> Log.d(TAG, "Unhandled message type: ${message.type}")
        }
    }

    /**
     * Handle a chat message.
     *
     * @param message The message to handle.
     */
    private suspend fun handleChatMessage(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val chatMessagePayload = gson.fromJson(payload, ChatMessagePayload::class.java)

            val chatMessage = Message(
                id = message.id ?: "",
                chatId = chatMessagePayload.chatId,
                senderId = chatMessagePayload.senderId,
                recipientId = chatMessagePayload.recipientId,
                content = chatMessagePayload.content,
                contentType = chatMessagePayload.contentType,
                mediaUrl = chatMessagePayload.mediaUrl,
                timestamp = message.timestamp,
                status = "sent"
            )

            _messages.emit(chatMessage)

            // Send delivery receipt
            sendMessageStatus(
                messageId = chatMessage.id,
                chatId = chatMessage.chatId,
                status = "delivered"
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing chat message", e)
        }
    }

    /**
     * Handle a message status update.
     *
     * @param message The message to handle.
     * @param status The message status.
     */
    private suspend fun handleMessageStatus(message: WebSocketMessage, status: String) {
        val payload = message.payload ?: return

        try {
            val statusPayload = gson.fromJson(payload, MessageStatusPayload::class.java)

            val messageStatus = MessageStatus(
                messageId = statusPayload.messageId,
                chatId = statusPayload.chatId,
                userId = statusPayload.userId,
                status = status,
                timestamp = message.timestamp
            )

            _messageStatuses.emit(messageStatus)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message status", e)
        }
    }

    /**
     * Handle a typing indicator.
     *
     * @param message The message to handle.
     */
    private suspend fun handleTypingIndicator(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val typingPayload = gson.fromJson(payload, TypingPayload::class.java)
            _typingIndicators.emit(typingPayload)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing typing indicator", e)
        }
    }

    /**
     * Handle an error message.
     *
     * @param message The message to handle.
     */
    private fun handleError(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val errorPayload = gson.fromJson(payload, ErrorPayload::class.java)
            Log.e(TAG, "WebSocket error: ${errorPayload.code} - ${errorPayload.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing error message", e)
        }
    }

    /**
     * Handle a message edit.
     *
     * @param message The message to handle.
     */
    private suspend fun handleMessageEdit(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val editPayload = gson.fromJson(payload, MessageEditPayload::class.java)

            val messageEdit = MessageEdit(
                messageId = editPayload.messageId,
                chatId = editPayload.chatId,
                userId = editPayload.userId,
                newContent = editPayload.newContent,
                timestamp = message.timestamp
            )

            _messageEdits.emit(messageEdit)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message edit", e)
        }
    }

    /**
     * Handle a message reaction.
     *
     * @param message The message to handle.
     * @param isAdd Whether the reaction is being added (true) or removed (false).
     */
    private suspend fun handleMessageReaction(message: WebSocketMessage, isAdd: Boolean) {
        val payload = message.payload ?: return

        try {
            val reactionPayload = gson.fromJson(payload, MessageReactionPayload::class.java)
            _messageReactions.emit(reactionPayload)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message reaction", e)
        }
    }

    /**
     * Handle a message reply.
     *
     * @param message The message to handle.
     */
    private suspend fun handleMessageReply(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val replyPayload = gson.fromJson(payload, MessageReplyPayload::class.java)

            val chatMessage = Message(
                id = message.id ?: "",
                chatId = replyPayload.chatId,
                senderId = replyPayload.senderId,
                recipientId = replyPayload.recipientId,
                content = replyPayload.content,
                contentType = replyPayload.contentType,
                mediaUrl = replyPayload.mediaUrl,
                timestamp = message.timestamp,
                status = "sent",
                replyToMessageId = replyPayload.replyToMessageId
            )

            // Emit to both messages and messageReplies flows
            _messages.emit(chatMessage)
            _messageReplies.emit(chatMessage)

            // Send delivery receipt
            sendMessageStatus(
                messageId = chatMessage.id,
                chatId = chatMessage.chatId,
                status = "delivered"
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message reply", e)
        }
    }

    /**
     * Handle a message deletion.
     *
     * @param message The message to handle.
     */
    private suspend fun handleMessageDeletion(message: WebSocketMessage) {
        val payload = message.payload ?: return

        try {
            val deletionPayload = gson.fromJson(payload, MessageDeletionPayload::class.java)

            val messageDeletion = MessageDeletion(
                messageId = deletionPayload.messageId,
                chatId = deletionPayload.chatId,
                userId = deletionPayload.userId,
                deleteType = deletionPayload.deleteType,
                timestamp = message.timestamp
            )

            _messageDeletions.emit(messageDeletion)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing message deletion", e)
        }
    }


}

/**
 * WebSocket connection states.
 */
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED
}
