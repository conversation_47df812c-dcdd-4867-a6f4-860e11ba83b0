package com.tfkcolin.meena.data.websocket

import android.util.Log
import com.tfkcolin.meena.utils.TokenManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import javax.inject.Inject
import javax.inject.Singleton

/**
 * WebSocket client for real-time communication.
 *
 * This client uses the WebSocket protocol's built-in ping/pong mechanism for connection
 * health monitoring rather than application-level ping/pong messages. The OkHttp WebSocket
 * client automatically responds to protocol-level ping frames from the server with pong frames.
 */
@Singleton
class WebSocketClient @Inject constructor(
    private val okHttpClient: OkHttpClient,
    private val tokenManager: TokenManager,
    private val messageSerializer: WebSocketMessageSerializer
) {
    companion object {
        private const val TAG = "WebSocketClient"
        private const val NORMAL_CLOSURE_STATUS = 1000
        private const val RECONNECT_DELAY = 5000L // 5 seconds
        private const val PING_INTERVAL = 30000L // 30 seconds
        private const val WS_URL = "wss://socialmediabackend-production-57c8.up.railway.app/api/v1/ws" // Railway deployment WebSocket URL
    }

    private var webSocket: WebSocket? = null
    private var isConnected = false
    private var reconnectJob: Job? = null
    private var pingJob: Job? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // Flows for incoming and outgoing messages
    private val _incomingMessages = MutableSharedFlow<WebSocketMessage>(replay = 0)
    val incomingMessages: SharedFlow<WebSocketMessage> = _incomingMessages

    /**
     * Connect to the WebSocket server.
     */
    fun connect() {
        if (isConnected) return

        val accessToken = tokenManager.getAccessToken() ?: return

        val request = Request.Builder()
            .url(WS_URL)
            .header("Authorization", "Bearer $accessToken")
            .header("Connection", "Upgrade")
            .header("Upgrade", "websocket")
            .build()

        webSocket = okHttpClient.newWebSocket(request, createWebSocketListener())

        // Start ping job
        startPingJob()
    }

    /**
     * Disconnect from the WebSocket server.
     */
    fun disconnect() {
        webSocket?.close(NORMAL_CLOSURE_STATUS, "Disconnected by user")
        webSocket = null
        isConnected = false

        // Cancel ping job
        pingJob?.cancel()
        pingJob = null

        // Cancel reconnect job
        reconnectJob?.cancel()
        reconnectJob = null
    }

    /**
     * Send a message to the WebSocket server.
     *
     * @param message The message to send.
     */
    fun sendMessage(message: WebSocketMessage) {
        if (!isConnected) {
            connect()
            return
        }

        val messageJson = messageSerializer.serialize(message)
        webSocket?.send(messageJson)
    }

    /**
     * Create a WebSocket listener.
     */
    private fun createWebSocketListener(): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket connection opened")
                isConnected = true

                // Cancel reconnect job if it's running
                reconnectJob?.cancel()
                reconnectJob = null
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "WebSocket message received: $text")

                try {
                    // Skip empty messages
                    if (text.isBlank()) {
                        Log.d(TAG, "Received empty WebSocket message, ignoring")
                        return
                    }

                    val message = messageSerializer.deserialize(text)
                    coroutineScope.launch {
                        _incomingMessages.emit(message)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing WebSocket message", e)
                }
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket connection closing: $code, $reason")
                webSocket.close(NORMAL_CLOSURE_STATUS, null)
                isConnected = false
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket connection closed: $code, $reason")
                isConnected = false

                // Cancel ping job
                pingJob?.cancel()
                pingJob = null
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket connection failure", t)
                isConnected = false

                // Cancel ping job
                pingJob?.cancel()
                pingJob = null

                // Start reconnect job
                startReconnectJob()
            }
        }
    }

    /**
     * Start a job to monitor connection health.
     *
     * Note: We no longer send application-level PING messages as we rely on
     * the WebSocket protocol's built-in ping/pong mechanism which is more efficient.
     * OkHttp automatically responds to protocol-level pings from the server.
     */
    private fun startPingJob() {
        // We're keeping the method for backward compatibility, but it no longer sends pings
        pingJob?.cancel()
        pingJob = coroutineScope.launch {
            // This job now just monitors the connection but doesn't send pings
            while (isActive) {
                delay(PING_INTERVAL)
                // Just check if we're still connected
                if (!isConnected && webSocket != null) {
                    Log.d(TAG, "Connection appears to be lost, attempting reconnect")
                    startReconnectJob()
                }
            }
        }
    }

    /**
     * Start a job to reconnect to the WebSocket server with exponential backoff.
     */
    private fun startReconnectJob() {
        reconnectJob?.cancel()
        reconnectJob = coroutineScope.launch {
            var retryCount = 0
            val maxRetries = 5

            while (retryCount < maxRetries && !isConnected) {
                // Exponential backoff with jitter
                val delayMillis = (RECONNECT_DELAY * Math.pow(2.0, retryCount.toDouble())).toLong() +
                        (Math.random() * 1000).toLong()

                Log.d(TAG, "Reconnecting in ${delayMillis}ms (attempt ${retryCount + 1}/$maxRetries)")
                delay(delayMillis)

                // Try to reconnect
                connect()

                // Wait a bit to see if connection is established
                delay(1000)

                // If connection successful, break the loop
                if (isConnected) {
                    Log.d(TAG, "Reconnection successful")
                    break
                }

                retryCount++
            }

            if (!isConnected && retryCount >= maxRetries) {
                Log.e(TAG, "Failed to reconnect after $maxRetries attempts")
            }
        }
    }
}
