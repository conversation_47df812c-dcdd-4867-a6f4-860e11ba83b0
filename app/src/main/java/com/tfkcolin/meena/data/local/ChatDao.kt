package com.tfkcolin.meena.data.local

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.tfkcolin.meena.data.models.Chat
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for the Chat entity.
 */
@Dao
interface ChatDao {

    /**
     * Insert a chat into the database.
     * If the chat already exists, replace it.
     *
     * @param chat The chat to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChat(chat: Chat)

    /**
     * Insert multiple chats into the database.
     * If a chat already exists, replace it.
     *
     * @param chats The chats to insert.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChats(chats: List<Chat>)

    /**
     * Update a chat's last message.
     *
     * @param chatId The chat ID.
     * @param lastMessage The last message.
     * @param lastMessageTimestamp The last message timestamp.
     */
    @Query("UPDATE chats SET lastMessage = :lastMessage, lastMessageTimestamp = :lastMessageTimestamp WHERE id = :chatId")
    suspend fun updateChatLastMessage(chatId: String, lastMessage: String, lastMessageTimestamp: Long)

    /**
     * Update a chat's unread count.
     *
     * @param chatId The chat ID.
     * @param unreadCount The unread count.
     */
    @Query("UPDATE chats SET unreadCount = :unreadCount WHERE id = :chatId")
    suspend fun updateChatUnreadCount(chatId: String, unreadCount: Int)

    /**
     * Get a chat by ID.
     *
     * @param chatId The chat ID.
     * @return The chat, or null if not found.
     */
    @Query("SELECT * FROM chats WHERE id = :chatId")
    suspend fun getChatById(chatId: String): Chat?

    /**
     * Get a chat by participants.
     *
     * @param participantIds The participant IDs.
     * @return The chat, or null if not found.
     */
    @Query("SELECT * FROM chats WHERE participantIds = :participantIds")
    suspend fun getChatByParticipants(participantIds: String): Chat?

    /**
     * Get all chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' ORDER BY lastMessageTimestamp DESC")
    fun getChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Get all chats for a user.
     *
     * @param userId The user ID.
     * @return All chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' ORDER BY lastMessageTimestamp DESC")
    suspend fun getChatsForUser(userId: String): List<Chat>

    /**
     * Delete a chat.
     *
     * @param chatId The chat ID.
     */
    @Query("DELETE FROM chats WHERE id = :chatId")
    suspend fun deleteChat(chatId: String)

    /**
     * Archive a chat.
     *
     * @param chatId The chat ID.
     * @param isArchived Whether the chat is archived.
     */
    @Query("UPDATE chats SET isArchived = :isArchived WHERE id = :chatId")
    suspend fun updateChatArchiveStatus(chatId: String, isArchived: Boolean)

    /**
     * Mute a chat.
     *
     * @param chatId The chat ID.
     * @param isMuted Whether the chat is muted.
     * @param mutedUntil The timestamp until which the chat is muted, or null for indefinite.
     */
    @Query("UPDATE chats SET isMuted = :isMuted, mutedUntil = :mutedUntil WHERE id = :chatId")
    suspend fun updateChatMuteStatus(chatId: String, isMuted: Boolean, mutedUntil: Long?)

    /**
     * Get all non-archived chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all non-archived chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' AND isArchived = 0 ORDER BY lastMessageTimestamp DESC")
    fun getNonArchivedChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Get all archived chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all archived chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' AND isArchived = 1 ORDER BY lastMessageTimestamp DESC")
    fun getArchivedChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Get all group chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all group chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' AND conversationType = 'group' ORDER BY lastMessageTimestamp DESC")
    fun getGroupChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Get all channel chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all channel chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' AND conversationType = 'channel' ORDER BY lastMessageTimestamp DESC")
    fun getChannelChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Get all one-to-one chats for a user.
     *
     * @param userId The user ID.
     * @return A flow of all one-to-one chats for the user, ordered by last message timestamp.
     */
    @Query("SELECT * FROM chats WHERE participantIds LIKE '%' || :userId || '%' AND conversationType = 'one_to_one' ORDER BY lastMessageTimestamp DESC")
    fun getOneToOneChatsForUserFlow(userId: String): Flow<List<Chat>>

    /**
     * Update a chat's name.
     *
     * @param chatId The chat ID.
     * @param name The new name.
     */
    @Query("UPDATE chats SET name = :name WHERE id = :chatId")
    suspend fun updateChatName(chatId: String, name: String)

    /**
     * Update a chat's description.
     *
     * @param chatId The chat ID.
     * @param description The new description.
     */
    @Query("UPDATE chats SET description = :description WHERE id = :chatId")
    suspend fun updateChatDescription(chatId: String, description: String)

    /**
     * Update a chat's avatar URL.
     *
     * @param chatId The chat ID.
     * @param avatarUrl The new avatar URL.
     */
    @Query("UPDATE chats SET avatarUrl = :avatarUrl WHERE id = :chatId")
    suspend fun updateChatAvatarUrl(chatId: String, avatarUrl: String)

    /**
     * Update a chat's admin IDs.
     *
     * @param chatId The chat ID.
     * @param adminIds The new admin IDs.
     */
    @Query("UPDATE chats SET adminIds = :adminIds WHERE id = :chatId")
    suspend fun updateChatAdminIds(chatId: String, adminIds: String)

    /**
     * Update a chat's participant IDs.
     *
     * @param chatId The chat ID.
     * @param participantIds The new participant IDs.
     */
    @Query("UPDATE chats SET participantIds = :participantIds WHERE id = :chatId")
    suspend fun updateChatParticipantIds(chatId: String, participantIds: String)

    /**
     * Update a chat's pinned status.
     *
     * @param chatId The chat ID.
     * @param isPinned Whether the chat is pinned.
     */
    @Query("UPDATE chats SET isPinned = :isPinned WHERE id = :chatId")
    suspend fun updateChatPinnedStatus(chatId: String, isPinned: Boolean)
}
