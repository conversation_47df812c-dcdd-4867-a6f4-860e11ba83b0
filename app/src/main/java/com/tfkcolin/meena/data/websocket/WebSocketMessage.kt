package com.tfkcolin.meena.data.websocket

import com.google.gson.JsonElement

/**
 * WebSocket message types.
 */
enum class WebSocketMessageType {
    // Connection messages
    CONNECT,
    CONNECT_ACK,
    DISCONNECT,

    // Chat messages
    MESSAGE,
    MESSAGE_DELIVERED,
    MESSAGE_READ,
    MESSAGE_TYPING,
    MESSAGE_EDIT,
    MESSAGE_DELETE,

    // Reaction messages
    MESSAGE_REACTION_ADD,
    MESSAGE_REACTION_REMOVE,

    // Reply messages
    MESSAGE_REPLY,

    // Presence messages
    PRESENCE_UPDATE,

    // Error messages
    ERROR
}

/**
 * WebSocket message model.
 *
 * @param type The message type.
 * @param payload The message payload.
 * @param id The message ID (optional).
 * @param timestamp The message timestamp (optional).
 */
data class WebSocketMessage(
    val type: WebSocketMessageType,
    val payload: JsonElement?,
    val id: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Chat message payload.
 *
 * @param chatId The chat ID.
 * @param senderId The sender ID.
 * @param recipientId The recipient ID.
 * @param content The message content.
 * @param contentType The message content type.
 * @param mediaUrl The media URL (optional).
 */
data class ChatMessagePayload(
    val chatId: String,
    val senderId: String,
    val recipientId: String,
    val content: String,
    val contentType: String = "text",
    val mediaUrl: String? = null
)

/**
 * Message status payload.
 *
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param status The message status.
 */
data class MessageStatusPayload(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val status: String // "delivered" or "read"
)

/**
 * Typing indicator payload.
 *
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param isTyping Whether the user is typing.
 */
data class TypingPayload(
    val chatId: String,
    val userId: String,
    val isTyping: Boolean
)

/**
 * Presence update payload.
 *
 * @param userId The user ID.
 * @param status The presence status.
 * @param lastSeen The last seen timestamp.
 */
data class PresencePayload(
    val userId: String,
    val status: String, // "online", "offline", "away"
    val lastSeen: Long? = null
)

/**
 * Error payload.
 *
 * @param code The error code.
 * @param message The error message.
 */
data class ErrorPayload(
    val code: Int,
    val message: String
)

/**
 * Message edit payload.
 *
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param newContent The new content.
 */
data class MessageEditPayload(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val newContent: String
)

/**
 * Message deletion payload.
 *
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param deleteType The delete type (self or everyone).
 */
data class MessageDeletionPayload(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val deleteType: String // "self" or "everyone"
)

/**
 * Message reaction payload.
 *
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param userId The user ID.
 * @param emoji The emoji reaction.
 */
data class MessageReactionPayload(
    val messageId: String,
    val chatId: String,
    val userId: String,
    val emoji: String
)

/**
 * Message reply payload.
 *
 * @param messageId The message ID.
 * @param chatId The chat ID.
 * @param senderId The sender ID.
 * @param recipientId The recipient ID.
 * @param content The message content.
 * @param contentType The message content type.
 * @param mediaUrl The media URL (optional).
 * @param replyToMessageId The ID of the message being replied to.
 */
data class MessageReplyPayload(
    val messageId: String,
    val chatId: String,
    val senderId: String,
    val recipientId: String,
    val content: String,
    val contentType: String = "text",
    val mediaUrl: String? = null,
    val replyToMessageId: String
)
