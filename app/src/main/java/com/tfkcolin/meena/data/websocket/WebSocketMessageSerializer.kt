package com.tfkcolin.meena.data.websocket

import com.google.gson.Gson
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Serializer for WebSocket messages.
 */
@Singleton
class WebSocketMessageSerializer @Inject constructor(
    private val gson: Gson
) {

    /**
     * Serialize a WebSocket message to JSON.
     *
     * @param message The message to serialize.
     * @return The serialized message.
     */
    fun serialize(message: WebSocketMessage): String {
        val jsonObject = JsonObject()
        jsonObject.addProperty("type", message.type.name)
        message.id?.let { jsonObject.addProperty("id", it) }
        jsonObject.addProperty("timestamp", message.timestamp)
        message.payload?.let { jsonObject.add("payload", it) }

        return gson.toJson(jsonObject)
    }

    /**
     * Deserialize a JSON string to a WebSocket message.
     *
     * @param json The JSON string to deserialize.
     * @return The deserialized message.
     */
    fun deserialize(json: String): WebSocketMessage {
        // Handle empty or invalid JSON
        if (json.isBlank()) {
            return WebSocketMessage(
                type = WebSocketMessageType.ERROR,
                id = null,
                timestamp = System.currentTimeMillis(),
                payload = null
            )
        }

        try {
            val jsonObject = gson.fromJson(json, JsonObject::class.java)

            // If jsonObject is null, return a default message
            if (jsonObject == null) {
                return WebSocketMessage(
                    type = WebSocketMessageType.ERROR,
                    id = null,
                    timestamp = System.currentTimeMillis(),
                    payload = null
                )
            }

            // If type is missing, default to ERROR
            val type = if (jsonObject.has("type")) {
                try {
                    WebSocketMessageType.valueOf(jsonObject.get("type").asString)
                } catch (e: Exception) {
                    WebSocketMessageType.ERROR
                }
            } else {
                WebSocketMessageType.ERROR
            }

            val id = if (jsonObject.has("id")) jsonObject.get("id").asString else null
            val timestamp = if (jsonObject.has("timestamp")) jsonObject.get("timestamp").asLong else System.currentTimeMillis()
            val payload = if (jsonObject.has("payload")) jsonObject.get("payload") else null

            return WebSocketMessage(
                type = type,
                id = id,
                timestamp = timestamp,
                payload = payload
            )
        } catch (e: Exception) {
            // If parsing fails, return a default message
            return WebSocketMessage(
                type = WebSocketMessageType.ERROR,
                id = null,
                timestamp = System.currentTimeMillis(),
                payload = null
            )
        }
    }

    /**
     * Create a chat message.
     *
     * @param chatId The chat ID.
     * @param senderId The sender ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @return The WebSocket message.
     */
    fun createChatMessage(
        chatId: String,
        senderId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ): WebSocketMessage {
        val payload = ChatMessagePayload(
            chatId = chatId,
            senderId = senderId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = WebSocketMessageType.MESSAGE,
            payload = payloadJson,
            id = generateMessageId()
        )
    }

    /**
     * Create a message status update.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @param status The message status.
     * @return The WebSocket message.
     */
    fun createMessageStatus(
        messageId: String,
        chatId: String,
        userId: String,
        status: String
    ): WebSocketMessage {
        val payload = MessageStatusPayload(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            status = status
        )

        val payloadJson = gson.toJsonTree(payload)

        val type = when (status) {
            "delivered" -> WebSocketMessageType.MESSAGE_DELIVERED
            "read" -> WebSocketMessageType.MESSAGE_READ
            else -> throw IllegalArgumentException("Invalid message status: $status")
        }

        return WebSocketMessage(
            type = type,
            payload = payloadJson
        )
    }

    /**
     * Create a typing indicator.
     *
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @param isTyping Whether the user is typing.
     * @return The WebSocket message.
     */
    fun createTypingIndicator(
        chatId: String,
        userId: String,
        isTyping: Boolean
    ): WebSocketMessage {
        val payload = TypingPayload(
            chatId = chatId,
            userId = userId,
            isTyping = isTyping
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = WebSocketMessageType.MESSAGE_TYPING,
            payload = payloadJson
        )
    }

    /**
     * Create a message edit notification.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @param newContent The new content.
     * @return The WebSocket message.
     */
    fun createMessageEdit(
        messageId: String,
        chatId: String,
        userId: String,
        newContent: String
    ): WebSocketMessage {
        val payload = MessageEditPayload(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            newContent = newContent
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = WebSocketMessageType.MESSAGE_EDIT,
            payload = payloadJson
        )
    }

    /**
     * Create a message deletion notification.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @param deleteType The delete type (self or everyone).
     * @return The WebSocket message.
     */
    fun createMessageDeletion(
        messageId: String,
        chatId: String,
        userId: String,
        deleteType: String
    ): WebSocketMessage {
        val payload = MessageDeletionPayload(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            deleteType = deleteType
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = WebSocketMessageType.MESSAGE_DELETE,
            payload = payloadJson
        )
    }

    /**
     * Create a message reaction notification.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param userId The user ID.
     * @param emoji The emoji reaction.
     * @param isAdd Whether to add (true) or remove (false) the reaction.
     * @return The WebSocket message.
     */
    fun createMessageReaction(
        messageId: String,
        chatId: String,
        userId: String,
        emoji: String,
        isAdd: Boolean
    ): WebSocketMessage {
        val payload = MessageReactionPayload(
            messageId = messageId,
            chatId = chatId,
            userId = userId,
            emoji = emoji
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = if (isAdd) WebSocketMessageType.MESSAGE_REACTION_ADD else WebSocketMessageType.MESSAGE_REACTION_REMOVE,
            payload = payloadJson
        )
    }

    /**
     * Create a message reply.
     *
     * @param messageId The message ID.
     * @param chatId The chat ID.
     * @param senderId The sender ID.
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     * @param replyToMessageId The ID of the message being replied to.
     * @return The WebSocket message.
     */
    fun createMessageReply(
        messageId: String,
        chatId: String,
        senderId: String,
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null,
        replyToMessageId: String
    ): WebSocketMessage {
        val payload = MessageReplyPayload(
            messageId = messageId,
            chatId = chatId,
            senderId = senderId,
            recipientId = recipientId,
            content = content,
            contentType = contentType,
            mediaUrl = mediaUrl,
            replyToMessageId = replyToMessageId
        )

        val payloadJson = gson.toJsonTree(payload)

        return WebSocketMessage(
            type = WebSocketMessageType.MESSAGE_REPLY,
            payload = payloadJson,
            id = messageId
        )
    }

    /**
     * Generate a unique message ID.
     *
     * @return The message ID.
     */
    private fun generateMessageId(): String {
        return java.util.UUID.randomUUID().toString()
    }
}
