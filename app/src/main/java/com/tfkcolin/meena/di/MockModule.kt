package com.tfkcolin.meena.di

import android.content.Context
import com.google.gson.Gson
import com.tfkcolin.meena.mock.ai.AIResponseGenerator
import com.tfkcolin.meena.mock.api.MockAuthApi
import com.tfkcolin.meena.mock.api.MockChatApi
import com.tfkcolin.meena.mock.api.MockContactApi
import com.tfkcolin.meena.mock.storage.MockDataStorage
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing mock backend dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object MockModule {
    
    /**
     * Provide MockDataStorage for persistent mock data.
     */
    @Provides
    @Singleton
    fun provideMockDataStorage(
        @ApplicationContext context: Context,
        gson: Gson
    ): MockDataStorage {
        return MockDataStorage(context, gson)
    }
    
    /**
     * Provide AI response generator for chat simulation.
     */
    @Provides
    @Singleton
    fun provideAIResponseGenerator(): AIResponseGenerator {
        return AIResponseGenerator()
    }
    
    /**
     * Provide MockAuthApi.
     */
    @Provides
    @Singleton
    fun provideMockAuthApi(
        mockDataStorage: MockDataStorage
    ): MockAuthApi {
        return MockAuthApi(mockDataStorage)
    }
    
    /**
     * Provide MockContactApi.
     */
    @Provides
    @Singleton
    fun provideMockContactApi(
        mockDataStorage: MockDataStorage,
        mockAuthApi: MockAuthApi
    ): MockContactApi {
        return MockContactApi(mockDataStorage, mockAuthApi)
    }
    
    /**
     * Provide MockChatApi.
     */
    @Provides
    @Singleton
    fun provideMockChatApi(
        mockDataStorage: MockDataStorage,
        mockAuthApi: MockAuthApi,
        aiResponseGenerator: AIResponseGenerator
    ): MockChatApi {
        return MockChatApi(mockDataStorage, mockAuthApi, aiResponseGenerator)
    }
}
