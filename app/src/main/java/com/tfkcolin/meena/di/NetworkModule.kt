package com.tfkcolin.meena.di

import com.google.gson.Gson
import com.tfkcolin.meena.BuildConfig
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.data.api.AuthInterceptor
import com.tfkcolin.meena.data.api.CallApi
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.api.ContactGroupApi
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.api.SupportApi
import com.tfkcolin.meena.data.api.TokenAuthenticator
import com.tfkcolin.meena.data.api.UserApi
import com.tfkcolin.meena.mock.api.MockAuthApi
import com.tfkcolin.meena.mock.api.MockCallApi
import com.tfkcolin.meena.mock.api.MockChatApi
import com.tfkcolin.meena.mock.api.MockContactApi
import com.tfkcolin.meena.mock.api.MockContactGroupApi
import com.tfkcolin.meena.mock.api.MockMediaApi
import com.tfkcolin.meena.mock.api.MockSupportApi
import com.tfkcolin.meena.mock.api.MockUserApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * Hilt module for providing network layer dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    // Base URL for the API - now determined by configuration
    private val BASE_URL: String
        get() = AppConfig.baseUrl

    // Timeouts
    private const val CONNECT_TIMEOUT = 15L
    private const val READ_TIMEOUT = 30L
    private const val WRITE_TIMEOUT = 30L

    /**
     * Provide a basic OkHttpClient without auth interceptor or authenticator.
     * This is used to create the AuthApi to avoid circular dependencies.
     */
    @Provides
    @Singleton
    @Named("basicOkHttpClient")
    fun provideBasicOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .also {
                // Add logging interceptor in debug builds
                if (BuildConfig.DEBUG) {
                    val loggingInterceptor = HttpLoggingInterceptor().apply {
                        level = HttpLoggingInterceptor.Level.BODY
                    }
                    it.addInterceptor(loggingInterceptor)
                }
            }
            .build()
    }

    /**
     * Provide a basic Retrofit instance without auth interceptor or authenticator.
     * This is used to create the AuthApi to avoid circular dependencies.
     */
    @Provides
    @Singleton
    @Named("basicRetrofit")
    fun provideBasicRetrofit(@Named("basicOkHttpClient") okHttpClient: OkHttpClient, gson: Gson): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }



    /**
     * Provide the full OkHttpClient with auth interceptor and authenticator.
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(
        authInterceptor: AuthInterceptor,
        tokenAuthenticator: TokenAuthenticator
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(authInterceptor)
            .authenticator(tokenAuthenticator)
            .also {
                // Add logging interceptor in debug builds
                if (BuildConfig.DEBUG) {
                    val loggingInterceptor = HttpLoggingInterceptor().apply {
                        level = HttpLoggingInterceptor.Level.BODY
                    }
                    it.addInterceptor(loggingInterceptor)
                }
            }
            .build()
    }

    /**
     * Provide the full Retrofit instance with auth interceptor and authenticator.
     */
    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient, gson: Gson): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    /**
     * Provide the ContactApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideContactApi(
        retrofit: Retrofit,
        mockContactApi: MockContactApi
    ): ContactApi {
        return if (AppConfig.useMockBackend) {
            mockContactApi
        } else {
            retrofit.create(ContactApi::class.java)
        }
    }

    /**
     * Provide the ChatApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideChatApi(
        retrofit: Retrofit,
        mockChatApi: MockChatApi
    ): ChatApi {
        return if (AppConfig.useMockBackend) {
            mockChatApi
        } else {
            retrofit.create(ChatApi::class.java)
        }
    }

    /**
     * Provide the MediaApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideMediaApi(
        retrofit: Retrofit,
        mockMediaApi: MockMediaApi
    ): MediaApi {
        return if (AppConfig.useMockBackend) {
            mockMediaApi
        } else {
            retrofit.create(MediaApi::class.java)
        }
    }

    /**
     * Provide the AuthApi - mock or real based on configuration.
     * Uses basic retrofit to avoid circular dependency with TokenAuthenticator.
     */
    @Provides
    @Singleton
    fun provideAuthApi(
        @Named("basicRetrofit") basicRetrofit: Retrofit,
        mockAuthApi: MockAuthApi
    ): AuthApi {
        return if (AppConfig.useMockBackend) {
            mockAuthApi
        } else {
            basicRetrofit.create(AuthApi::class.java)
        }
    }

    /**
     * Provide the UserApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideUserApi(
        retrofit: Retrofit,
        mockUserApi: MockUserApi
    ): UserApi {
        return if (AppConfig.useMockBackend) {
            mockUserApi
        } else {
            retrofit.create(UserApi::class.java)
        }
    }

    /**
     * Provide the CallApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideCallApi(
        retrofit: Retrofit,
        mockCallApi: MockCallApi
    ): CallApi {
        return if (AppConfig.useMockBackend) {
            mockCallApi
        } else {
            retrofit.create(CallApi::class.java)
        }
    }

    /**
     * Provide the SupportApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideSupportApi(
        retrofit: Retrofit,
        mockSupportApi: MockSupportApi
    ): SupportApi {
        return if (AppConfig.useMockBackend) {
            mockSupportApi
        } else {
            retrofit.create(SupportApi::class.java)
        }
    }

    /**
     * Provide the ContactGroupApi - mock or real based on configuration.
     */
    @Provides
    @Singleton
    fun provideContactGroupApi(
        retrofit: Retrofit,
        mockContactGroupApi: MockContactGroupApi
    ): ContactGroupApi {
        return if (AppConfig.useMockBackend) {
            mockContactGroupApi
        } else {
            retrofit.create(ContactGroupApi::class.java)
        }
    }
}
