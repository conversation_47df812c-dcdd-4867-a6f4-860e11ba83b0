package com.tfkcolin.meena.di

import com.tfkcolin.meena.data.api.AuthApi
import com.tfkcolin.meena.data.api.CallApi
import com.tfkcolin.meena.data.api.ChatApi
import com.tfkcolin.meena.data.api.ContactApi
import com.tfkcolin.meena.data.api.ContactGroupApi
import com.tfkcolin.meena.data.api.MediaApi
import com.tfkcolin.meena.data.api.SupportApi
import com.tfkcolin.meena.data.api.UserApi
import com.tfkcolin.meena.mock.api.MockAuthApi
import com.tfkcolin.meena.mock.api.MockCallApi
import com.tfkcolin.meena.mock.api.MockChatApi
import com.tfkcolin.meena.mock.api.MockContactApi
import com.tfkcolin.meena.mock.api.MockContactGroupApi
import com.tfkcolin.meena.mock.api.MockMediaApi
import com.tfkcolin.meena.mock.api.MockSupportApi
import com.tfkcolin.meena.mock.api.MockUserApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing network layer dependencies.
 * This branch only provides mock implementations.
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    /**
     * Provide the ContactApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideContactApi(mockContactApi: MockContactApi): ContactApi = mockContactApi

    /**
     * Provide the ChatApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideChatApi(mockChatApi: MockChatApi): ChatApi = mockChatApi

    /**
     * Provide the MediaApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideMediaApi(mockMediaApi: MockMediaApi): MediaApi = mockMediaApi

    /**
     * Provide the AuthApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideAuthApi(mockAuthApi: MockAuthApi): AuthApi = mockAuthApi

    /**
     * Provide the UserApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideUserApi(mockUserApi: MockUserApi): UserApi = mockUserApi

    /**
     * Provide the CallApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideCallApi(mockCallApi: MockCallApi): CallApi = mockCallApi

    /**
     * Provide the SupportApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideSupportApi(mockSupportApi: MockSupportApi): SupportApi = mockSupportApi

    /**
     * Provide the ContactGroupApi - always mock implementation.
     */
    @Provides
    @Singleton
    fun provideContactGroupApi(mockContactGroupApi: MockContactGroupApi): ContactGroupApi = mockContactGroupApi
}
