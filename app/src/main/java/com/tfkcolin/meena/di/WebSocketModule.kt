package com.tfkcolin.meena.di

import com.google.gson.Gson
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.data.websocket.WebSocketClient
import com.tfkcolin.meena.data.websocket.WebSocketMessageSerializer
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Singleton

/**
 * Hilt module for providing WebSocket dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
object WebSocketModule {
    
    /**
     * Provide the WebSocket message serializer.
     */
    @Provides
    @Singleton
    fun provideWebSocketMessageSerializer(gson: Gson): WebSocketMessageSerializer {
        return WebSocketMessageSerializer(gson)
    }
    
    /**
     * Provide the WebSocket client.
     */
    @Provides
    @Singleton
    fun provideWebSocketClient(
        okHttpClient: OkHttpClient,
        tokenManager: TokenManager,
        messageSerializer: WebSocketMessageSerializer
    ): WebSocketClient {
        return WebSocketClient(okHttpClient, tokenManager, messageSerializer)
    }
}
