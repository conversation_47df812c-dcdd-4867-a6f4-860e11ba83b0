package com.tfkcolin.meena.config

import com.tfkcolin.meena.BuildConfig

/**
 * Application configuration management.
 * Handles switching between mock and real backend configurations.
 */
object AppConfig {
    
    /**
     * Determines if the app should use mock backend.
     * This can be controlled via build variants or runtime configuration.
     */
    val useMockBackend: Boolean
        get() = BuildConfig.USE_MOCK_BACKEND || _forceMockMode
    
    /**
     * Base URL for the API.
     * Returns mock URL if mock mode is enabled, otherwise real URL.
     *
     * Note: In mock mode, this can point to either:
     * - Local mock server (for pure offline development)
     * - Live backend server (for testing with real data)
     * - Staging server (for integration testing)
     */
    val baseUrl: String
        get() = if (useMockBackend) {
            BuildConfig.MOCK_BASE_URL
        } else {
            BuildConfig.REAL_BASE_URL
        }
    
    /**
     * WebSocket URL for real-time communication.
     */
    val webSocketUrl: String
        get() = if (useMockBackend) {
            "ws://localhost:8080/ws"
        } else {
            "wss://socialmediabackend-production-57c8.up.railway.app/ws"
        }
    
    /**
     * Media upload base URL.
     */
    val mediaUploadUrl: String
        get() = if (useMockBackend) {
            "http://localhost:8080/api/v1/media"
        } else {
            "https://socialmediabackend-production-57c8.up.railway.app/api/v1/media"
        }
    
    // Runtime configuration override
    private var _forceMockMode: Boolean = false
    
    /**
     * Force mock mode at runtime (useful for testing).
     * This overrides build configuration.
     */
    fun setMockMode(enabled: Boolean) {
        _forceMockMode = enabled
    }
    
    /**
     * Configuration for mock backend behavior.
     */
    object MockConfig {
        // Simulated network delays (in milliseconds)
        const val NETWORK_DELAY_MIN = 200L
        const val NETWORK_DELAY_MAX = 1000L
        
        // AI response configuration
        const val AI_RESPONSE_DELAY_MIN = 1000L
        const val AI_RESPONSE_DELAY_MAX = 3000L
        
        // Mock data persistence
        const val PERSIST_MOCK_DATA = true
        
        // Enable realistic error simulation
        const val SIMULATE_NETWORK_ERRORS = true
        const val ERROR_RATE_PERCENTAGE = 5 // 5% chance of network errors
        
        // Mock user configuration
        const val DEFAULT_MOCK_USERS_COUNT = 20
        const val DEFAULT_MOCK_CHATS_COUNT = 10
        const val DEFAULT_MOCK_MESSAGES_PER_CHAT = 50
        
        // AI personas configuration
        val AI_PERSONAS = listOf(
            "friendly_assistant",
            "tech_enthusiast", 
            "casual_friend",
            "professional_colleague",
            "humorous_buddy",
            "supportive_mentor"
        )
        
        // Story simulation
        const val MOCK_STORIES_COUNT = 15
        const val STORY_DURATION_HOURS = 24
        
        // Call simulation
        const val SIMULATE_INCOMING_CALLS = true
        const val CALL_SIMULATION_INTERVAL_MINUTES = 30
    }
    
    /**
     * Development and testing configuration.
     */
    object DevConfig {
        // Enable debug logging for mock backend
        const val ENABLE_MOCK_LOGGING = true
        
        // Enable detailed API request/response logging
        const val ENABLE_API_LOGGING = true
        
        // Enable mock data reset on app start
        const val RESET_MOCK_DATA_ON_START = false
        
        // Enable developer menu in app
        const val ENABLE_DEV_MENU = true
    }
    
    /**
     * Feature flags for controlling app behavior.
     */
    object FeatureFlags {
        // Enable/disable specific features in mock mode
        const val ENABLE_STORIES = true
        const val ENABLE_CALLS = true
        const val ENABLE_GROUP_CHATS = true
        const val ENABLE_CHANNELS = true
        const val ENABLE_MEDIA_MESSAGES = true
        const val ENABLE_MESSAGE_REACTIONS = true
        const val ENABLE_MESSAGE_REPLIES = true
        const val ENABLE_CONTACT_GROUPS = true
        const val ENABLE_PROFILE_VERIFICATION = true
        const val ENABLE_GOLD_SUBSCRIPTION = true
        
        // AI simulation features
        const val ENABLE_AI_CHAT_RESPONSES = true
        const val ENABLE_TYPING_INDICATORS = true
        const val ENABLE_READ_RECEIPTS = true
        const val ENABLE_ONLINE_STATUS = true
    }
}
