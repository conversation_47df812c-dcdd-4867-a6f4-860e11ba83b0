package com.tfkcolin.meena.mock

import android.content.Context
import androidx.startup.Initializer
import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.mock.storage.MockDataStorage
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Initializes mock data when the app starts in mock mode.
 */
@Singleton
class MockInitializer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val mockDataStorage: MockDataStorage
) {
    
    private val initializationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * Initialize mock data if needed.
     */
    fun initialize() {
        if (AppConfig.useMockBackend) {
            initializationScope.launch {
                try {
                    mockDataStorage.initializeIfNeeded()
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Mock data initialized successfully")
                    }
                } catch (e: Exception) {
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Error initializing mock data: ${e.message}")
                    }
                }
            }
        }
    }
    
    /**
     * Reset mock data (useful for testing).
     */
    fun resetMockData() {
        if (AppConfig.useMockBackend) {
            initializationScope.launch {
                try {
                    mockDataStorage.clearAllData()
                    mockDataStorage.initializeIfNeeded()
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Mock data reset successfully")
                    }
                } catch (e: Exception) {
                    if (AppConfig.DevConfig.ENABLE_MOCK_LOGGING) {
                        println("Error resetting mock data: ${e.message}")
                    }
                }
            }
        }
    }
    
    /**
     * Get current mock user for testing purposes.
     */
    fun getCurrentMockUser() = mockDataStorage.getCurrentUser()
    
    /**
     * Get all mock users for testing purposes.
     */
    fun getAllMockUsers() = mockDataStorage.getUsers()
    
    /**
     * Get mock chats for testing purposes.
     */
    fun getMockChats() = mockDataStorage.getChats()
}

/**
 * App Startup initializer for mock data.
 */
class MockDataInitializer : Initializer<MockInitializer> {
    
    override fun create(context: Context): MockInitializer {
        // Get the MockInitializer from Hilt
        val entryPoint = EntryPointAccessors.fromApplication(
            context,
            MockInitializerEntryPoint::class.java
        )
        
        val mockInitializer = entryPoint.mockInitializer()
        mockInitializer.initialize()
        
        return mockInitializer
    }
    
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}

/**
 * Hilt entry point for accessing MockInitializer during app startup.
 */
@dagger.hilt.EntryPoint
@dagger.hilt.InstallIn(dagger.hilt.components.SingletonComponent::class)
interface MockInitializerEntryPoint {
    fun mockInitializer(): MockInitializer
}
