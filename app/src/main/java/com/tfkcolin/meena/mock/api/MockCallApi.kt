package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.*
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of CallApi for testing and development.
 */
@Singleton
class MockCallApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : CallApi {
    
    override suspend fun getCallLogs(
        limit: Int,
        offset: Int
    ): Response<CallLogsResponse> {
        simulateNetworkDelay()
        
        try {
            // Generate mock call logs
            val callLogs = generateMockCallLogs(limit, offset)
            
            val response = CallLogsResponse(
                call_logs = callLogs,
                total_count = 50, // Mock total count
                limit = limit,
                offset = offset
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get call logs: ${e.message}")
        }
    }
    
    override suspend fun initiateCall(request: InitiateCallRequest): Response<InitiateCallResponse> {
        simulateNetworkDelay()
        
        try {
            // Validate the callee exists
            val users = mockDataStorage.getUsers()
            val callee = users.values.find { it.userHandle == request.callee_user_handle }
                ?: return createErrorResponse(404, "User not found")
            
            // Generate mock call session
            val callId = UUID.randomUUID().toString()
            val token = "mock_call_token_${Random.nextInt(10000, 99999)}"
            
            val response = InitiateCallResponse(
                call_id = callId,
                signaling_server_url = "wss://mock-signaling.example.com/ws",
                ice_servers = generateMockIceServers(),
                token = token
            )
            
            // Store call log
            storeCallLog(callId, callee.userId, request.call_type, "initiated")
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to initiate call: ${e.message}")
        }
    }
    
    override suspend fun endCall(
        callId: String,
        request: EndCallRequest
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            // Update call log with duration
            updateCallLog(callId, "completed", request.duration_seconds)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to end call: ${e.message}")
        }
    }
    
    // Helper methods
    private fun generateMockCallLogs(limit: Int, offset: Int): List<CallLog> {
        val users = mockDataStorage.getUsers().values.toList()
        val callLogs = mutableListOf<CallLog>()
        
        val callTypes = listOf("audio", "video")
        val callStatuses = listOf("completed", "missed", "rejected", "in_progress")
        
        repeat(limit) { index ->
            val otherUser = users.randomOrNull() ?: return@repeat
            val callType = callTypes.random()
            val status = callStatuses.random()
            val startTime = System.currentTimeMillis() - Random.nextLong(0, 7 * 24 * 60 * 60 * 1000) // Last 7 days
            val duration = if (status == "completed") Random.nextInt(30, 3600) else null // 30 seconds to 1 hour
            
            val callLog = CallLog(
                call_log_id = UUID.randomUUID().toString(),
                other_party = UserInfo(
                    user_id = otherUser.userId,
                    user_handle = otherUser.userHandle,
                    display_name = otherUser.displayName,
                    avatar_url = otherUser.profilePictureUrl
                ),
                type = callType,
                status = status,
                start_time = startTime,
                duration_seconds = duration
            )
            
            callLogs.add(callLog)
        }
        
        return callLogs.sortedByDescending { it.start_time }
    }
    
    private fun generateMockIceServers(): List<IceServer> {
        return listOf(
            IceServer(
                urls = listOf("stun:stun.l.google.com:19302"),
                username = null,
                credential = null
            ),
            IceServer(
                urls = listOf("turn:mock-turn.example.com:3478"),
                username = "mock_user",
                credential = "mock_credential"
            )
        )
    }
    
    private fun storeCallLog(
        callId: String,
        otherPartyId: String,
        callType: String,
        status: String
    ) {
        // In a real implementation, this would store the call log in the database
        // For mock purposes, we'll just simulate this
    }
    
    private fun updateCallLog(
        callId: String,
        status: String,
        durationSeconds: Int
    ) {
        // In a real implementation, this would update the call log in the database
        // For mock purposes, we'll just simulate this
    }
    
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}

/**
 * User info for call logs.
 */
data class UserInfo(
    val user_id: String,
    val user_handle: String,
    val display_name: String,
    val avatar_url: String?
)
