package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.ContactGroupApi
import com.tfkcolin.meena.data.models.*
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of ContactGroupApi for testing and development.
 */
@Singleton
class MockContactGroupApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : ContactGroupApi {
    
    private val mockContactGroups = mutableMapOf<String, ContactGroup>()
    
    override suspend fun getContactGroups(
        authToken: String,
        limit: Int,
        offset: Int
    ): Response<ContactGroupListResponse> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            // Generate mock contact groups if none exist
            if (mockContactGroups.isEmpty()) {
                generateMockContactGroups(userId)
            }
            
            val userGroups = mockContactGroups.values
                .filter { it.ownerId == userId }
                .sortedBy { it.name }
            
            val paginatedGroups = userGroups.drop(offset).take(limit)
            
            val response = ContactGroupListResponse(
                contactGroups = paginatedGroups,
                totalCount = userGroups.size,
                limit = limit,
                offset = offset
            )
            
            return Response.success(response)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get contact groups: ${e.message}")
        }
    }
    
    override suspend fun getContactGroupById(
        authToken: String,
        groupId: String
    ): Response<ContactGroup> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val group = mockContactGroups[groupId]
                ?: return createErrorResponse(404, "Contact group not found")
            
            // Check if user owns the group
            if (group.ownerId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            return Response.success(group)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to get contact group: ${e.message}")
        }
    }
    
    override suspend fun createContactGroup(
        authToken: String,
        request: CreateContactGroupRequest
    ): Response<ContactGroup> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val groupId = UUID.randomUUID().toString()
            val now = System.currentTimeMillis()
            
            val contactGroup = ContactGroup(
                id = groupId,
                name = request.name,
                description = request.description,
                ownerId = userId,
                contactIds = request.contactIds ?: emptyList(),
                createdAt = now,
                updatedAt = now
            )
            
            mockContactGroups[groupId] = contactGroup
            
            return Response.success(contactGroup)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to create contact group: ${e.message}")
        }
    }
    
    override suspend fun updateContactGroup(
        authToken: String,
        groupId: String,
        request: UpdateContactGroupRequest
    ): Response<ContactGroup> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val group = mockContactGroups[groupId]
                ?: return createErrorResponse(404, "Contact group not found")
            
            // Check if user owns the group
            if (group.ownerId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            val updatedGroup = group.copy(
                name = request.name ?: group.name,
                description = request.description ?: group.description,
                updatedAt = System.currentTimeMillis()
            )
            
            mockContactGroups[groupId] = updatedGroup
            
            return Response.success(updatedGroup)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to update contact group: ${e.message}")
        }
    }
    
    override suspend fun deleteContactGroup(
        authToken: String,
        groupId: String
    ): Response<Unit> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val group = mockContactGroups[groupId]
                ?: return createErrorResponse(404, "Contact group not found")
            
            // Check if user owns the group
            if (group.ownerId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            mockContactGroups.remove(groupId)
            
            return Response.success(Unit)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to delete contact group: ${e.message}")
        }
    }
    
    override suspend fun addContactToGroup(
        authToken: String,
        groupId: String,
        contactId: String
    ): Response<ContactGroup> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val group = mockContactGroups[groupId]
                ?: return createErrorResponse(404, "Contact group not found")
            
            // Check if user owns the group
            if (group.ownerId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Check if contact exists
            val userContacts = mockDataStorage.getContacts(userId)
            val contactExists = userContacts.any { it.id == contactId }
            if (!contactExists) {
                return createErrorResponse(404, "Contact not found")
            }
            
            // Add contact to group if not already present
            val updatedContactIds = if (contactId in group.contactIds) {
                group.contactIds
            } else {
                group.contactIds + contactId
            }
            
            val updatedGroup = group.copy(
                contactIds = updatedContactIds,
                updatedAt = System.currentTimeMillis()
            )
            
            mockContactGroups[groupId] = updatedGroup
            
            return Response.success(updatedGroup)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to add contact to group: ${e.message}")
        }
    }
    
    override suspend fun removeContactFromGroup(
        authToken: String,
        groupId: String,
        contactId: String
    ): Response<ContactGroup> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val group = mockContactGroups[groupId]
                ?: return createErrorResponse(404, "Contact group not found")
            
            // Check if user owns the group
            if (group.ownerId != userId) {
                return createErrorResponse(403, "Access denied")
            }
            
            // Remove contact from group
            val updatedContactIds = group.contactIds.filter { it != contactId }
            
            val updatedGroup = group.copy(
                contactIds = updatedContactIds,
                updatedAt = System.currentTimeMillis()
            )
            
            mockContactGroups[groupId] = updatedGroup
            
            return Response.success(updatedGroup)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Failed to remove contact from group: ${e.message}")
        }
    }
    
    // Helper methods
    private fun generateMockContactGroups(userId: String) {
        val groupNames = listOf(
            "Family",
            "Work Colleagues",
            "Close Friends",
            "University Friends",
            "Neighbors",
            "Sports Team",
            "Book Club",
            "Project Team"
        )
        
        val userContacts = mockDataStorage.getContacts(userId)
        
        repeat(3) { index ->
            val groupId = UUID.randomUUID().toString()
            val now = System.currentTimeMillis() - Random.nextLong(0, 30 * 24 * 60 * 60 * 1000)
            
            // Randomly assign some contacts to the group
            val groupContacts = userContacts.shuffled().take(Random.nextInt(2, 6))
            
            val contactGroup = ContactGroup(
                id = groupId,
                name = groupNames.getOrNull(index) ?: "Group ${index + 1}",
                description = "Mock contact group for testing",
                ownerId = userId,
                contactIds = groupContacts.map { it.id },
                createdAt = now,
                updatedAt = now
            )
            
            mockContactGroups[groupId] = contactGroup
        }
    }
    
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
