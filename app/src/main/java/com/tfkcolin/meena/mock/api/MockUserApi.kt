package com.tfkcolin.meena.mock.api

import com.tfkcolin.meena.config.AppConfig
import com.tfkcolin.meena.data.api.UserApi
import com.tfkcolin.meena.data.models.UpdateProfileRequest
import com.tfkcolin.meena.data.models.User
import com.tfkcolin.meena.mock.storage.MockDataStorage
import kotlinx.coroutines.delay
import okhttp3.MediaType.Companion.toMediaType
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Mock implementation of UserApi for testing and development.
 */
@Singleton
class MockUserApi @Inject constructor(
    private val mockDataStorage: MockDataStorage,
    private val mockAuthApi: MockAuthApi
) : UserApi {
    
    override suspend fun getUserById(userId: String): Response<User> {
        simulateNetworkDelay()
        
        try {
            val user = mockDataStorage.getUser(userId)
                ?: return createErrorResponse(404, "User not found")
            
            return Response.success(user)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun getUserByHandle(userHandle: String): Response<User> {
        simulateNetworkDelay()
        
        try {
            val users = mockDataStorage.getUsers()
            val user = users.values.find { it.userHandle == userHandle }
                ?: return createErrorResponse(404, "User not found")
            
            return Response.success(user)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun getCurrentUserProfile(authToken: String): Response<User> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val user = mockDataStorage.getUser(userId)
                ?: return createErrorResponse(404, "User not found")
            
            return Response.success(user)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    override suspend fun updateProfile(
        authToken: String,
        request: UpdateProfileRequest
    ): Response<User> {
        simulateNetworkDelay()
        
        try {
            val userId = extractUserIdFromToken(authToken)
                ?: return createErrorResponse(401, "Unauthorized")
            
            val currentUser = mockDataStorage.getUser(userId)
                ?: return createErrorResponse(404, "User not found")
            
            // Update user profile with new information
            val updatedUser = currentUser.copy(
                displayName = request.displayName ?: currentUser.displayName,
                bio = request.bio ?: currentUser.bio,
                profilePictureUrl = request.avatarUrl ?: currentUser.profilePictureUrl,
                email = request.email ?: currentUser.email,
                phoneNumber = request.phoneNumber ?: currentUser.phoneNumber
            )
            
            // Save updated user
            mockDataStorage.updateUser(updatedUser)
            
            return Response.success(updatedUser)
            
        } catch (e: Exception) {
            return createErrorResponse(500, "Internal server error: ${e.message}")
        }
    }
    
    // Helper methods
    private suspend fun simulateNetworkDelay() {
        if (AppConfig.MockConfig.NETWORK_DELAY_MIN > 0) {
            val delay = Random.nextLong(
                AppConfig.MockConfig.NETWORK_DELAY_MIN,
                AppConfig.MockConfig.NETWORK_DELAY_MAX
            )
            delay(delay)
        }
    }
    
    private fun extractUserIdFromToken(authToken: String): String? {
        val token = authToken.removePrefix("Bearer ").trim()
        return mockAuthApi.validateToken(token)
    }
    
    private fun <T> createErrorResponse(code: Int, message: String): Response<T> {
        return Response.error(
            code,
            okhttp3.ResponseBody.create(
                "application/json".toMediaType(),
                """{"error": "$message", "status": $code}"""
            )
        )
    }
}
