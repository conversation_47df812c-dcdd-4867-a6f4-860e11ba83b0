package com.tfkcolin.meena.mock.data

import com.tfkcolin.meena.data.models.*
import java.util.*
import kotlin.random.Random

/**
 * Generates realistic mock data for the application.
 */
object MockDataGenerator {
    
    private val firstNames = listOf(
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sydney"
    )
    
    private val lastNames = listOf(
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    )
    
    private val bios = listOf(
        "Love traveling and photography 📸",
        "Coffee enthusiast ☕ | Tech lover 💻",
        "Fitness enthusiast 💪 | Healthy living",
        "Artist at heart 🎨 | Creative soul",
        "Music lover 🎵 | Concert goer",
        "Foodie 🍕 | Always hungry",
        "Book worm 📚 | Knowledge seeker",
        "Adventure seeker 🏔️ | Outdoor enthusiast",
        "Gamer 🎮 | Digital native",
        "Entrepreneur 💼 | Building the future"
    )
    
    private val messageTemplates = listOf(
        "Hey! How's your day going?",
        "Just saw the most amazing sunset! 🌅",
        "Working on something exciting today",
        "Coffee break time! ☕",
        "Have you seen the latest news?",
        "Planning something fun for the weekend",
        "Just finished a great workout 💪",
        "Reading an interesting book right now",
        "The weather is perfect today!",
        "Trying out a new recipe 👨‍🍳"
    )
    
    private val groupNames = listOf(
        "Study Group", "Weekend Warriors", "Coffee Lovers", "Tech Talk",
        "Fitness Buddies", "Book Club", "Travel Squad", "Foodies United",
        "Gaming Crew", "Music Enthusiasts", "Art Collective", "Startup Ideas",
        "Movie Night", "Hiking Group", "Photography Club", "Cooking Class"
    )
    
    private val channelNames = listOf(
        "Tech News", "Daily Updates", "Announcements", "General Discussion",
        "Random Thoughts", "Inspiration", "Motivation Monday", "Tips & Tricks",
        "Industry Insights", "Community Updates", "Events & Meetups", "Q&A",
        "Feedback", "Suggestions", "Help & Support", "Resources"
    )
    
    /**
     * Generate a mock user with realistic data.
     */
    fun generateMockUser(id: String? = null): User {
        val userId = id ?: UUID.randomUUID().toString()
        val firstName = firstNames.random()
        val lastName = lastNames.random()
        val userHandle = "${firstName.lowercase()}${lastName.lowercase()}${Random.nextInt(100, 999)}"
        
        return User(
            userId = userId,
            userHandle = userHandle,
            displayName = "$firstName $lastName",
            bio = bios.random(),
            profilePictureUrl = "https://i.pravatar.cc/150?u=$userId",
            email = "$<EMAIL>",
            phoneNumber = "+1${Random.nextInt(100, 999)}${Random.nextInt(100, 999)}${Random.nextInt(1000, 9999)}",
            subscriptionTier = if (Random.nextFloat() < 0.3f) "gold" else "free",
            verificationStatus = if (Random.nextBoolean()) "verified" else "none",
            isActive = true,
            createdAt = (System.currentTimeMillis() - Random.nextLong(86400000, 31536000000)).toString(),
            lastSeenAt = (System.currentTimeMillis() - Random.nextLong(0, 86400000)).toString()
        )
    }
    
    /**
     * Generate a mock contact relationship.
     */
    fun generateMockContact(userId: String, contactUser: User): Contact {
        return Contact(
            id = UUID.randomUUID().toString(),
            userId = userId,
            contactId = contactUser.userId,
            displayName = if (Random.nextFloat() < 0.3f) "${contactUser.displayName} (${listOf("Work", "School", "Gym", "Neighbor").random()})" else contactUser.displayName,
            relationship = "friend",
            notes = if (Random.nextFloat() < 0.2f) "Met at ${listOf("work", "school", "gym", "party", "conference").random()}" else null,
            createdAt = (System.currentTimeMillis() - Random.nextLong(0, 31536000000)).toString(),
            updatedAt = (System.currentTimeMillis() - Random.nextLong(0, 86400000)).toString(),
            isFavorite = Random.nextFloat() < 0.2f, // 20% chance of being favorite
            lastInteractionAt = (System.currentTimeMillis() - Random.nextLong(0, 86400000)).toString(),
            user = UserProfile(
                id = contactUser.userId,
                handle = contactUser.userHandle,
                displayName = contactUser.displayName,
                avatarUrl = contactUser.profilePictureUrl,
                status = if (contactUser.isActive) "online" else "offline",
                lastSeen = contactUser.lastSeenAt,
                bio = contactUser.bio
            )
        )
    }
    
    /**
     * Generate a mock chat/conversation.
     */
    fun generateMockChat(
        id: String? = null,
        participants: List<String>,
        type: ConversationType = ConversationType.ONE_TO_ONE,
        name: String? = null
    ): Chat {
        val chatId = id ?: UUID.randomUUID().toString()
        val chatName = when (type) {
            ConversationType.GROUP -> name ?: groupNames.random()
            ConversationType.CHANNEL -> name ?: channelNames.random()
            ConversationType.ONE_TO_ONE -> null
        }
        
        return Chat(
            id = chatId,
            conversationType = type.value,
            privacyType = when (type) {
                ConversationType.ONE_TO_ONE -> null
                else -> listOf("public", "private", "secret").random()
            },
            participantIds = participants.joinToString(","),
            name = chatName,
            description = if (type != ConversationType.ONE_TO_ONE && Random.nextBoolean()) {
                "A great place to ${listOf("chat", "share ideas", "collaborate", "have fun", "learn together").random()}"
            } else null,
            avatarUrl = if (type != ConversationType.ONE_TO_ONE) "https://i.pravatar.cc/150?u=$chatId" else null,
            adminIds = if (type != ConversationType.ONE_TO_ONE) participants.take(Random.nextInt(1, minOf(3, participants.size))).joinToString(",") else null,
            createdBy = participants.first(),
            lastMessage = messageTemplates.random(),
            lastMessageTimestamp = System.currentTimeMillis() - Random.nextLong(0, 86400000),
            unreadCount = Random.nextInt(0, 10),
            isArchived = Random.nextFloat() < 0.1f, // 10% chance of being archived
            isMuted = Random.nextFloat() < 0.15f, // 15% chance of being muted
            isPinned = Random.nextFloat() < 0.2f, // 20% chance of being pinned
            createdAt = System.currentTimeMillis() - Random.nextLong(86400000, 31536000000),
            isEncrypted = Random.nextBoolean(),
            lastMessageSenderId = participants.random(),
            lastMessageType = listOf("text", "image", "video", "audio", "file").random()
        )
    }
    
    /**
     * Generate a mock message.
     */
    fun generateMockMessage(
        chatId: String,
        senderId: String,
        recipientId: String,
        id: String? = null,
        replyToId: String? = null
    ): Message {
        val messageId = id ?: UUID.randomUUID().toString()
        val contentType = if (Random.nextFloat() < 0.8f) "text" else listOf("image", "video", "audio", "document").random()

        return Message(
            id = messageId,
            chatId = chatId,
            senderId = senderId,
            recipientId = recipientId,
            content = if (contentType == "text") messageTemplates.random() else "Media message",
            contentType = contentType,
            mediaUrl = if (contentType != "text") "https://example.com/media/${UUID.randomUUID()}" else null,
            hasAttachments = contentType != "text",
            timestamp = System.currentTimeMillis() - Random.nextLong(0, 86400000),
            status = listOf("sent", "delivered", "read").random(),
            isEdited = Random.nextFloat() < 0.1f, // 10% chance of being edited
            deletedFor = null,
            replyToMessageId = replyToId,
            forwardFromMessageId = null,
            forwardFromChatId = null,
            forwardFromUserId = null,
            reactions = if (Random.nextFloat() < 0.3f) {
                mapOf("👍" to listOf(senderId), "❤️" to listOf(senderId))
            } else null
        ).apply {
            // Add attachments if it's a media message
            if (contentType != "text") {
                attachments = listOf(
                    MediaAttachment(
                        id = UUID.randomUUID().toString(),
                        messageId = messageId,
                        type = contentType,
                        url = "https://example.com/media/${UUID.randomUUID()}",
                        thumbnailUrl = if (contentType in listOf("image", "video")) "https://example.com/thumbnails/${UUID.randomUUID()}" else null,
                        name = "attachment_${Random.nextInt(1000, 9999)}.${
                            when (contentType) {
                                "image" -> "jpg"
                                "video" -> "mp4"
                                "audio" -> "mp3"
                                else -> "pdf"
                            }
                        }",
                        size = Random.nextLong(1024, 10485760), // 1KB to 10MB
                        duration = if (contentType in listOf("video", "audio")) Random.nextLong(10000, 300000) else null,
                        width = if (contentType in listOf("image", "video")) Random.nextInt(200, 1920) else null,
                        height = if (contentType in listOf("image", "video")) Random.nextInt(200, 1080) else null,
                        latitude = null,
                        longitude = null
                    )
                )
            }
        }
    }
    
    /**
     * Generate recovery phrase words.
     */
    fun generateRecoveryPhrase(): List<String> {
        val words = listOf(
            "apple", "banana", "cherry", "dog", "elephant", "frog", "giraffe", "hippo", "iguana",
            "jungle", "kangaroo", "lion", "monkey", "nightingale", "octopus", "penguin", "quail", "rabbit"
        )
        return words.shuffled().take(12)
    }
    
    /**
     * Generate a random Meena ID (9 characters).
     */
    fun generateMeenaId(): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..9).map { chars.random() }.joinToString("")
    }
}
