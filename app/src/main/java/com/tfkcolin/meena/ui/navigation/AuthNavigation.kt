package com.tfkcolin.meena.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.tfkcolin.meena.ui.auth.AccountRecoveryScreen
import com.tfkcolin.meena.ui.auth.LoginScreen
import com.tfkcolin.meena.ui.auth.RegistrationScreen
import com.tfkcolin.meena.ui.auth.SecretPhraseScreen
import com.tfkcolin.meena.ui.auth.SetupPinScreen
import com.tfkcolin.meena.ui.auth.TwoFactorAuthScreen
import com.tfkcolin.meena.ui.auth.UniqueIdScreen
import com.tfkcolin.meena.ui.auth.WelcomeScreen
import com.tfkcolin.meena.utils.TokenManager

/**
 * Authentication navigation graph.
 * This handles all authentication-related navigation.
 */
fun NavGraphBuilder.authNavigation(
    navController: NavHostController,
    onAuthenticationComplete: () -> Unit
) {
    navigation(
        startDestination = Screen.WelcomeScreen.route,
        route = AUTH_GRAPH_ROUTE
    ) {
        // Welcome screen (entry point)
        composable(Screen.WelcomeScreen.route) {
            // Check if the user has tokens but hasn't completed registration
            val context = LocalContext.current
            val tokenManager = remember { TokenManager(context) }

            // If the user has tokens but hasn't completed registration,
            // navigate directly to the UniqueIdScreen
            LaunchedEffect(Unit) {
                if (tokenManager.getAccessToken() != null && !tokenManager.isRegistrationComplete()) {
                    println("User has tokens but hasn't completed registration, navigating to UniqueIdScreen")
                    navController.navigate(Screen.UniqueIdScreen.route) {
                        popUpTo(Screen.WelcomeScreen.route) { inclusive = true }
                    }
                }
            }

            WelcomeScreen(
                onCreateAccount = {
                    navController.navigate(Screen.RegistrationScreen.route)
                },
                onLogin = {
                    navController.navigate(Screen.Login.route)
                }
            )
        }

        // Registration flow
        composable(Screen.RegistrationScreen.route) {
            RegistrationScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onRegistrationSuccess = {
                    navController.navigate(Screen.UniqueIdScreen.route)
                }
            )
        }

        composable(Screen.UniqueIdScreen.route) {
            UniqueIdScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onContinue = {
                    navController.navigate(Screen.SecretPhraseScreen.route)
                }
            )
        }

        composable(Screen.SecretPhraseScreen.route) {
            SecretPhraseScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onContinue = {
                    navController.navigate(Screen.SetupPinScreen.route)
                }
            )
        }

        composable(Screen.SetupPinScreen.route) {
            SetupPinScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onSetupComplete = {
                    onAuthenticationComplete()
                }
            )
        }

        // Login flow
        composable(Screen.Login.route) {
            LoginScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToRegister = {
                    navController.navigate(Screen.RegistrationScreen.route) {
                        popUpTo(Screen.Login.route) { inclusive = true }
                    }
                },
                onNavigateToRecovery = {
                    navController.navigate(Screen.AccountRecovery.route)
                },
                onLoginSuccess = {
                    onAuthenticationComplete()
                },
                onNavigateTo2FA = {
                    navController.navigate(Screen.TwoFactorAuth.route)
                }
            )
        }

        // Account recovery
        composable(Screen.AccountRecovery.route) {
            AccountRecoveryScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onRecoverySuccess = {
                    onAuthenticationComplete()
                }
            )
        }

        // Two-factor authentication
        composable(Screen.TwoFactorAuth.route) {
            TwoFactorAuthScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onAuthSuccess = {
                    onAuthenticationComplete()
                }
            )
        }
    }
}

// Route for the auth graph
const val AUTH_GRAPH_ROUTE = "auth_graph"
