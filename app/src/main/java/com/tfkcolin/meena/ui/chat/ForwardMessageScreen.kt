package com.tfkcolin.meena.ui.chat

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.data.models.Chat
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.ui.components.MeenaTopBar

/**
 * Forward message screen.
 *
 * @param messageId The message ID to forward.
 * @param onNavigateBack Navigate back to the previous screen.
 * @param viewModel The chat view model.
 */
@Composable
fun ForwardMessageScreen(
    messageId: String,
    onNavigateBack: () -> Unit,
    viewModel: ChatViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val chats by viewModel.chats.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // State for selected chat and additional content
    var selectedChat by remember { mutableStateOf<Chat?>(null) }
    var additionalContent by remember { mutableStateOf("") }

    // Show error message
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    // Handle successful forwarding
    LaunchedEffect(uiState.messageState.forwardOperation.isSuccessful) {
        if (uiState.messageState.forwardOperation.isSuccessful) {
            snackbarHostState.showSnackbar("Message forwarded successfully")
            viewModel.resetAllOperationStates()
            onNavigateBack()
        }
    }

    Scaffold(
        topBar = {
            MeenaTopBar(
                title = "Forward Message",
                onBackClick = onNavigateBack
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Message preview (in a real app, we would show the actual message content)
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Message Preview",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            )
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Original message content would be shown here",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Additional content field
                OutlinedTextField(
                    value = additionalContent,
                    onValueChange = { additionalContent = it },
                    label = { Text("Add a comment (optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Select a chat to forward to:",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Chat list
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    contentPadding = PaddingValues(vertical = 8.dp)
                ) {
                    items(chats) { chat ->
                        ForwardChatItem(
                            chat = chat,
                            isSelected = chat == selectedChat,
                            onClick = { selectedChat = chat }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Forward button
                Button(
                    onClick = {
                        selectedChat?.let { chat ->
                            // Forward the message
                            viewModel.forwardMessage(
                                messageId = messageId,
                                targetChatId = chat.id,
                                additionalContent = if (additionalContent.isBlank()) null else additionalContent
                            )
                        }
                    },
                    enabled = selectedChat != null && !uiState.messageState.forwardOperation.isInProgress,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = null
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text("Forward")
                }
            }

            if (uiState.messageState.forwardOperation.isInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

/**
 * Forward chat item component.
 *
 * @param chat The chat to display.
 * @param isSelected Whether the chat is selected.
 * @param onClick The click handler.
 * @param modifier The modifier for the component.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForwardChatItem(
    chat: Chat,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = androidx.compose.material3.CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Get the other participant's ID
            val participantIds = chat.participantIds.split(",")
            val otherParticipantId = participantIds.firstOrNull() ?: "Unknown"

            Text(
                text = otherParticipantId,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                )
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = chat.lastMessage ?: "No messages yet",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}
