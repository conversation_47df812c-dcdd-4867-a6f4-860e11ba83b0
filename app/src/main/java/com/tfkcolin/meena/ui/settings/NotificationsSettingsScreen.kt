package com.tfkcolin.meena.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Preview
import androidx.compose.material.icons.filled.ReceiptLong
import androidx.compose.material.icons.filled.Textsms
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.ui.components.SettingsItem

/**
 * Notifications settings screen.
 * Allows users to customize notification preferences.
 *
 * @param onNavigateBack Navigate back to the profile screen.
 * @param viewModel The settings view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationsSettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val settingsState by viewModel.settingsState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val scrollState = rememberScrollState()

    // Show error message
    LaunchedEffect(settingsState.error) {
        settingsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Notifications") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
        ) {
            // General notifications section
            Text(
                text = "Notifications",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(16.dp)
            )

            Text(
                text = "Control how and when you receive notifications",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            SettingsItem(
                title = "Enable Notifications",
                description = "Receive notifications for new messages and events",
                icon = Icons.Default.Notifications,
                onClick = { viewModel.updateNotificationsEnabled(!settingsState.notificationsEnabled) },
                endContent = {
                    Switch(
                        checked = settingsState.notificationsEnabled,
                        onCheckedChange = { viewModel.updateNotificationsEnabled(it) }
                    )
                }
            )

            SettingsItem(
                title = "Message Previews",
                description = "Show message content in notifications",
                icon = Icons.Default.Preview,
                onClick = { viewModel.updateMessagePreviewsEnabled(!settingsState.messagePreviewsEnabled) },
                endContent = {
                    Switch(
                        checked = settingsState.messagePreviewsEnabled,
                        onCheckedChange = { viewModel.updateMessagePreviewsEnabled(it) }
                    )
                }
            )

            HorizontalDivider(
                modifier = Modifier.padding(vertical = 8.dp),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outlineVariant
            )

            // Chat features section
            Text(
                text = "Chat Features",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(16.dp)
            )

            Text(
                text = "Control chat-related features and notifications",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            SettingsItem(
                title = "Read Receipts",
                description = "Let others know when you've read their messages",
                icon = Icons.Default.ReceiptLong,
                onClick = { viewModel.updateReadReceiptsEnabled(!settingsState.readReceiptsEnabled) },
                endContent = {
                    Switch(
                        checked = settingsState.readReceiptsEnabled,
                        onCheckedChange = { viewModel.updateReadReceiptsEnabled(it) }
                    )
                }
            )

            SettingsItem(
                title = "Typing Indicators",
                description = "Let others know when you're typing",
                icon = Icons.Default.Textsms,
                onClick = { viewModel.updateTypingIndicatorsEnabled(!settingsState.typingIndicatorsEnabled) },
                endContent = {
                    Switch(
                        checked = settingsState.typingIndicatorsEnabled,
                        onCheckedChange = { viewModel.updateTypingIndicatorsEnabled(it) }
                    )
                }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}
