package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.QRCodeScanner
import com.tfkcolin.meena.ui.contacts.components.RelationshipDropdown

/**
 * Add contact screen.
 * Allows users to add contacts by Meena ID or by scanning a QR code.
 *
 * @param onNavigateBack Navigate back to the contact list screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun AddContactScreen(
    onNavigateBack: () -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val focusManager = LocalFocusManager.current

    var userHandle by remember { mutableStateOf("") }
    var displayName by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var relationship by remember { mutableStateOf("friend") }
    var isRelationshipMenuExpanded by remember { mutableStateOf(false) }

    // QR code scanner state
    var showScanner by remember { mutableStateOf(false) }

    val relationships = listOf(
        "friend" to "Friend",
        "family" to "Family",
        "colleague" to "Colleague",
        "acquaintance" to "Acquaintance"
    )

    /**
     * Add a contact.
     */
    fun addContact() {
        viewModel.addContact(
            userHandle = userHandle,
            displayName = if (displayName.isBlank()) null else displayName,
            notes = if (notes.isBlank()) null else notes
        )
    }

    // Check if contact was added successfully
    LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
        if (contactsState.addContactOperation.isSuccessful) {
            viewModel.resetContactOperationStates()
            onNavigateBack()
        }
    }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    // Show operation error message
    LaunchedEffect(contactsState.addContactOperation.error) {
        contactsState.addContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    Scaffold(
        topBar = {
            MeenaTopBar(
                title = "Add Contact",
                onBackClick = onNavigateBack,
                actions = {
                    IconButton(onClick = { showScanner = true }) {
                        Icon(
                            imageVector = Icons.Default.QrCodeScanner,
                            contentDescription = "Scan QR Code"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        if (showScanner) {
            QRCodeScanner(
                onQRCodeScanned = { scannedMeenaId ->
                    userHandle = scannedMeenaId
                    showScanner = false
                    // Auto-add contact when scanned
                    addContact()
                },
                onClose = { showScanner = false }
            )
        } else {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                Text(
                    text = "Add a New Contact",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "Enter the Meena ID of the person you want to add to your contacts, or scan their QR code.",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(32.dp))

                // Use OutlinedTextField directly to support leadingIcon
                OutlinedTextField(
                    value = userHandle,
                    onValueChange = { userHandle = it },
                    label = { Text("Meena ID") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = null
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                MeenaTextField(
                    value = displayName,
                    onValueChange = { displayName = it },
                    label = "Display Name (Optional)",
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Relationship dropdown
                RelationshipDropdown(
                    selectedRelationship = relationship,
                    onRelationshipSelected = { relationship = it },
                    relationships = relationships,
                    isExpanded = isRelationshipMenuExpanded,
                    onExpandedChange = { isRelationshipMenuExpanded = it },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Notes field
                MeenaTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = "Notes (Optional)",
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            if (userHandle.isNotBlank()) {
                                addContact()
                            }
                        }
                    ),
                    singleLine = false,
                    maxLines = 5
                )

                Spacer(modifier = Modifier.height(32.dp))

                MeenaPrimaryButton(
                    text = "Add Contact",
                    onClick = { addContact() },
                    enabled = !contactsState.properties.isLoading &&
                              !contactsState.addContactOperation.isInProgress &&
                              userHandle.isNotBlank(),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Or scan a QR code",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                MeenaPrimaryButton(
                    text = "Scan QR Code",
                    onClick = { showScanner = true },
                    enabled = !contactsState.properties.isLoading &&
                              !contactsState.addContactOperation.isInProgress,
                    modifier = Modifier.fillMaxWidth()
                )

                // Add some space at the bottom for scrolling
                Spacer(modifier = Modifier.height(32.dp))
            }

            if (contactsState.properties.isLoading || contactsState.addContactOperation.isInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
    }
}

