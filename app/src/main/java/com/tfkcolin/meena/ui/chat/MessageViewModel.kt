package com.tfkcolin.meena.ui.chat

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.R
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message
import com.tfkcolin.meena.data.services.MediaService

import com.tfkcolin.meena.domain.usecases.chat.AddMessageReactionUseCase
import com.tfkcolin.meena.domain.usecases.chat.DeleteMessageForEveryoneUseCase
import com.tfkcolin.meena.domain.usecases.chat.DeleteMessageForSelfUseCase
import com.tfkcolin.meena.domain.usecases.chat.EditMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.ForwardMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetAttachmentsForMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetMessagesFlowUseCase
import com.tfkcolin.meena.domain.usecases.chat.GetMessagesUseCase
import com.tfkcolin.meena.domain.usecases.chat.MarkChatMessagesAsReadUseCase
import com.tfkcolin.meena.domain.usecases.chat.RemoveMessageReactionUseCase
import com.tfkcolin.meena.domain.usecases.chat.ReplyToMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.SearchMessagesInChatUseCase
import com.tfkcolin.meena.domain.usecases.chat.SearchMessagesUseCase
import com.tfkcolin.meena.domain.usecases.chat.SendMessageUseCase
import com.tfkcolin.meena.domain.usecases.chat.SendMessageWithAttachmentsUseCase
import com.tfkcolin.meena.domain.usecases.chat.UpdateMessageStatusUseCase
import com.tfkcolin.meena.ui.base.BaseViewModel
import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.base.failure
import com.tfkcolin.meena.ui.base.reset
import com.tfkcolin.meena.ui.base.start
import com.tfkcolin.meena.ui.base.success
import com.tfkcolin.meena.utils.ErrorHandler
import com.tfkcolin.meena.utils.FileUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for message operations in a chat.
 */
@HiltViewModel
class MessageViewModel @Inject constructor(
    private val getMessagesUseCase: GetMessagesUseCase,
    private val getMessagesFlowUseCase: GetMessagesFlowUseCase,
    private val sendMessageUseCase: SendMessageUseCase,
    private val sendMessageWithAttachmentsUseCase: SendMessageWithAttachmentsUseCase,
    private val updateMessageStatusUseCase: UpdateMessageStatusUseCase,
    private val markChatMessagesAsReadUseCase: MarkChatMessagesAsReadUseCase,
    private val editMessageUseCase: EditMessageUseCase,
    private val deleteMessageForSelfUseCase: DeleteMessageForSelfUseCase,
    private val deleteMessageForEveryoneUseCase: DeleteMessageForEveryoneUseCase,
    private val forwardMessageUseCase: ForwardMessageUseCase,
    private val searchMessagesUseCase: SearchMessagesUseCase,
    private val searchMessagesInChatUseCase: SearchMessagesInChatUseCase,
    private val getAttachmentsForMessageUseCase: GetAttachmentsForMessageUseCase,
    private val addMessageReactionUseCase: AddMessageReactionUseCase,
    private val removeMessageReactionUseCase: RemoveMessageReactionUseCase,
    private val replyToMessageUseCase: ReplyToMessageUseCase,
    private val mediaService: MediaService,
    private val fileUtils: FileUtils,
    errorHandler: ErrorHandler
) : BaseViewModel(errorHandler) {

    // UI state for message operations
    private val _uiState = MutableStateFlow(MessageUiState())
    val uiState: StateFlow<MessageUiState> = _uiState.asStateFlow()

    // Current chat ID
    private val _currentChatId = MutableStateFlow<String?>(null)
    val currentChatId: StateFlow<String?> = _currentChatId.asStateFlow()

    // Messages for the current chat
    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    init {
        // Note: Real-time message listening is not supported in mock-only mode
    }

    /**
     * Set the current chat and load its messages.
     *
     * @param chatId The chat ID.
     */
    fun setCurrentChat(chatId: String) {
        if (_currentChatId.value == chatId) return

        _currentChatId.value = chatId
        loadMessages(chatId)

        // Mark all messages in this chat as read
        viewModelScope.launch {
            markChatMessagesAsReadUseCase(chatId)
        }

        // Note: WebSocket connection is not needed in mock-only mode
    }

    /**
     * Load messages for a chat.
     *
     * @param chatId The chat ID.
     */
    fun loadMessages(chatId: String) {
        launchWithErrorHandling {
            setLoading(true)
            _uiState.update { it.copy(
                properties = it.properties.copy(isLoading = true, error = null)
            ) }

            executeUseCase(
                useCase = { getMessagesUseCase(chatId) },
                onSuccess = { response ->
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false),
                            totalMessageCount = response.total_count
                        )
                    }
                    setLoading(false)

                    // Update messages
                    _messages.value = response.messages

                    // Mark all messages as read
                    response.messages.forEach { message ->
                        if (message.status != "read" && message.recipientId == "current_user_id") {
                            updateMessageStatus(message.id, "read")
                        }
                    }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update {
                        it.copy(
                            properties = it.properties.copy(isLoading = false, error = errorMessage)
                        )
                    }
                    setError(errorMessage)
                },
                showLoading = false // We're managing loading state manually
            )
        }

        // Subscribe to messages flow
        viewModelScope.launch {
            getMessagesFlowUseCase(chatId).collectLatest { messages ->
                _messages.value = messages
            }
        }
    }

    /**
     * Send a message.
     *
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param contentType The message content type.
     * @param mediaUrl The media URL (optional).
     */
    fun sendMessage(
        recipientId: String,
        content: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ) {
        val chatId = _currentChatId.value ?: return

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                sendOperation = it.sendOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    sendMessageUseCase(
                        chatId = chatId,
                        recipientId = recipientId,
                        content = content,
                        contentType = contentType,
                        mediaUrl = mediaUrl
                    )
                },
                onSuccess = { response ->
                    _uiState.update { it.copy(
                        sendOperation = it.sendOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_message_send)
                    )
                    _uiState.update { it.copy(
                        sendOperation = it.sendOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Send a message with attachments.
     *
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param attachments The list of attachments.
     */
    fun sendMessageWithAttachments(
        recipientId: String,
        content: String,
        attachments: List<MediaAttachment>
    ) {
        val chatId = _currentChatId.value ?: return

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                sendOperation = it.sendOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    sendMessageWithAttachmentsUseCase(
                        chatId = chatId,
                        recipientId = recipientId,
                        content = content,
                        attachments = attachments
                    )
                },
                onSuccess = { response ->
                    _uiState.update { it.copy(
                        sendOperation = it.sendOperation.success(),
                        selectedAttachments = emptyList() // Clear attachments after successful send
                    ) }

                    // Observe upload progress for each attachment
                    attachments.forEach { attachment ->
                        attachment.uploadId?.let { uploadId ->
                            viewModelScope.launch {
                                mediaService.observeUploadProgress(uploadId).collectLatest { progress ->
                                    // You can handle progress updates here if needed
                                }
                            }
                        }
                    }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_message_send)
                    )
                    _uiState.update { it.copy(
                        sendOperation = it.sendOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Upload a media file and add it to selected attachments.
     *
     * @param uri The URI of the media file.
     * @param contentType The content type of the file.
     */
    fun uploadMediaForMessage(uri: Uri, contentType: String) {
        viewModelScope.launch {
            // Update state to indicate upload is in progress
            _uiState.update { it.copy(
                properties = it.properties.copy(isLoading = true)
            ) }

            // Upload the media
            val result = mediaService.uploadMedia(uri, contentType)

            // Update state based on result
            if (result.isSuccess) {
                val attachment = result.getOrNull()!!
                _uiState.update { it.copy(
                    properties = it.properties.copy(isLoading = false),
                    selectedAttachments = it.selectedAttachments + attachment
                ) }
            } else {
                val error = result.exceptionOrNull()?.message ?: "Unknown error"
                _uiState.update { it.copy(
                    properties = it.properties.copy(isLoading = false, error = error)
                ) }
                setError(error)
            }
        }
    }

    /**
     * Update a message's status.
     *
     * @param messageId The message ID.
     * @param status The new status.
     */
    fun updateMessageStatus(messageId: String, status: String) {
        viewModelScope.launch {
            updateMessageStatusUseCase(messageId, status)
        }
    }

    /**
     * Send a typing indicator.
     * Note: Typing indicators are not supported in mock-only mode.
     *
     * @param isTyping Whether the user is typing.
     */
    fun sendTypingIndicator(isTyping: Boolean) {
        // Note: Typing indicators are not supported in mock-only mode
    }

    /**
     * Edit a message.
     *
     * @param messageId The message ID.
     * @param newContent The new content.
     */
    fun editMessage(messageId: String, newContent: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                editOperation = it.editOperation.start()
            ) }

            executeUseCase(
                useCase = { editMessageUseCase(messageId, newContent) },
                onSuccess = {
                    _uiState.update { it.copy(
                        editOperation = it.editOperation.success(),
                        messageToEdit = null
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        editOperation = it.editOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Delete a message for self.
     *
     * @param messageId The message ID.
     */
    fun deleteMessageForSelf(messageId: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                deleteOperation = it.deleteOperation.start()
            ) }

            executeUseCase(
                useCase = { deleteMessageForSelfUseCase(messageId) },
                onSuccess = {
                    _uiState.update { it.copy(
                        deleteOperation = it.deleteOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        deleteOperation = it.deleteOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Delete a message for everyone.
     *
     * @param messageId The message ID.
     */
    fun deleteMessageForEveryone(messageId: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                deleteOperation = it.deleteOperation.start()
            ) }

            executeUseCase(
                useCase = { deleteMessageForEveryoneUseCase(messageId) },
                onSuccess = {
                    _uiState.update { it.copy(
                        deleteOperation = it.deleteOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        deleteOperation = it.deleteOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Forward a message to another chat.
     *
     * @param messageId The message ID.
     * @param targetChatId The target chat ID.
     * @param additionalContent Additional content to include with the forwarded message.
     */
    fun forwardMessage(messageId: String, targetChatId: String, additionalContent: String? = null) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                forwardOperation = it.forwardOperation.start()
            ) }

            executeUseCase(
                useCase = { forwardMessageUseCase(messageId, targetChatId, additionalContent) },
                onSuccess = {
                    _uiState.update { it.copy(
                        forwardOperation = it.forwardOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        forwardOperation = it.forwardOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Search for messages across all chats.
     *
     * @param query The search query.
     */
    fun searchMessages(query: String) {
        if (query.isBlank()) {
            clearSearch()
            return
        }

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                searchOperation = it.searchOperation.start(),
                searchQuery = query
            ) }

            try {
                val results = searchMessagesUseCase(query)
                _uiState.update { it.copy(
                    searchOperation = it.searchOperation.success(),
                    searchResults = results
                ) }
            } catch (e: Exception) {
                val errorMessage = errorHandler.getErrorMessage(
                    e,
                    appContext.getString(R.string.error_unknown)
                )
                _uiState.update { it.copy(
                    searchOperation = it.searchOperation.failure(errorMessage),
                    properties = it.properties.copy(error = errorMessage)
                ) }
                setError(errorMessage)
            }
        }
    }

    /**
     * Search for messages in the current chat.
     *
     * @param query The search query.
     */
    fun searchMessagesInCurrentChat(query: String) {
        val chatId = _currentChatId.value ?: return

        if (query.isBlank()) {
            clearSearch()
            return
        }

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                searchOperation = it.searchOperation.start(),
                searchQuery = query
            ) }

            try {
                val results = searchMessagesInChatUseCase(chatId, query)
                _uiState.update { it.copy(
                    searchOperation = it.searchOperation.success(),
                    searchResults = results
                ) }
            } catch (e: Exception) {
                val errorMessage = errorHandler.getErrorMessage(
                    e,
                    appContext.getString(R.string.error_unknown)
                )
                _uiState.update { it.copy(
                    searchOperation = it.searchOperation.failure(errorMessage),
                    properties = it.properties.copy(error = errorMessage)
                ) }
                setError(errorMessage)
            }
        }
    }

    /**
     * Clear the search results.
     */
    fun clearSearch() {
        _uiState.update { it.copy(
            searchOperation = it.searchOperation.reset(),
            searchQuery = "",
            searchResults = emptyList()
        ) }
    }

    /**
     * Set the message to edit.
     *
     * @param message The message to edit.
     */
    fun setMessageToEdit(message: Message) {
        _uiState.update { it.copy(messageToEdit = message) }
    }

    /**
     * Clear the message to edit.
     */
    fun clearMessageToEdit() {
        _uiState.update { it.copy(messageToEdit = null) }
    }

    /**
     * Add an attachment to the selected attachments.
     *
     * @param attachment The attachment to add.
     */
    fun addAttachment(attachment: MediaAttachment) {
        _uiState.update {
            it.copy(
                selectedAttachments = it.selectedAttachments + attachment
            )
        }
    }

    /**
     * Remove an attachment from the selected attachments.
     *
     * @param attachment The attachment to remove.
     */
    fun removeAttachment(attachment: MediaAttachment) {
        _uiState.update {
            it.copy(
                selectedAttachments = it.selectedAttachments.filter { it.id != attachment.id }
            )
        }
    }

    /**
     * Clear all selected attachments.
     */
    fun clearAttachments() {
        _uiState.update { it.copy(selectedAttachments = emptyList()) }
    }

    /**
     * Clear the error message.
     */
    override fun clearError() {
        super.clearError()
        _uiState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
    }

    /**
     * Reset message operation states.
     */
    fun resetMessageOperationStates() {
        _uiState.update { it.copy(
            sendOperation = it.sendOperation.reset(),
            editOperation = it.editOperation.reset(),
            deleteOperation = it.deleteOperation.reset(),
            forwardOperation = it.forwardOperation.reset(),
            searchOperation = it.searchOperation.reset()
        ) }
    }

    /**
     * Get attachments for a message.
     *
     * @param messageId The message ID.
     * @return A flow of media attachments.
     */
    fun getAttachmentsForMessage(messageId: String) = getAttachmentsForMessageUseCase.getFlow(messageId)

    /**
     * Add a reaction to a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji to add.
     */
    fun addReaction(messageId: String, emoji: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                reactionOperation = it.reactionOperation.start()
            ) }

            executeUseCase(
                useCase = { addMessageReactionUseCase(messageId, emoji) },
                onSuccess = {
                    _uiState.update { it.copy(
                        reactionOperation = it.reactionOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        reactionOperation = it.reactionOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Remove a reaction from a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji to remove.
     */
    fun removeReaction(messageId: String, emoji: String) {
        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                reactionOperation = it.reactionOperation.start()
            ) }

            executeUseCase(
                useCase = { removeMessageReactionUseCase(messageId, emoji) },
                onSuccess = {
                    _uiState.update { it.copy(
                        reactionOperation = it.reactionOperation.success()
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_unknown)
                    )
                    _uiState.update { it.copy(
                        reactionOperation = it.reactionOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Toggle a reaction on a message.
     *
     * @param messageId The message ID.
     * @param emoji The emoji to toggle.
     * @param currentUserId The current user ID.
     */
    fun toggleReaction(messageId: String, emoji: String, currentUserId: String) {
        val message = _messages.value.find { it.id == messageId } ?: return

        if (message.hasUserReacted(emoji, currentUserId)) {
            removeReaction(messageId, emoji)
        } else {
            addReaction(messageId, emoji)
        }
    }

    /**
     * Reply to a message.
     *
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param contentType The content type.
     * @param mediaUrl The media URL.
     */
    fun replyToMessage(
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ) {
        val chatId = _currentChatId.value ?: return

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                replyOperation = it.replyOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    replyToMessageUseCase(
                        chatId = chatId,
                        recipientId = recipientId,
                        content = content,
                        replyToMessageId = replyToMessageId,
                        contentType = contentType,
                        mediaUrl = mediaUrl
                    )
                },
                onSuccess = { response ->
                    _uiState.update { it.copy(
                        replyOperation = it.replyOperation.success(),
                        messageToReplyTo = null
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_message_send)
                    )
                    _uiState.update { it.copy(
                        replyOperation = it.replyOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Reply to a message with attachments.
     *
     * @param recipientId The recipient ID.
     * @param content The message content.
     * @param replyToMessageId The ID of the message being replied to.
     * @param attachments The list of attachments.
     */
    fun replyToMessageWithAttachments(
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ) {
        val chatId = _currentChatId.value ?: return

        launchWithErrorHandling {
            // Update state to indicate operation is in progress
            _uiState.update { it.copy(
                replyOperation = it.replyOperation.start()
            ) }

            executeUseCase(
                useCase = {
                    replyToMessageUseCase.withAttachments(
                        chatId = chatId,
                        recipientId = recipientId,
                        content = content,
                        replyToMessageId = replyToMessageId,
                        attachments = attachments
                    )
                },
                onSuccess = { response ->
                    _uiState.update { it.copy(
                        replyOperation = it.replyOperation.success(),
                        messageToReplyTo = null,
                        selectedAttachments = emptyList() // Clear attachments after successful send
                    ) }
                },
                onError = { error ->
                    val errorMessage = errorHandler.getErrorMessage(
                        error,
                        appContext.getString(R.string.error_message_send)
                    )
                    _uiState.update { it.copy(
                        replyOperation = it.replyOperation.failure(errorMessage),
                        properties = it.properties.copy(error = errorMessage)
                    ) }
                    setError(errorMessage)
                },
                showLoading = false
            )
        }
    }

    /**
     * Set the message to reply to.
     *
     * @param message The message to reply to.
     */
    fun setMessageToReplyTo(message: Message) {
        _uiState.update { it.copy(messageToReplyTo = message) }
    }

    /**
     * Clear the message to reply to.
     */
    fun clearMessageToReplyTo() {
        _uiState.update { it.copy(messageToReplyTo = null) }
    }

    /**
     * Download a media attachment.
     *
     * @param attachment The media attachment to download.
     */
    fun downloadMediaAttachment(attachment: MediaAttachment) {
        viewModelScope.launch {
            val url = attachment.url ?: return@launch
            val fileName = attachment.fileName ?: "download_${System.currentTimeMillis()}"

            // Update state to indicate download is in progress
            _uiState.update { it.copy(
                properties = it.properties.copy(isLoading = true)
            ) }

            // Download the media
            val result = mediaService.downloadMedia(url, fileName)

            // Update state based on result
            if (result.isSuccess) {
                _uiState.update { it.copy(
                    properties = it.properties.copy(isLoading = false)
                ) }
            } else {
                val error = result.exceptionOrNull()?.message ?: "Unknown error"
                _uiState.update { it.copy(
                    properties = it.properties.copy(isLoading = false, error = error)
                ) }
                setError(error)
            }
        }
    }
}


