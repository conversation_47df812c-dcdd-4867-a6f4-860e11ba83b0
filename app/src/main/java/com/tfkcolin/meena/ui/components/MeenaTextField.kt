package com.tfkcolin.meena.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import com.tfkcolin.meena.R

/**
 * Text field component for the app.
 * 
 * @param value The current value of the text field.
 * @param onValueChange The callback for when the value changes.
 * @param label The label for the text field.
 * @param modifier The modifier for the text field.
 * @param placeholder The placeholder text.
 * @param keyboardOptions The keyboard options.
 * @param keyboardActions The keyboard actions.
 * @param isError Whether the text field is in an error state.
 * @param errorText The error text to display.
 * @param singleLine Whether the text field is single line.
 * @param maxLines The maximum number of lines.
 * @param readOnly Whether the text field is read-only.
 */
@Composable
fun MeenaTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    placeholder: String? = null,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    isError: Boolean = false,
    errorText: String? = null,
    singleLine: Boolean = true,
    maxLines: Int = 1,
    readOnly: Boolean = false
) {
    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        label = { Text(text = label) },
        placeholder = placeholder?.let { { Text(text = it) } },
        modifier = modifier.fillMaxWidth(),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        isError = isError,
        supportingText = if (isError && !errorText.isNullOrEmpty()) {
            { Text(text = errorText) }
        } else null,
        singleLine = singleLine,
        maxLines = maxLines,
        readOnly = readOnly,
        colors = colors
    )
}

/**
 * Password text field component for the app.
 * 
 * @param value The current value of the text field.
 * @param onValueChange The callback for when the value changes.
 * @param label The label for the text field.
 * @param modifier The modifier for the text field.
 * @param placeholder The placeholder text.
 * @param keyboardOptions The keyboard options.
 * @param keyboardActions The keyboard actions.
 * @param isError Whether the text field is in an error state.
 * @param errorText The error text to display.
 */
@Composable
fun MeenaPasswordTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    placeholder: String? = null,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    keyboardOptions: KeyboardOptions = KeyboardOptions(
        keyboardType = KeyboardType.Password,
    ),
    isError: Boolean = false,
    errorText: String? = null
) {
    var passwordVisible by remember { mutableStateOf(false) }
    
    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        label = { Text(text = label) },
        placeholder = placeholder?.let { { Text(text = it) } },
        modifier = modifier.fillMaxWidth(),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        isError = isError,
        supportingText = if (isError && !errorText.isNullOrEmpty()) {
            { Text(text = errorText) }
        } else null,
        singleLine = true,
        visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
        trailingIcon = {
            val icon = if (passwordVisible) {
                painterResource(id = R.drawable.ic_visibility_off)
            } else {
                painterResource(id = R.drawable.ic_visibility)
            }
            
            IconButton(onClick = { passwordVisible = !passwordVisible }) {
                Icon(
                    painter = icon,
                    contentDescription = if (passwordVisible) "Hide password" else "Show password"
                )
            }
        }
    )
}
