package com.tfkcolin.meena.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.filled.SettingsBrightness
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.ui.components.SettingsItem

/**
 * Appearance settings screen.
 * Allows users to customize the app's appearance, such as theme.
 *
 * @param onNavigateBack Navigate back to the profile screen.
 * @param viewModel The settings view model.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppearanceSettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val settingsState by viewModel.settingsState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val scrollState = rememberScrollState()

    // Show error message
    LaunchedEffect(settingsState.error) {
        settingsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Appearance") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
        ) {
            // Theme section
            Text(
                text = "Theme",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(16.dp)
            )

            Text(
                text = "Choose how the app appears on your device",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // System theme option
            ThemeOption(
                title = "System default",
                description = "Follow your device's theme settings",
                icon = Icons.Default.SettingsBrightness,
                isSelected = settingsState.theme == "system",
                onClick = { viewModel.updateTheme("system") }
            )

            // Light theme option
            ThemeOption(
                title = "Light",
                description = "Always use light theme",
                icon = Icons.Default.LightMode,
                isSelected = settingsState.theme == "light",
                onClick = { viewModel.updateTheme("light") }
            )

            // Dark theme option
            ThemeOption(
                title = "Dark",
                description = "Always use dark theme",
                icon = Icons.Default.DarkMode,
                isSelected = settingsState.theme == "dark",
                onClick = { viewModel.updateTheme("dark") }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * Theme option item.
 *
 * @param title The title of the theme option.
 * @param description The description of the theme option.
 * @param icon The icon of the theme option.
 * @param isSelected Whether the theme option is selected.
 * @param onClick The action to perform when the theme option is clicked.
 */
@Composable
private fun ThemeOption(
    title: String,
    description: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    SettingsItem(
        title = title,
        description = description,
        icon = icon,
        onClick = onClick,
        endContent = {
            RadioButton(
                selected = isSelected,
                onClick = onClick
            )
        }
    )
}
