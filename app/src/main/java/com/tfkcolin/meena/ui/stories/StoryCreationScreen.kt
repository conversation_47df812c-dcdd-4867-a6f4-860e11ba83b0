package com.tfkcolin.meena.ui.stories

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddToPhotos
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.EmojiEmotions
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.PhotoCamera
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.TextFields
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import coil.compose.AsyncImage
import com.tfkcolin.meena.ui.components.foundation.DraggableTextOverlay
import com.tfkcolin.meena.ui.components.foundation.TextOverlay
import com.tfkcolin.meena.ui.stories.models.OverlayItem
import com.tfkcolin.meena.ui.stories.models.OverlayType
import com.tfkcolin.meena.ui.stories.models.SegmentType
import com.tfkcolin.meena.ui.stories.models.Story
import com.tfkcolin.meena.ui.stories.models.StorySegment
import java.util.Date
import java.util.UUID

/**
 * Screen for creating a new story.
 *
 * @param onClose The callback when the creation is cancelled.
 * @param onPublish The callback when the story is published.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StoryCreationScreen(
    onClose: () -> Unit,
    onPublish: (Uri, String) -> Unit
) {
    var selectedImage: Uri? by remember { mutableStateOf(null) }
    var currentText by remember { mutableStateOf("") }
    var textOverlays by remember { mutableStateOf<List<TextOverlay>>(emptyList()) }
    var editingTextId: String? by remember { mutableStateOf(null) }
    var caption by remember { mutableStateOf("") }

    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        selectedImage = uri
    }

    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        // Handle camera result
        // Note: This requires a URI to be prepared beforehand
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Create Story") },
                navigationIcon = {
                    IconButton(onClick = onClose) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close"
                        )
                    }
                },
                actions = {
                    Button(
                        onClick = {
                            selectedImage?.let { uri ->
                                onPublish(uri, caption)
                            }
                        },
                        enabled = selectedImage != null
                    ) {
                        Text("Share")
                    }
                }
            )
        },
        bottomBar = {
            // Controls for story creation
            BottomAppBar(
                containerColor = MaterialTheme.colorScheme.surface,
                contentColor = MaterialTheme.colorScheme.onSurface
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Text tool
                    IconButton(
                        onClick = {
                            editingTextId = UUID.randomUUID().toString()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.TextFields,
                            contentDescription = "Add text"
                        )
                    }

                    // Sticker tool
                    IconButton(
                        onClick = { /* Open sticker picker */ }
                    ) {
                        Icon(
                            imageVector = Icons.Default.EmojiEmotions,
                            contentDescription = "Add sticker"
                        )
                    }

                    // Drawing tool
                    IconButton(
                        onClick = { /* Enable drawing mode */ }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Draw"
                        )
                    }

                    // Media picker
                    if (selectedImage == null) {
                        IconButton(
                            onClick = { galleryLauncher.launch("image/*") }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Image,
                                contentDescription = "Pick image"
                            )
                        }

                        IconButton(
                            onClick = { /* Launch camera */ }
                        ) {
                            Icon(
                                imageVector = Icons.Default.PhotoCamera,
                                contentDescription = "Take photo"
                            )
                        }
                    }
                }
            }
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .background(Color.Black)
        ) {
            // Preview area
            if (selectedImage != null) {
                // Image preview
                AsyncImage(
                    model = selectedImage,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )

                // Overlay text items
                textOverlays.forEach { textOverlay ->
                    if (textOverlay.id != editingTextId) {
                        DraggableTextOverlay(
                            overlay = textOverlay,
                            onPositionChange = { newPosition ->
                                textOverlays = textOverlays.map { overlay ->
                                    if (overlay.id == textOverlay.id) {
                                        overlay.copy(position = newPosition)
                                    } else {
                                        overlay
                                    }
                                }
                            },
                            onEditRequest = {
                                currentText = textOverlay.text
                                editingTextId = textOverlay.id
                            },
                            onDeleteRequest = {
                                textOverlays = textOverlays.filter { it.id != textOverlay.id }
                            }
                        )
                    }
                }

                // Caption input at the bottom
                TextField(
                    value = caption,
                    onValueChange = { caption = it },
                    placeholder = { Text("Add a caption...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                        .padding(16.dp),
                    colors = androidx.compose.material3.TextFieldDefaults.colors(
                        focusedContainerColor = Color.Black.copy(alpha = 0.5f),
                        unfocusedContainerColor = Color.Black.copy(alpha = 0.5f),
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White
                    )
                )
            } else {
                // Empty state
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.AddToPhotos,
                            contentDescription = null,
                            modifier = Modifier.size(72.dp),
                            tint = Color.White.copy(alpha = 0.6f)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Tap to add a photo or video",
                            color = Color.White.copy(alpha = 0.6f),
                            style = MaterialTheme.typography.bodyLarge
                        )

                        Spacer(modifier = Modifier.height(24.dp))

                        Row(
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Button(
                                onClick = { /* Launch camera */ },
                                modifier = Modifier.height(48.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PhotoCamera,
                                    contentDescription = "Camera"
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Camera")
                            }

                            Button(
                                onClick = { galleryLauncher.launch("image/*") },
                                modifier = Modifier.height(48.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PhotoLibrary,
                                    contentDescription = "Gallery"
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Gallery")
                            }
                        }
                    }
                }
            }

            // Text editing dialog
            if (editingTextId != null) {
                Dialog(
                    onDismissRequest = {
                        editingTextId = null
                        currentText = ""
                    }
                ) {
                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = MaterialTheme.colorScheme.surface
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Add Text",
                                style = MaterialTheme.typography.titleMedium
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            TextField(
                                value = currentText,
                                onValueChange = { currentText = it },
                                placeholder = { Text("Enter text here...") },
                                modifier = Modifier.fillMaxWidth()
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.End
                            ) {
                                TextButton(
                                    onClick = {
                                        editingTextId = null
                                        currentText = ""
                                    }
                                ) {
                                    Text("Cancel")
                                }

                                Spacer(modifier = Modifier.width(8.dp))

                                Button(
                                    onClick = {
                                        if (currentText.isNotEmpty()) {
                                            val isExisting = textOverlays.any { it.id == editingTextId }

                                            textOverlays = if (isExisting) {
                                                // Update existing text
                                                textOverlays.map { overlay ->
                                                    if (overlay.id == editingTextId) {
                                                        overlay.copy(text = currentText)
                                                    } else {
                                                        overlay
                                                    }
                                                }
                                            } else {
                                                // Add new text
                                                textOverlays + TextOverlay(
                                                    id = editingTextId!!,
                                                    text = currentText,
                                                    position = Offset(0.5f, 0.5f) // Center
                                                )
                                            }

                                            editingTextId = null
                                            currentText = ""
                                        }
                                    },
                                    enabled = currentText.isNotEmpty()
                                ) {
                                    Text("Done")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
