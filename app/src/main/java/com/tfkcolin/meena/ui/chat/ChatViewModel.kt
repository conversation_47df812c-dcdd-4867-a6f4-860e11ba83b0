package com.tfkcolin.meena.ui.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.meena.utils.TokenManager
import com.tfkcolin.meena.utils.MediaAttachmentHelper
import com.tfkcolin.meena.data.models.ConversationType
import com.tfkcolin.meena.data.models.MediaAttachment
import com.tfkcolin.meena.data.models.Message

import com.tfkcolin.meena.ui.base.OperationState
import com.tfkcolin.meena.ui.models.ConversationListItem
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Facade ViewModel for chat screens that delegates to specialized ViewModels.
 * This approach separates responsibilities and avoids method overload conflicts.
 */
@HiltViewModel
class ChatViewModel @Inject constructor(
    private val conversationListViewModel: ConversationListViewModel,
    private val messageViewModel: MessageViewModel,
    private val groupChatViewModel: GroupChatViewModel,
    private val tokenManager: TokenManager,
    val mediaAttachmentHelper: MediaAttachmentHelper
) : ViewModel() {

    // Combined UI state for chat operations
    private val _uiState = MutableStateFlow(ChatUiState())
    val uiState: StateFlow<ChatUiState> = _uiState.asStateFlow()

    // Typing indicators
    private val _typingUsers = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val typingUsers: StateFlow<Map<String, Boolean>> = _typingUsers.asStateFlow()

    init {
        // Collect states from specialized ViewModels
        viewModelScope.launch {
            conversationListViewModel.uiState.collectLatest { state ->
                _uiState.update { it.copy(
                    conversationListState = state
                ) }
            }
        }

        viewModelScope.launch {
            messageViewModel.uiState.collectLatest { state ->
                _uiState.update { it.copy(
                    messageState = state
                ) }
            }
        }

        viewModelScope.launch {
            groupChatViewModel.uiState.collectLatest { state ->
                _uiState.update { it.copy(
                    groupChatState = state
                ) }
            }
        }

        // Note: Typing indicators are not supported in mock-only mode
    }

    // Delegate properties to specialized ViewModels
    val chats = conversationListViewModel.chats
    val groupChats = conversationListViewModel.groupChats
    val conversationListItems = conversationListViewModel.conversationListItems
    val messages = messageViewModel.messages
    val currentChatId = messageViewModel.currentChatId

    // Conversation List Operations
    fun loadChats() = conversationListViewModel.loadChats()
    fun getOrCreateChat(userId: String) = conversationListViewModel.getOrCreateChat(userId)
    fun archiveChat(conversationId: String, isArchived: Boolean) = conversationListViewModel.archiveChat(conversationId, isArchived)
    fun muteChat(conversationId: String, isMuted: Boolean) = conversationListViewModel.muteChat(conversationId, isMuted)
    fun pinChat(conversationId: String, isPinned: Boolean) = conversationListViewModel.pinChat(conversationId, isPinned)
    fun toggleShowArchivedChats(show: Boolean) = conversationListViewModel.toggleShowArchivedChats(show)
    fun updateContactNameMap(contactNameMap: Map<String, String>) = conversationListViewModel.updateContactNameMap(contactNameMap)

    // Conversation filtering methods
    fun getConversationsByType(type: ConversationType): List<ConversationListItem> {
        return conversationListItems.value.filter { it.conversationType == type }
    }

    fun getOneToOneConversations(): List<ConversationListItem> {
        return getConversationsByType(ConversationType.ONE_TO_ONE)
    }

    fun getGroupConversations(): List<ConversationListItem> {
        return getConversationsByType(ConversationType.GROUP)
    }

    fun getChannelConversations(): List<ConversationListItem> {
        return getConversationsByType(ConversationType.CHANNEL)
    }

    // Message Operations
    fun setCurrentChat(chatId: String) = messageViewModel.setCurrentChat(chatId)
    fun loadMessages(chatId: String) = messageViewModel.loadMessages(chatId)
    fun sendMessage(recipientId: String, content: String, contentType: String = "text", mediaUrl: String? = null) =
        messageViewModel.sendMessage(recipientId, content, contentType, mediaUrl)
    fun sendMessageWithAttachments(recipientId: String, content: String, attachments: List<MediaAttachment>) =
        messageViewModel.sendMessageWithAttachments(recipientId, content, attachments)
    fun updateMessageStatus(messageId: String, status: String) = messageViewModel.updateMessageStatus(messageId, status)
    fun sendTypingIndicator(isTyping: Boolean) = messageViewModel.sendTypingIndicator(isTyping)
    fun editMessage(messageId: String, newContent: String) = messageViewModel.editMessage(messageId, newContent)
    fun deleteMessageForSelf(messageId: String) = messageViewModel.deleteMessageForSelf(messageId)
    fun deleteMessageForEveryone(messageId: String) = messageViewModel.deleteMessageForEveryone(messageId)
    fun forwardMessage(messageId: String, targetChatId: String, additionalContent: String? = null) =
        messageViewModel.forwardMessage(messageId, targetChatId, additionalContent)
    fun searchMessages(query: String) = messageViewModel.searchMessages(query)
    fun searchMessagesInCurrentChat(query: String) = messageViewModel.searchMessagesInCurrentChat(query)
    fun clearSearch() = messageViewModel.clearSearch()
    fun setMessageToEdit(message: Message) = messageViewModel.setMessageToEdit(message)
    fun clearMessageToEdit() = messageViewModel.clearMessageToEdit()
    fun addAttachment(attachment: MediaAttachment) = messageViewModel.addAttachment(attachment)
    fun removeAttachment(attachment: MediaAttachment) = messageViewModel.removeAttachment(attachment)
    fun clearAttachments() = messageViewModel.clearAttachments()

    // Message Reaction Operations
    fun addReaction(messageId: String, emoji: String) = messageViewModel.addReaction(messageId, emoji)
    fun removeReaction(messageId: String, emoji: String) = messageViewModel.removeReaction(messageId, emoji)
    fun toggleReaction(messageId: String, emoji: String, currentUserId: String) =
        messageViewModel.toggleReaction(messageId, emoji, currentUserId)

    // Message Reply Operations
    fun replyToMessage(
        recipientId: String,
        content: String,
        replyToMessageId: String,
        contentType: String = "text",
        mediaUrl: String? = null
    ) = messageViewModel.replyToMessage(recipientId, content, replyToMessageId, contentType, mediaUrl)

    fun replyToMessageWithAttachments(
        recipientId: String,
        content: String,
        replyToMessageId: String,
        attachments: List<MediaAttachment>
    ) = messageViewModel.replyToMessageWithAttachments(recipientId, content, replyToMessageId, attachments)

    fun setMessageToReplyTo(message: Message) = messageViewModel.setMessageToReplyTo(message)
    fun clearMessageToReplyTo() = messageViewModel.clearMessageToReplyTo()

    // Group Chat Operations
    fun createGroupChat(name: String, description: String?, initialMembers: List<String>, privacyType: String) =
        groupChatViewModel.createGroupChat(name, description, initialMembers, privacyType)
    fun updateGroupChat(groupId: String, name: String?, description: String?, pictureUrl: String?, privacyType: String?) =
        groupChatViewModel.updateGroupChat(groupId, name, description, pictureUrl, privacyType)
    fun addGroupChatParticipants(groupId: String, userHandles: List<String>) =
        groupChatViewModel.addGroupChatParticipants(groupId, userHandles)
    fun removeGroupChatParticipants(groupId: String, userHandles: List<String>) =
        groupChatViewModel.removeGroupChatParticipants(groupId, userHandles)
    fun updateGroupChatAdmins(groupId: String, userHandles: List<String>) =
        groupChatViewModel.updateGroupChatAdmins(groupId, userHandles)
    fun leaveGroupChat(groupId: String) = groupChatViewModel.leaveGroupChat(groupId)

    // Utility Methods
    fun clearError() {
        _uiState.update { it.copy(
            properties = it.properties.copy(error = null)
        ) }
        conversationListViewModel.clearError()
        messageViewModel.clearError()
        groupChatViewModel.clearError()
    }

    fun resetAllOperationStates() {
        conversationListViewModel.resetConversationOperationStates()
        messageViewModel.resetMessageOperationStates()
        groupChatViewModel.resetGroupChatOperationStates()
    }

    fun getCurrentUserId(): String {
        return tokenManager.getUserId() ?: ""
    }

    fun getMessageById(messageId: String): Message? {
        return messages.value.find { it.id == messageId }
    }

    override fun onCleared() {
        super.onCleared()
        // Note: No WebSocket cleanup needed in mock-only mode
    }
}


