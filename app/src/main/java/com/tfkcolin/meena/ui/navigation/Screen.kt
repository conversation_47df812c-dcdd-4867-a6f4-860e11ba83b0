package com.tfkcolin.meena.ui.navigation

/**
 * Screen routes for navigation.
 */
sealed class Screen(val route: String) {
    // Auth screens
    object AnonymousSignIn : Screen("anonymous_sign_in") // This is now the WelcomeScreen
    object Login : Screen("login")
    object Register : Screen("register")
    object RecoveryPhrase : Screen("recovery_phrase")
    object RecoveryPin : Screen("recovery_pin")
    object RemoteWipePin : Screen("remote_wipe_pin")
    object AccountRecovery : Screen("account_recovery")
    object TwoFactorAuth : Screen("two_factor_auth")

    // New auth flow screens
    object WelcomeScreen : Screen("welcome_screen")
    object RegistrationScreen : Screen("registration_screen")
    object UniqueIdScreen : Screen("unique_id_screen")
    object SecretPhraseScreen : Screen("secret_phrase_screen")
    object SetupPinScreen : Screen("setup_pin_screen")
    // Main screens
    object Home : Screen("home")
    object Contacts : Screen("contacts")
    object AddContact : Screen("add_contact")
    object ContactDetail : Screen("contact_detail/{contactId}") {
        fun createRoute(contactId: String) = "contact_detail/$contactId"
    }
    object EditContact : Screen("edit_contact/{contactId}") {
        fun createRoute(contactId: String) = "edit_contact/$contactId"
    }
    object ContactGroups : Screen("contact_groups")
    object ContactGroupDetail : Screen("contact_group_detail/{groupId}") {
        fun createRoute(groupId: String) = "contact_group_detail/$groupId"
    }
    object Settings : Screen("settings")
    object SecuritySettings : Screen("security_settings")
    object EditProfile : Screen("edit_profile")
    object AppearanceSettings : Screen("appearance_settings")
    object NotificationsSettings : Screen("notifications_settings")
    object LanguageSettings : Screen("language_settings")

    // Chat screens
    object ChatList : Screen("chat_list")
    object Chat : Screen("chat/{chatId}/{recipientId}") {
        fun createRoute(chatId: String, recipientId: String) = "chat/$chatId/$recipientId"
    }
    object NewChat : Screen("new_chat")
    object Search : Screen("search")
    object ChatSearch : Screen("chat_search/{chatId}/{recipientName}") {
        fun createRoute(chatId: String, recipientName: String) = "chat_search/$chatId/$recipientName"
    }
    object ForwardMessage : Screen("forward_message/{messageId}") {
        fun createRoute(messageId: String) = "forward_message/$messageId"
    }

    // Group screens
    object CreateGroup : Screen("create_group")
    object GroupDetails : Screen("group_details/{groupId}") {
        fun createRoute(groupId: String) = "group_details/$groupId"
    }

    // Media screens
    object MediaPreview : Screen("media_preview/{messageId}/{initialIndex?}") {
        fun createRoute(messageId: String, initialIndex: Int = 0) = "media_preview/$messageId/$initialIndex"
    }

    // Bottom navigation screens
    object Chats : Screen("chats")
    object Stories : Screen("stories")
    object Calls : Screen("calls")
    object Profile : Screen("profile")

    // Stories screens
    object StoryCreation : Screen("story_creation")
    object StoryViewer : Screen("story_viewer")
}
