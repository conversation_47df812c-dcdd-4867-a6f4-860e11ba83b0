package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.data.models.isFavorite
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.components.SwipeToDelete
import com.tfkcolin.meena.ui.contacts.components.ContactListItem
import com.tfkcolin.meena.ui.contacts.components.ContactSearchBar
import com.tfkcolin.meena.ui.contacts.components.ContactSection
import com.tfkcolin.meena.ui.contacts.components.RelationshipFilterChips

/**
 * Contact list screen.
 *
 * @param onNavigateToAddContact Navigate to the add contact screen.
 * @param onNavigateToContactDetail Navigate to the contact detail screen.
 * @param onNavigateToContactGroups Navigate to the contact groups screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun ContactListScreen(
    onNavigateToAddContact: () -> Unit,
    onNavigateToContactDetail: (String) -> Unit,
    onNavigateToContactGroups: () -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val contacts by viewModel.contacts.collectAsState()
    val contactsByRelationship by viewModel.contactsByRelationship.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    var searchQuery by remember { mutableStateOf("") }
    var showSearch by remember { mutableStateOf(false) }

    // Filter contacts based on selected relationship
    val filteredContacts = if (contactsState.selectedRelationship != null) {
        contacts.filter { it.relationship == contactsState.selectedRelationship }
    } else {
        contacts
    }

    // Filter contacts based on search query
    val searchResults = if (searchQuery.isNotEmpty()) {
        filteredContacts.filter {
            it.displayName?.contains(searchQuery, ignoreCase = true) == true ||
            it.notes?.contains(searchQuery, ignoreCase = true) == true ||
            it.contactId.contains(searchQuery, ignoreCase = true)
        }
    } else {
        filteredContacts
    }

    // Get favorite contacts
    val favoriteContacts = contacts.filter { it.isFavorite() }

    // Get recent contacts
    val recentContacts = contactsState.recentContacts

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    // Show operation success/error messages
    LaunchedEffect(contactsState.addContactOperation.isSuccessful) {
        if (contactsState.addContactOperation.isSuccessful) {
            snackbarHostState.showSnackbar("Contact added successfully")
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.updateContactOperation.isSuccessful) {
        if (contactsState.updateContactOperation.isSuccessful) {
            snackbarHostState.showSnackbar("Contact updated successfully")
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.blockContactOperation.isSuccessful) {
        if (contactsState.blockContactOperation.isSuccessful) {
            snackbarHostState.showSnackbar("Contact blocked successfully")
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.isSuccessful) {
        if (contactsState.unblockContactOperation.isSuccessful) {
            snackbarHostState.showSnackbar("Contact unblocked successfully")
            viewModel.resetContactOperationStates()
        }
    }

    // Show operation error messages
    LaunchedEffect(contactsState.addContactOperation.error) {
        contactsState.addContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.updateContactOperation.error) {
        contactsState.updateContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.blockContactOperation.error) {
        contactsState.blockContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    LaunchedEffect(contactsState.unblockContactOperation.error) {
        contactsState.unblockContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    Scaffold(
        topBar = {
            MeenaTopBar(
                title = "Contacts",
                actions = {
                    IconButton(onClick = { showSearch = !showSearch }) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = "Search"
                        )
                    }
                    IconButton(onClick = { onNavigateToContactGroups() }) {
                        Icon(
                            imageVector = Icons.Default.Group,
                            contentDescription = "Contact Groups"
                        )
                    }
                    IconButton(onClick = { viewModel.loadContacts() }) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh"
                        )
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = onNavigateToAddContact,
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = Color.White
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Contact"
                )
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (contacts.isEmpty() && !contactsState.properties.isLoading) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "No Contacts",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Add your first contact by tapping the + button",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // Contact list with sections
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(bottom = 80.dp) // Add padding for FAB
                ) {
                    // Search bar
                    if (showSearch) {
                        item {
                            ContactSearchBar(
                                query = searchQuery,
                                onQueryChange = { searchQuery = it },
                                onSearch = { viewModel.searchContacts(it) },
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                    }

                    // Relationship filter chips
                    item {
                        RelationshipFilterChips(
                            selectedRelationship = contactsState.selectedRelationship,
                            onRelationshipSelected = { viewModel.filterContactsByRelationship(it) },
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    // If searching, show search results
                    if (searchQuery.isNotEmpty()) {
                        item {
                            Text(
                                text = "Search Results",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }

                        if (searchResults.isEmpty()) {
                            item {
                                Text(
                                    text = "No contacts found for '$searchQuery'",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                                )
                            }
                        } else {
                            items(searchResults) { contact ->
                                SwipeToDelete(
                                    item = contact,
                                    onDelete = { viewModel.deleteContact(it.id) }
                                ) { swipeContact ->
                                    ContactListItem(
                                        contact = swipeContact,
                                        onClick = { onNavigateToContactDetail(swipeContact.id) },
                                        onRemove = { viewModel.deleteContact(swipeContact.id) },
                                        onEdit = { onNavigateToContactDetail(swipeContact.id) },
                                        onMessage = { /* TODO: Navigate to chat */ },
                                        onToggleFavorite = {
                                            if (swipeContact.isFavorite()) {
                                                viewModel.removeFromFavorites(swipeContact.id)
                                            } else {
                                                viewModel.addToFavorites(swipeContact.id)
                                            }
                                        },
                                        onToggleBlocked = {
                                            if (swipeContact.isBlocked()) {
                                                viewModel.unblockContact(swipeContact.id)
                                            } else {
                                                viewModel.blockContact(swipeContact.id)
                                            }
                                        },
                                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    } else {
                        // Favorite contacts section
                        if (favoriteContacts.isNotEmpty()) {
                            item {
                                ContactSection(
                                    title = "Favorites",
                                    contacts = favoriteContacts,
                                    onContactClick = { onNavigateToContactDetail(it.id) },
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }

                        // Recent contacts section
                        if (recentContacts.isNotEmpty()) {
                            item {
                                ContactSection(
                                    title = "Recent",
                                    contacts = recentContacts,
                                    onContactClick = { onNavigateToContactDetail(it.id) },
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }

                        // All contacts section
                        item {
                            Text(
                                text = "All Contacts",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                            )
                        }

                        items(filteredContacts) { contact ->
                            SwipeToDelete(
                                item = contact,
                                onDelete = { viewModel.deleteContact(it.id) }
                            ) { swipeContact ->
                                ContactListItem(
                                    contact = swipeContact,
                                    onClick = { onNavigateToContactDetail(swipeContact.id) },
                                    onRemove = { viewModel.deleteContact(swipeContact.id) },
                                    onEdit = { onNavigateToContactDetail(swipeContact.id) },
                                    onMessage = { /* TODO: Navigate to chat */ },
                                    onToggleFavorite = {
                                        if (swipeContact.isFavorite()) {
                                            viewModel.removeFromFavorites(swipeContact.id)
                                        } else {
                                            viewModel.addToFavorites(swipeContact.id)
                                        }
                                    },
                                    onToggleBlocked = {
                                        if (swipeContact.isBlocked()) {
                                            viewModel.unblockContact(swipeContact.id)
                                        } else {
                                            viewModel.blockContact(swipeContact.id)
                                        }
                                    },
                                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }

            if (contactsState.properties.isLoading && contacts.isEmpty()) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}
