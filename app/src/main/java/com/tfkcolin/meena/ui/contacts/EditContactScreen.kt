package com.tfkcolin.meena.ui.contacts

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.meena.ui.components.MeenaPrimaryButton
import com.tfkcolin.meena.ui.components.MeenaTextField
import com.tfkcolin.meena.ui.components.MeenaTopBar
import com.tfkcolin.meena.ui.contacts.components.RelationshipDropdown

/**
 * Edit contact screen.
 *
 * @param contactId The ID of the contact to edit.
 * @param onNavigateBack Navigate back to the contact detail screen.
 * @param viewModel The contacts view model.
 */
@Composable
fun EditContactScreen(
    contactId: String,
    onNavigateBack: () -> Unit,
    viewModel: ContactsViewModel = hiltViewModel()
) {
    val contactsState by viewModel.contactsState.collectAsState()
    val contacts by viewModel.contacts.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val focusManager = LocalFocusManager.current

    // Find the contact with the given ID
    val contact = contacts.find { it.id == contactId }

    var displayName by remember { mutableStateOf(contact?.displayName ?: "") }
    var notes by remember { mutableStateOf(contact?.notes ?: "") }
    var relationship by remember { mutableStateOf(contact?.relationship ?: "friend") }
    var isRelationshipMenuExpanded by remember { mutableStateOf(false) }

    val relationships = listOf(
        "friend" to "Friend",
        "family" to "Family",
        "colleague" to "Colleague",
        "acquaintance" to "Acquaintance"
    )

    /**
     * Update the contact.
     */
    fun updateContact() {
        viewModel.updateContact(
            contactId = contactId,
            displayName = displayName,
            notes = if (notes.isBlank()) null else notes,
            relationship = relationship
        )
    }

    // Check if contact was updated successfully
    LaunchedEffect(contactsState.updateContactOperation.isSuccessful) {
        if (contactsState.updateContactOperation.isSuccessful) {
            viewModel.resetContactOperationStates()
            onNavigateBack()
        }
    }

    // Show error message
    LaunchedEffect(contactsState.error) {
        contactsState.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.clearError()
        }
    }

    // Show operation error message
    LaunchedEffect(contactsState.updateContactOperation.error) {
        contactsState.updateContactOperation.error?.let {
            snackbarHostState.showSnackbar(it)
            viewModel.resetContactOperationStates()
        }
    }

    Scaffold(
        topBar = {
            MeenaTopBar(
                title = "Edit Contact",
                onBackClick = onNavigateBack
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (contact == null) {
                // Contact not found
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "Contact not found",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    MeenaPrimaryButton(
                        text = "Go Back",
                        onClick = onNavigateBack
                    )
                }
            } else {
                // Edit contact form
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState()),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Top
                ) {
                    Text(
                        text = "Edit Contact",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Update the information for this contact.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(32.dp))

                    MeenaTextField(
                        value = displayName,
                        onValueChange = { displayName = it },
                        label = "Display Name",
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Next
                        ),
                        keyboardActions = KeyboardActions(
                            onNext = { focusManager.moveFocus(FocusDirection.Down) }
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Relationship dropdown
                    RelationshipDropdown(
                        selectedRelationship = relationship,
                        onRelationshipSelected = { relationship = it },
                        relationships = relationships,
                        isExpanded = isRelationshipMenuExpanded,
                        onExpandedChange = { isRelationshipMenuExpanded = it },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Notes field
                    MeenaTextField(
                        value = notes,
                        onValueChange = { notes = it },
                        label = "Notes (Optional)",
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            capitalization = KeyboardCapitalization.Sentences,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                focusManager.clearFocus()
                                if (displayName.isNotBlank()) {
                                    updateContact()
                                }
                            }
                        ),
                        singleLine = false,
                        maxLines = 5
                    )

                    Spacer(modifier = Modifier.height(32.dp))

                    MeenaPrimaryButton(
                        text = "Save Changes",
                        onClick = { updateContact() },
                        enabled = !contactsState.properties.isLoading &&
                                  !contactsState.updateContactOperation.isInProgress &&
                                  displayName.isNotBlank(),
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Add some space at the bottom for scrolling
                    Spacer(modifier = Modifier.height(32.dp))
                }
            }

            if (contactsState.properties.isLoading || contactsState.updateContactOperation.isInProgress) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

