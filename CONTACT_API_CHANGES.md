# Contact API Changes

This document describes the changes made to the Contact API to use database functions and add a new endpoint for favorite contacts.

## Changes Made

1. **Database Functions**:
   - Added `get_contacts(user_id, limit, offset)` function to get all contacts for a user
   - Added `get_favorite_contacts(user_id, limit, offset)` function to get favorite contacts for a user
   - Added `get_contact_by_id(user_id, contact_id)` function to get a specific contact by ID

2. **Backend Code**:
   - Updated `ContactRepository` to use the database functions
   - Added `GetFavoriteContacts` method to `ContactService`
   - Added `GetFavoriteContacts` handler to `ContactHandler`
   - Updated router to add the new endpoint

3. **Android App**:
   - Added a new endpoint to the `ContactApi` interface

## New Endpoint

A new endpoint has been added to get favorite contacts:

```
GET /api/v1/contacts/favorites
```

**Parameters**:
- `limit` (optional): Maximum number of contacts to return (default: 50)
- `offset` (optional): Number of contacts to skip for pagination (default: 0)

**Response**:
```json
{
  "contacts": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "contact_id": "uuid",
      "display_name": "<PERSON>",
      "user_handle": "johndoe",
      "avatar_url": "https://example.com/avatar.jpg",
      "relationship": "friend",
      "last_active": "2025-05-07T12:00:00Z",
      "is_online": true
    },
    ...
  ],
  "total_count": 10
}
```

## How to Apply the Changes

1. **Deploy the Backend Code**:
   - Build and deploy the backend code to Railway

2. **Apply Database Changes**:
   - Option 1: Reset the entire database (recommended for development)
     - Run `./scripts/initialize_database.sh`
   - Option 2: Apply only the database functions (preserves existing data)
     - Run `./scripts/apply_functions.sh`

See the `deploy_changes.sh` script for detailed instructions.

## Benefits

These changes provide several benefits:

1. **Improved Maintainability**: All contact queries are now defined in the database, making them easier to maintain and update.

2. **Consistent Behavior**: All contact queries now use the same logic, ensuring consistent behavior across the application.

3. **Better Performance**: The database functions can be optimized at the database level, potentially improving performance.

4. **Enhanced Functionality**: We've added a new endpoint for getting favorite contacts, which wasn't directly exposed before.

## Future Improvements

See the [Database TODO List](docs/database/TODO.md) for planned improvements to the database functions.
